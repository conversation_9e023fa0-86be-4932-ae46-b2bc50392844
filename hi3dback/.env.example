APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost
FRONTEND_URL=http://localhost:3000

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Configuration pour les e-mails
# Option 1: Utiliser le pilote log (les e-mails sont écrits dans les logs)
MAIL_MAILER=log
MAIL_HOST=localhost
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Option 2: Utiliser Mailtrap pour tester les e-mails
# MAIL_MAILER=smtp
# MAIL_HOST=sandbox.smtp.mailtrap.io
# MAIL_PORT=2525
# MAIL_USERNAME=votre_username_mailtrap
# MAIL_PASSWORD=votre_password_mailtrap
# MAIL_ENCRYPTION=tls
# MAIL_FROM_ADDRESS="<EMAIL>"
# MAIL_FROM_NAME="${APP_NAME}"

# Option 3: Utiliser Gmail
# MAIL_MAILER=smtp
# MAIL_HOST=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=votre_mot_de_passe_d_application
# MAIL_ENCRYPTION=tls
# MAIL_FROM_ADDRESS="<EMAIL>"
# MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Configuration personnalisée
# Définir sur true pour sauter la vérification d'e-mail (utile en développement)
SKIP_EMAIL_VERIFICATION=false
# URL de l'application frontend
FRONTEND_URL=http://localhost:3000

# Configuration du système de fichiers
# Taille limite pour le stockage local (en MB)
FILE_LOCAL_STORAGE_LIMIT=10
# Taille maximale d'upload (en MB)
FILE_MAX_UPLOAD_SIZE=500
# Types MIME autorisés (séparés par des virgules)
FILE_ALLOWED_MIME_TYPES="image/jpeg,image/png,image/gif,image/webp,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/zip,application/x-rar-compressed,text/plain"

# Configuration SwissTransfer
SWISSTRANSFER_ENABLED=true
SWISSTRANSFER_BASE_URL=https://www.swisstransfer.com
SWISSTRANSFER_API_URL=https://www.swisstransfer.com/api
SWISSTRANSFER_MAX_FILE_SIZE=50000
SWISSTRANSFER_TIMEOUT=300
