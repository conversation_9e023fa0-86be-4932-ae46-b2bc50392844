{"info": {"_postman_id": "gmail-auth-api-collection", "name": "Gmail Authentication API", "description": "Collection pour tester l'API d'authentification Gmail OAuth", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. Vérifier le statut de configuration Gmail", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has configured field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('configured');", "});", "", "if (pm.response.json().configured) {", "    pm.test(\"Configuration is complete\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData.configuration.is_complete).to.be.true;", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/gmail/status", "host": ["{{base_url}}"], "path": ["api", "auth", "gmail", "status"]}, "description": "Vérifier si la configuration Gmail OAuth est disponible et complète"}, "response": []}, {"name": "2. <PERSON><PERSON>enir l'URL de redirection Gmail", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200 or 500\", function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 500]);", "});", "", "if (pm.response.code === 200) {", "    pm.test(\"Response has redirect_url\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('redirect_url');", "        pm.expect(jsonData.redirect_url).to.include('accounts.google.com');", "    });", "    ", "    // Sauvegarder l'URL pour usage manuel", "    var jsonData = pm.response.json();", "    if (jsonData.redirect_url) {", "        pm.globals.set('gmail_redirect_url', jsonData.redirect_url);", "        console.log('URL de redirection Gmail:', jsonData.redirect_url);", "    }", "} else {", "    pm.test(\"Error message is present\", function () {", "        var jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('message');", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/gmail/redirect", "host": ["{{base_url}}"], "path": ["api", "auth", "gmail", "redirect"]}, "description": "Obtenir l'URL de redirection vers Google OAuth. Cette URL doit être ouverte dans un navigateur pour l'authentification."}, "response": []}, {"name": "3. <PERSON><PERSON><PERSON> le callback Gmail (avec code fictif)", "event": [{"listen": "test", "script": {"exec": ["// Ce test échouera avec un code fictif, c'est normal", "pm.test(\"Response is JSON\", function () {", "    pm.response.to.be.json;", "});", "", "pm.test(\"Response has success field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/auth/gmail/callback?code=fake_code_for_testing&state=test", "host": ["{{base_url}}"], "path": ["api", "auth", "gmail", "callback"], "query": [{"key": "code", "value": "fake_code_for_testing", "description": "Code d'autorisation fictif (remplacer par un vrai code de Google)"}, {"key": "state", "value": "test", "description": "État de sécurité (optionnel)"}]}, "description": "Simuler le callback de Google. En réalité, cette URL est appelée par Google avec un vrai code d'autorisation."}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Script exécuté avant chaque requête", "console.log('Testing Gmail Auth API...');"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Script exécuté après chaque requête", "console.log('Response status:', pm.response.code);"]}}], "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string", "description": "URL de base de l'API"}]}