APP_NAME=Laravel
APP_ENV=testing
APP_KEY=base64:KkMI1g8FX/V30VJ2u56tfLI+bptZslwoKWr8RAyTxLw=
APP_DEBUG=true
APP_TIMEZONE=UTC
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

# Base de données de test (SQLite en mémoire pour les tests)
DB_CONNECTION=sqlite
DB_DATABASE=:memory:

# Cache pour les tests
CACHE_DRIVER=array
CACHE_PREFIX=

# Session pour les tests
SESSION_DRIVER=array
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# Queue pour les tests
QUEUE_CONNECTION=sync

# Mail pour les tests
MAIL_MAILER=array
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Scout - Désactiver Meilisearch pour les tests
SCOUT_DRIVER=collection

# Logs
LOG_CHANNEL=single
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Broadcasting
BROADCAST_CONNECTION=log
BROADCAST_DRIVER=log

# Filesystem
FILESYSTEM_DISK=local

# Sanctum
SANCTUM_STATEFUL_DOMAINS=localhost,localhost:3000,127.0.0.1,127.0.0.1:8000,::1

# Autres configurations de test
BCRYPT_ROUNDS=4
