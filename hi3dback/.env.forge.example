# Configuration d'environnement pour Laravel Forge
# Copiez ce fichier et adaptez les valeurs pour votre environnement de production

APP_NAME="Hi3D Backend"
APP_ENV=production
APP_KEY=base64:YOUR_PRODUCTION_APP_KEY_HERE
APP_DEBUG=false
APP_URL=https://your-production-domain.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Base de données de production
DB_CONNECTION=pgsql
DB_HOST=127.0.0.1
DB_PORT=5432
DB_DATABASE=hi3d_production
DB_USERNAME=forge
DB_PASSWORD=your_secure_database_password

# Cache et sessions pour la production
BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=s3
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

# Redis pour cache et queues
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your_redis_password
REDIS_PORT=6379

# Configuration e-mail de production
MAIL_MAILER=smtp
MAIL_HOST=smtp.your-provider.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS S3 pour le stockage de fichiers
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=eu-west-1
AWS_BUCKET=your-s3-bucket-name
AWS_USE_PATH_STYLE_ENDPOINT=false

# Configuration Scout et Meilisearch (OBLIGATOIRE pour l'automatisation)
SCOUT_DRIVER=meilisearch
MEILISEARCH_HOST=https://your-meilisearch-instance.com
MEILISEARCH_KEY=your_meilisearch_master_key

# Configuration personnalisée
SKIP_EMAIL_VERIFICATION=false
FRONTEND_URL=https://your-frontend-domain.com

# Configuration du système de fichiers pour la production
FILE_LOCAL_STORAGE_LIMIT=50
FILE_MAX_UPLOAD_SIZE=500
FILE_ALLOWED_MIME_TYPES="image/jpeg,image/png,image/gif,image/webp,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/zip,application/x-rar-compressed,text/plain"

# Configuration SwissTransfer
SWISSTRANSFER_ENABLED=true
SWISSTRANSFER_BASE_URL=https://www.swisstransfer.com
SWISSTRANSFER_API_URL=https://www.swisstransfer.com/api
SWISSTRANSFER_MAX_FILE_SIZE=50000
SWISSTRANSFER_TIMEOUT=300

# Configuration pour l'automatisation Forge (OPTIONNEL)
FORGE_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
FORGE_NOTIFICATION_CHANNEL="#deployments"

# Configuration des performances Meilisearch
SCOUT_CHUNK_SIZE=500
MEILISEARCH_TIMEOUT=30
MEILISEARCH_MAX_RETRIES=3

# Configuration des queues pour l'indexation
INDEXATION_QUEUE=indexation
INDEXATION_TIMEOUT=600
INDEXATION_MAX_TRIES=3
