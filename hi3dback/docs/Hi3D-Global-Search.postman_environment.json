{"id": "hi3d-global-search-env", "name": "Hi3D Global Search Environment", "values": [{"key": "base_url", "value": "http://localhost:8000/api", "description": "Base URL for the Hi3D API", "enabled": true}, {"key": "auth_token", "value": "", "description": "Sanctum authentication token (set after login)", "enabled": true}, {"key": "search_query", "value": "<PERSON><PERSON>", "description": "Default search query for testing", "enabled": true}, {"key": "suggestion_query", "value": "<PERSON>r", "description": "Query for testing suggestions (shorter)", "enabled": true}, {"key": "test_city", "value": "Paris", "description": "City for testing location filters", "enabled": true}, {"key": "test_rating", "value": "4.5", "description": "Minimum rating for testing rating filters", "enabled": true}, {"key": "test_price", "value": "3000", "description": "Maximum price for testing price filters", "enabled": true}, {"key": "test_category", "value": "<PERSON><PERSON>", "description": "Category for testing category filters", "enabled": true}, {"key": "pagination_size", "value": "10", "description": "Number of results per page", "enabled": true}, {"key": "suggestion_limit", "value": "5", "description": "Maximum number of suggestions to return", "enabled": true}, {"key": "production_url", "value": "https://your-production-domain.com/api", "description": "Production API URL (update with your domain)", "enabled": false}, {"key": "staging_url", "value": "https://staging.your-domain.com/api", "description": "Staging API URL (update with your domain)", "enabled": false}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-07-08T19:30:00.000Z", "_postman_exported_using": "Postman/10.0.0"}