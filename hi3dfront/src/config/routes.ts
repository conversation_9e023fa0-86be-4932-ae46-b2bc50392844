// Configuration des routes de l'application

// Routes principales
export const ROUTES = {
  // Pages publiques
  HOME: '/',
  ABOUT: '/about',
  OFFERS: '/offers',
  EXPLORE: '/explore',
  CONTACT: '/contact',

  // Authentification
  REGISTER: '/register',
  LOGIN: '/login',
  SIMPLE_LOGIN: '/simple-login',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password',

  // Profil
  PROFILE: '/profile',
  EDIT_PROFILE: '/profile/edit',

  // Projets et offres
  PROJECTS: '/projects',
  PROJECT_DETAILS: (id: string | number) => `/projects/${id}`,
  PROJECT_APPLY: (id: string | number) => `/projects/${id}/apply`,
  OFFER_DETAILS: (id: string | number) => `/offre/${id}`,
  INDEPENDANTS_LIST: '/lists-independants',

  // Tableau de bord
  DASHBOARD: '/dashboard',
  DASHBOARD_PROFILE: '/dashboard/profile',
  DASHBOARD_EDIT_PROFILE: '/dashboard/profile/edit',
  DASHBOARD_CLIENT_PROFILE: '/dashboard/client-profile',
  DASHBOARD_CLIENT_PROFILE_EDIT: '/dashboard/client-profile/edit',
  DASHBOARD_CREATE_PROJECT: '/dashboard/create-project',
  DASHBOARD_PROJECTS: '/dashboard/projects',
  DASHBOARD_PROJECT_DETAILS: (id: string | number) => `/dashboard/projects/${id}`,
  DASHBOARD_SERVICES: '/dashboard/services',
  DASHBOARD_SERVICE_DETAILS: (id: string | number) => `/dashboard/services/${id}`,

  // Notifications et messages
  NOTIFICATIONS: '/notifications',
  DISCUSSIONS: '/discussions',
  DISCUSSION_DETAILS: (id: string | number) => `/discussions/${id}`,

  // Autres routes
  ALL_PROJECTS: '/all-projects',
  ALL_ACTIVITIES: '/all-activities',
  ALL_PROFESSIONALS: '/all-professionals',
  INVOICES: '/invoices',
};

// Routes API
export const API_ROUTES = {
  // Authentification
  REGISTER: '/api/register',
  LOGIN: '/api/login',
  LOGOUT: '/api/logout',
  FORGOT_PASSWORD: '/api/password/forgot',
  RESET_PASSWORD: '/api/password/reset',
  VERIFY_EMAIL: (id: string | number, hash: string) => `/api/email/verify/${id}/${hash}`,
  RESEND_VERIFICATION: '/api/email/verify/resend',
  USER: '/api/user',

  // Profil
  PROFILE: '/api/profile',
  COMPLETE_PROFILE: '/api/profile/complete',
  PROFILE_COMPLETION: '/api/profile/completion',
  UPLOAD_AVATAR: '/api/profile/avatar',
  UPLOAD_PORTFOLIO: '/api/profile/portfolio',
  DELETE_PORTFOLIO: (id: string | number) => `/api/profile/portfolio/${id}`,
  UPDATE_AVAILABILITY: '/api/profile/availability',

  // Expériences et réalisations
  EXPERIENCES: '/api/experiences',
  EXPERIENCE_DETAILS: (id: string | number) => `/api/experiences/${id}`,
  EXPERIENCE_PROJECTS: (experienceId: string | number) => `/api/experiences/${experienceId}/projects`,
  ACHIEVEMENTS: '/api/achievements',
  ACHIEVEMENT_DETAILS: (id: string | number) => `/api/achievements/${id}`,

  // Projets
  PROJECTS: '/api/projects',
  PROJECT_DETAILS: (id: string | number) => `/api/projects/${id}`,

  // Offres
  OPEN_OFFERS: '/api/open-offers',
  OPEN_OFFER_DETAILS: (id: string | number) => `/api/open-offers/${id}`,
  OPEN_OFFER_APPLY: (id: string | number) => `/api/open-offers/${id}/apply`,
  OPEN_OFFER_APPLICATIONS: (id: string | number) => `/api/open-offers/${id}/applications`,
  UPDATE_APPLICATION_STATUS: (id: string | number) => `/api/offer-applications/${id}/status`,
  CLOSE_OFFER: (id: string | number) => `/api/open-offers/${id}/close`,
  COMPLETE_OFFER: (id: string | number) => `/api/open-offers/${id}/complete`,
  REJECT_OFFER: (id: string | number) => `/api/open-offers/${id}/reject`,
  INVITE_PROFESSIONAL: (id: string | number) => `/api/open-offers/${id}/invite`,
  CLIENT_OPEN_OFFERS: '/api/client/open-offers',
  CLIENT_CLOSED_COMPLETED_OFFERS: '/api/client/closed-completed-offers',

  // Messages
  OFFER_MESSAGES: (id: string | number) => `/api/open-offers/${id}/messages`,

  // Professionnels
  PROFESSIONALS: '/api/professionals',
  PROFESSIONALS_AVAILABILITY: '/api/professionals/availability',
  PROFESSIONAL_DETAILS: (id: string | number) => `/api/professionals/${id}`,
  PROFESSIONAL_OFFERS: (id: string | number) => `/api/professionals/${id}/offers`,

  // Services
  SERVICE_OFFERS: '/api/service-offers',
  SERVICE_OFFER_DETAILS: (id: string | number) => `/api/service-offers/${id}`,

  // Tableau de bord
  DASHBOARD: '/api/dashboard',
  DASHBOARD_PROJECTS: '/api/dashboard/projects',
  DASHBOARD_PROJECT_DETAILS: (id: string | number) => `/api/dashboard/projects/${id}`,
  DASHBOARD_PROJECT_REMOVE_ATTACHMENT: (projectId: string | number, attachmentIndex: number) =>
    `/api/dashboard/projects/${projectId}/attachments/${attachmentIndex}`,

  // Contacts
  CONTACTS: '/api/contacts',
  CONTACT_DETAILS: (id: string | number) => `/api/contacts/${id}`,

  // Utilitaires
  PING: '/api/ping',
  HEALTH_CHECK: '/api/health-check',
};
