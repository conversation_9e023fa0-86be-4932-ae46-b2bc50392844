@media (max-width: 767px) {
  .mobile-two-columns {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
  }

  .mobile-info-column {
    width: 55%;
    padding-right: 12px;
  }

  .mobile-info-column h3 {
    font-size: 16px !important;
    margin-bottom: 6px;
  }

  .mobile-info-column p {
    font-size: 12px !important;
    line-height: 1.3;
    margin-bottom: 4px;
  }

  .mobile-buttons-column {
    width: 45%;
  }

  .mobile-buttons-column button {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
    font-size: 13px !important;
    border-radius: 20px !important;
  }
}

@media (min-width: 768px) {
  .mobile-two-columns {
    display: flex;
    flex-direction: column;
  }

  .mobile-info-column,
  .mobile-buttons-column {
    width: 100%;
  }
}