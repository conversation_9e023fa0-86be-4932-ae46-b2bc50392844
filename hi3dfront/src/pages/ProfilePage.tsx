import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Award,
  Edit,
  Calendar,
  Globe,
  Clock,
  ChevronRight,
  Star,
  Download
} from 'lucide-react';
import { API_BASE_URL } from '../config';
import { mockProfileData } from '../mocks/profileData';

interface ProfileData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  skills: string[];
  avatar: string;
  portfolio: Array<{
    path: string;
    name: string;
    type: string;
  }>;
  title?: string;
  rating?: number;
  hourly_rate?: number;
  availability_status?: string;
  experience?: number;
  completion_percentage: number;
}

const ProfilePage: React.FC = () => {
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');
  const navigate = useNavigate();

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          navigate('/login');
          return;
        }

        const response = await fetch(`${API_BASE_URL}/api/profile/completion`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch profile data');
        }

        const data = await response.json();
        console.log('Profile data from API:', data);
        setProfileData(data.profile_data);
      } catch (err) {
        console.error('Error fetching profile data:', err);

        // Utiliser les données mockées en cas d'échec de l'API
        console.log('Using mock profile data instead');
        setProfileData(mockProfileData.profile_data);
        setError(null); // Effacer l'erreur puisque nous avons des données de secours
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [navigate]);

  const handleEditProfile = () => {
    navigate('/profile/edit');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      </div>
    );
  }

  if (!profileData) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg">
          No profile data available. Please complete your profile.
        </div>
      </div>
    );
  }

  const getAvatarUrl = () => {
    if (profileData.avatar) {
      if (profileData.avatar.startsWith('http')) {
        return profileData.avatar;
      }
      return `${API_BASE_URL}/api/storage/${profileData.avatar}`;
    }
    return 'https://via.placeholder.com/150';
  };

  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, index) => (
      <Star
        key={index}
        className={`h-5 w-5 ${index < Math.floor(rating) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
      />
    ));
  };

  return (
    <div className="bg-gray-50 min-h-screen pb-12">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white relative">
        {/* Bouton Edit Profile - Position absolue par rapport au header */}
        <div
          style={{
            position: 'absolute',
            top: '1rem',
            right: '1rem',
            zIndex: 50
          }}
        >
          <button
            type="button"
            onClick={handleEditProfile}
            style={{
              backgroundColor: '#ffffff',
              color: '#2980b9',
              padding: '0.75rem 1.5rem',
              borderRadius: '0.5rem',
              fontWeight: 'bold',
              fontSize: '1rem',
              border: '2px solid #2980b9',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            }}
          >
            <Edit style={{ marginRight: '0.5rem', width: '1.25rem', height: '1.25rem' }} />
            Modifier le profil
          </button>
        </div>
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center md:items-start gap-6">
            <div className="relative">
              <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-lg">
                <img
                  src={getAvatarUrl()}
                  alt={`${profileData.first_name} ${profileData.last_name}`}
                  className="w-full h-full object-cover"
                />
              </div>

            </div>
            <div className="text-center md:text-left">
              <h1 className="text-3xl font-bold">{profileData.first_name} {profileData.last_name}</h1>
              <p className="text-primary-100 mt-1">{profileData.title || 'Professional'}</p>
              <div className="flex items-center justify-center md:justify-start mt-2">
                {profileData.rating && renderStars(profileData.rating)}
                {profileData.rating && <span className="ml-2">{profileData.rating.toFixed(1)}</span>}
              </div>
              <div className="flex flex-wrap gap-4 mt-4 justify-center md:justify-start">
                <div className="flex items-center">
                  <Mail className="h-5 w-5 mr-2 text-primary-200" />
                  <span>{profileData.email}</span>
                </div>
                <div className="flex items-center">
                  <Phone className="h-5 w-5 mr-2 text-primary-200" />
                  <span>{profileData.phone}</span>
                </div>
                <div className="flex items-center">
                  <MapPin className="h-5 w-5 mr-2 text-primary-200" />
                  <span>{profileData.city}, {profileData.country}</span>
                </div>
              </div>
            </div>
            <div className="md:ml-auto flex flex-col items-center md:items-end mt-4 md:mt-0">

              <div className="bg-white text-primary-800 px-4 py-2 rounded-lg shadow-md">
                <div className="text-sm">Profile Completion</div>
                <div className="flex items-center mt-1">
                  <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-primary-600 rounded-full"
                      style={{ width: `${profileData.completion_percentage}%` }}
                    ></div>
                  </div>
                  <span className="ml-2 text-sm font-medium">{profileData.completion_percentage}%</span>
                </div>
              </div>
              {profileData.hourly_rate && (
                <div className="mt-4 bg-primary-700 px-4 py-2 rounded-lg shadow-md">
                  <div className="text-sm text-primary-100">Hourly Rate</div>
                  <div className="text-xl font-bold">${profileData.hourly_rate}/hr</div>
                </div>
              )}
              {profileData.availability_status && (
                <div className={`mt-4 px-4 py-2 rounded-lg shadow-md ${
                  profileData.availability_status === 'available'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    <span className="font-medium capitalize">{profileData.availability_status}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white shadow">
        <div className="container mx-auto px-4">
          <div className="flex overflow-x-auto">
            <button
              className={`px-4 py-4 font-medium text-sm border-b-2 transition-colors ${
                activeTab === 'overview'
                  ? 'border-primary-600 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('overview')}
            >
              Overview
            </button>
            <button
              className={`px-4 py-4 font-medium text-sm border-b-2 transition-colors ${
                activeTab === 'skills'
                  ? 'border-primary-600 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('skills')}
            >
              Skills
            </button>
            <button
              className={`px-4 py-4 font-medium text-sm border-b-2 transition-colors ${
                activeTab === 'portfolio'
                  ? 'border-primary-600 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => setActiveTab('portfolio')}
            >
              Portfolio
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* About Me */}
            <div className="md:col-span-2">
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">About Me</h2>
                <p className="text-gray-600 whitespace-pre-line">
                  {profileData.bio || 'No bio information available.'}
                </p>
              </div>

              {/* Experience */}
              {profileData.experience && (
                <div className="bg-white rounded-xl shadow-sm p-6 mt-6">
                  <h2 className="text-xl font-semibold text-gray-800 mb-4">Experience</h2>
                  <div className="flex items-center text-gray-600">
                    <Briefcase className="h-5 w-5 mr-3 text-primary-600" />
                    <span>{profileData.experience} years of experience</span>
                  </div>
                </div>
              )}
            </div>

            {/* Contact Information */}
            <div>
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-800 mb-4">Contact Information</h2>
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <Mail className="h-5 w-5 mr-3 text-primary-600 mt-0.5" />
                    <div>
                      <div className="text-sm text-gray-500">Email</div>
                      <div className="text-gray-700">{profileData.email}</div>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <Phone className="h-5 w-5 mr-3 text-primary-600 mt-0.5" />
                    <div>
                      <div className="text-sm text-gray-500">Phone</div>
                      <div className="text-gray-700">{profileData.phone}</div>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <MapPin className="h-5 w-5 mr-3 text-primary-600 mt-0.5" />
                    <div>
                      <div className="text-sm text-gray-500">Address</div>
                      <div className="text-gray-700">{profileData.address}</div>
                      <div className="text-gray-700">{profileData.city}, {profileData.country}</div>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'skills' && (
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Skills & Expertise</h2>
            {profileData.skills && profileData.skills.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {profileData.skills.map((skill, index) => (
                  <span
                    key={index}
                    className="bg-primary-50 text-primary-700 px-3 py-1 rounded-full text-sm font-medium"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No skills listed yet.</p>
            )}
          </div>
        )}

        {activeTab === 'portfolio' && (
          <div className="bg-white rounded-xl shadow-sm p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-6">Portfolio</h2>
            {profileData.portfolio && profileData.portfolio.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                {profileData.portfolio.map((item, index) => (
                  <div key={index} className="group relative">
                    <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 border border-gray-200">
                      {item.type.startsWith('image/') ? (
                        <img
                          src={`${API_BASE_URL}/api/storage/${item.path}`}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex flex-col items-center justify-center p-4">
                          <div className="text-4xl text-gray-400 mb-2">
                            {item.type.includes('pdf') ? '📄' : '📁'}
                          </div>
                          <div className="text-sm text-gray-600 text-center truncate w-full">
                            {item.name}
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 rounded-lg">
                      <a
                        href={`${API_BASE_URL}/api/storage/${item.path}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="bg-white text-primary-600 p-2 rounded-full shadow-lg hover:bg-gray-100 transition-colors"
                        download={item.name}
                      >
                        <Download className="h-5 w-5" />
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No portfolio items added yet.</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfilePage;
