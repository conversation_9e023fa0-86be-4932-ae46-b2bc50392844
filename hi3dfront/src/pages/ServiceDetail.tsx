import React, { useState, useEffect } from 'react';
import { useLocation } from "react-router-dom";
import { <PERSON>evronLeft, ChevronRight, Eye, Heart, MapPin, Star, Pin, Languages } from 'lucide-react';
import { Button } from '../components/ui/buttons';
import { Badge } from '../components/ui/badges';
import Header from '../components/Header';
import Footer from '../components/Footer';
import QuoteRequestModal from '../components/QuoteRequestModal';
import { useParams, useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';
import { getAllCategories } from '../data/categories'
import './ServiceDetail.css';


interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  is_professional: boolean;
}

// Interface pour les éléments du portfolio
interface PortfolioItem {
  id?: number;
  path?: string;
  name?: string;
  type?: string;
  created_at?: string;
  // description?: string;
}

interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  phone: string;
  email?: string;
  address: string;
  city: string;
  country: string;
  skills: string[] | null;
  languages: string[] | null;
  availability_status: string | null;
  services_offered: string[];
  hourly_rate: string;
  completion_percentage: number;
  created_at: string;
  updated_at: string;
  avatar?: string;
  cover_photo?: string;
  profile_picture_path?: string;
  rating?: number;
  review_count?: number;
  bio?: string;
  title?: string;
  portfolio?: PortfolioItem[];
  user: User;
  likes_count?: number;
  views_count?: number;
}

interface Achievement {
  id: number;
  freelance_profile_id: number;
  title: string;
  organization: string;
  date_obtained: string;
  description: string;
  file_path: string | null;
  achievement_url: string | null;
  created_at: string;
  updated_at: string;
}

const ServiceDetail = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showQuoteModal, setShowQuoteModal] = useState(false);
  const { state } = useLocation();
  const project = state?.project || state?.service;
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [images, setImages] = useState([
    project?.image_url || "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=600&auto=format&fit=crop",
    ...(project?.file_urls?.length ? project.file_urls : []),
    // "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=600&auto=format&fit=crop"
  ]);
  const [professional, setProfessional] = useState<FreelanceProfile | null>(null);
  const [error, setError] = useState<string | null>(null);

  const [offers, setOffers] = useState([]);
  const [selectedOfferId, setSelectedOfferId] = useState(null);
  const [showModal, setShowModal] = useState(false);

  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [projects, setProject] = useState<any | []>([]);
  const [services, setService] = useState<any | []>([]);
  const [serviceDetails, setServiceDetails] = useState<any | null>(null);
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  // Fonction pour formater l'URL de l'image
  const getImageUrl = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  const getImageService = (imagePath: string | undefined, defaultImage: string = "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}/storage${imagePath}`;
    return `${API_BASE_URL}/storage/${imagePath}`;
  };

  // const getUrlProlfil = (path : string)  => {
  //     return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
  // };

  const getUrlProlfil = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  const recordView = async (serviceOfferId:any) => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/service-offers/${serviceOfferId}/view`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
        }
      );

      const data = await response.json();
      console.log("Réponse API :", data);

      if (data.success) {
        if (data.data.view_recorded) {
          console.log("Vue enregistrée avec succès ✅");
        } else {
          console.log("Vue déjà enregistrée pour cette session ⚠️");
        }
      } else {
        console.error("Erreur API :", data.message);
      }
    } catch (error) {
      console.error("Erreur lors de l'appel API :", error);
    }
  };

  useEffect(() => {
    // recordProfessionalView(id);
    fetchProfessional();
    // fetchClientPendingOffers();
    fetchAchievements();
    fetchServiceOffert();
    // Récupérer les détails complets du service
    if (project?.id) {
      fetchServiceDetails(project.id);
    }
  }, [project?.user_id, project?.professional_id, project?.id]);

  useEffect(() => {
    if(project?.id && project?.price != null && project?.price != 0){
    recordView(project?.id);
    }
  }, [project?.id, project?.price]);

  // Debug: Afficher les données
  useEffect(() => {
    console.log("Project data:", project);
    console.log("Service details:", serviceDetails);
  }, [project, serviceDetails]);


  const fetchProfessional = async () => {
    try {
      setLoading(true);
      // Essayer d'abord l'endpoint professionals
      let response = await fetch(`${API_BASE_URL}/api/professionals/${project?.professional_id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      // Si l'endpoint professionals échoue, essayer l'endpoint users
      if (!response.ok) {
        console.log('Endpoint professionals a échoué, essai de l\'endpoint users');
        response = await fetch(`${API_BASE_URL}/api/users/${project?.professional_id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        });
      }

      if (!response.ok) {
        throw new Error('Impossible de récupérer les détails du professionnel');
      }

      const data = await response.json();
      console.log('Données du professionnel récupérées:', data);

      // Traiter les données selon le format
      if (data.professional) {
        // Traiter les skills qui peuvent être une chaîne JSON ou un tableau
        let skills = [];
        if (data.professional.skills) {
          if (Array.isArray(data.professional.skills)) {
            skills = data.professional.skills;
          } else if (typeof data.professional.skills === 'string') {
            try {
              skills = JSON.parse(data.professional.skills);
            } catch (e) {
              skills = [data.professional.skills]; // Si ce n'est pas un JSON valide, le traiter comme une chaîne simple
            }
          }
        }

        // Traiter le portfolio qui peut être une chaîne JSON ou un tableau
        let portfolio = [];
        if (data.professional.portfolio) {
          if (Array.isArray(data.professional.portfolio)) {
            portfolio = data.professional.portfolio;
          } else if (typeof data.professional.portfolio === 'string') {
            try {
              portfolio = JSON.parse(data.professional.portfolio);
            } catch (e) {
              portfolio = []; // Si ce n'est pas un JSON valide, utiliser un tableau vide
            }
          }
        }

        // Mettre à jour les données du professionnel
        setProfessional({
          ...data.professional,
          skills: skills,
          portfolio: portfolio.length > 0 ? portfolio : [
            { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
            { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
            { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
          ],
          likes_count: data.professional.likes_count,
          views_count: data.professional.views_count,
        });
      } else if (data.user && data.profile_data) {
        // Traiter le portfolio qui peut être une chaîne JSON ou un tableau
        let portfolio = [];
        if (data.profile_data.portfolio) {
          if (Array.isArray(data.profile_data.portfolio)) {
            portfolio = data.profile_data.portfolio;
          } else if (typeof data.profile_data.portfolio === 'string') {
            try {
              portfolio = JSON.parse(data.profile_data.portfolio);
            } catch (e) {
              portfolio = []; // Si ce n'est pas un JSON valide, utiliser un tableau vide
            }
          }
        }

        setProfessional({
          ...data.profile_data,
          user: data.user,
          portfolio: portfolio.length > 0 ? portfolio : [
            { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
            { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
            { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
          ],
          likes_count: data.user.likes_count || 0,
          views_count: data.user.views_count || 0,
        });
      } else if (data.user) {
        setProfessional({
          id: data.user.id,
          user_id: data.user.id,
          first_name: data.user.first_name,
          last_name: data.user.last_name,
          phone: '',
          address: '',
          city: data.user.city || 'Paris',
          country: data.user.country || 'France',
          skills: data.user.skills || ['Animation 3D', 'Modélisation 3D', 'Rigging'],
          languages: ['Français', 'Anglais'],
          availability_status: data.user.availability_status || 'available',
          services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
          hourly_rate: data.user.hourly_rate || '45',
          completion_percentage: data.completion_percentage || 100,
          created_at: data.user.created_at,
          updated_at: data.user.updated_at,
          avatar: data.user.avatar,
          profile_picture_path: data.user.profile_picture_path,
          rating: data.user.rating || 4.8,
          review_count: data.user.review_count || 27,
          bio: data.user.bio || 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
          title: data.user.title || 'Artiste 3D',
          portfolio: data.user.portfolio || [
            { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
            { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
            { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
          ],
          likes_count: data.user.likes_count || 0,
          views_count: data.user.views_count || 0,
          user: data.user,
        });
      } else {
        // Utiliser des données de démonstration
        setProfessional({
          id: parseInt(project.professional_id || '1'),
          user_id: parseInt(project.user_id || '1'),
          first_name: 'Thomas',
          last_name: 'Martin',
          phone: '+33 6 12 34 56 78',
          address: '123 Rue de la Création',
          city: 'Paris',
          country: 'France',
          skills: ['Animation 3D', 'Modélisation 3D', 'Rigging', 'Texturing', 'Rendu 3D'],
          languages: ['Français', 'Anglais'],
          availability_status: 'available',
          services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
          hourly_rate: '45',
          completion_percentage: 100,
          created_at: '2023-01-01',
          updated_at: '2023-01-01',
          avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          profile_picture_path: 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
          rating: 4.8,
          review_count: 27,
          portfolio: [
            { name: 'Projet 3D 1', path: 'https://picsum.photos/seed/101/300/200', type: 'Modélisation 3D pour un jeu vidéo' },
            { name: 'Animation 3D', path: 'https://picsum.photos/seed/102/300/200', type: 'Animation de personnage' },
            { name: 'Rendu architectural', path: 'https://picsum.photos/seed/103/300/200', type: 'Visualisation architecturale' },
          ],
          likes_count: 0,
          views_count: 0,
          bio: 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
          title: 'Artiste 3D',
          user: {
            id: parseInt(project.user_id || '1'),
            first_name: 'Thomas',
            last_name: 'Martin',
            email: '<EMAIL>',
            is_professional: true,
          },
        });
      }
    } catch (err) {
      console.error('Erreur lors de la récupération du professionnel:', err);
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Une erreur inconnue est survenue');
      }

      // Utiliser des données de démonstration en cas d'erreur
      setProfessional({
        id: parseInt(project.professional_id || '1'),
        user_id: parseInt(project.user_id || '1'),
        first_name: 'Thomas',
        last_name: 'Martin',
        phone: '+33 6 12 34 56 78',
        address: '123 Rue de la Création',
        city: 'Paris',
        country: 'France',
        skills: ['Animation 3D', 'Modélisation 3D', 'Rigging', 'Texturing', 'Rendu 3D'],
        languages: ['Français', 'Anglais'],
        availability_status: 'available',
        services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
        hourly_rate: '45',
        completion_percentage: 100,
        created_at: '2023-01-01',
        updated_at: '2023-01-01',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
        profile_picture_path: 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
        rating: 4.8,
        review_count: 27,
        portfolio: [
          { name: 'Projet 3D 1', path: 'https://picsum.photos/seed/101/300/200', type: 'Modélisation 3D pour un jeu vidéo' },
          { name: 'Animation 3D', path: 'https://picsum.photos/seed/102/300/200', type: 'Animation de personnage' },
          { name: 'Rendu architectural', path: 'https://picsum.photos/seed/103/300/200', type: 'Visualisation architecturale' },
        ],
        likes_count: 0,
        views_count: 0,
        bio: 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
        title: 'Artiste 3D',
        user: {
          id: parseInt(project.user_id || '1'),
          first_name: 'Thomas',
          last_name: 'Martin',
          email: '<EMAIL>',
          is_professional: true,
        },
      });
    } finally {
      setLoading(false);
    }
  };


  const fetchServiceDetails = async (serviceId: number) => {
    try {
      console.log("Récupération des détails du service ID:", serviceId);
      
      // D'abord, récupérer la liste des services
      const response = await fetch(`${API_BASE_URL}/api/professionals/${project?.user_id}/service-offers`);
      
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des services');
      }

      const services = await response.json();
      console.log("Liste des services récupérée:", services);
      
      // Trouver le service avec l'ID correspondant
      const serviceDetail = services.find((service: any) => service.id === serviceId);
      
      if (serviceDetail) {
        console.log("Service trouvé:", serviceDetail);
        setServiceDetails(serviceDetail);
      } else {
        console.log("Service non trouvé avec l'ID:", serviceId);
        // Essayer l'endpoint public comme fallback
        try {
          const publicResponse = await fetch(`${API_BASE_URL}/api/service-offers/${serviceId}/public`);
          if (publicResponse.ok) {
            const publicData = await publicResponse.json();
            console.log("Détails du service récupérés via endpoint public:", publicData);
            setServiceDetails(publicData);
          }
        } catch (publicError) {
          console.error("Erreur avec l'endpoint public:", publicError);
        }
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des détails du service:", error);
    }
  };

  const fetchServiceOffert = async () => {
    try {
      setLoading(true);

      const response = await fetch(`${API_BASE_URL}/api/professionals/${project?.user_id}/service-offers`);

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des réalisations');
      }

      const data = await response.json();

      console.log("data :", data);

      if (Array.isArray(data)) {
        const formatted = data.map((item) => ({
          id: item.id,
          title: item.title,
          description: item.description || 'Projet réalisé avec passion et expertise technique.',
          concepts: item.concepts,
          revisions: item.revisions,
          image_url: Array.isArray(item.files) && item.files.length > 0
            ? getImageService(item.files[0].path)//`${API_BASE_URL}/storage/${item.files[0].path}`
            : item.image ? getImageService(item.image) : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          file_urls: Array.isArray(item.files)
            ? item.files.map((file: any) => getImageService(file.path))//`${API_BASE_URL}/storage/${file.path}`)
            : [],
          category: item.categories ? item.categories.join(" - ") : "",
          client_name: item.execution_time,
          professional_name: item.user.first_name + ' ' + item.user.last_name, // Si tu n'as pas cette info dans l'API
          professional_id: item.user.id || 1,
          date_create: item.created_at,
          price: item.price,
          user_id: item.user.id,
          views: item.views,
          likes: item.likes,
          avatar: item.user.professional_details.avatar ? getUrlProlfil(String(item.user.professional_details.avatar)) : 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          // Ajouter les champs dynamiques
          what_you_get: item.what_you_get,
          who_is_this_for: item.who_is_this_for,
          delivery_method: item.delivery_method,
          why_choose_me: item.why_choose_me,
        }));

        console.log("Data Formater :", formatted);
        setService(formatted);
      } else {
        setService([]);
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des réalisations:", error);
      setError("Impossible de charger les réalisations. Veuillez réessayer plus tard.");
    } finally {
      setLoading(false);
    }
  };

  const fetchAchievements = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/professionals/${project?.professional_id}/achievements`);

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des réalisations');
      }

      const data = await response.json();

      if (data.success && data.achievements) {
        setAchievements(data.achievements);

        const formatted = data.achievements.map((proj: any) => ({
          id: proj.id,
          title: proj.title,
          description: proj.description || 'Projet réalisé avec passion et expertise technique.',
          image_url: proj.cover_photo
            ? getImageService(proj.cover_photo)//`${API_BASE_URL}/storage/${proj.cover_photo}`
            : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          file_urls: Array.isArray(proj.gallery_photos)
            ? proj.gallery_photos.map((file: any) => getImageService(file.path))//`${API_BASE_URL}/storage/${file.path}`)
            : [],
          category: proj.category,
          client_name: '',
          date_create: proj.created_at,
          professional_name: proj.professional.first_name + ' ' + proj.professional.last_name, // Si tu n'as pas cette info dans l'API
          professional_id: proj.professional_profile_id || 1,
          user_id: proj.professional.user_id,
          avatar: proj.professional.avatar ? getUrlProlfil(String(proj.professional.avatar)) : 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        }));

        console.log("Data Formater :", formatted);
        setProject(formatted);
      } else {
        setAchievements([]);
        setProject([]);
      }
    } catch (error) {
      console.error("Erreur lors de la récupération des réalisations:", error);
      setError("Impossible de charger les réalisations. Veuillez réessayer plus tard.");

    } finally {
      setLoading(false);
    }
  };


  const otherServices = [
    {
      id: 1,
      title: "Maquette orbital",
      price: "USD 3000",
      author: "Jack and Moris Render",
      isPro: true,
      views: 1,
      likes: 20,
      image: '/lovable-uploads/fcf0d7f7-bd2d-4bde-932f-ed180072b22f.png'
    },
    {
      id: 2,
      title: "360 visite tours",
      price: "USD 2000",
      author: "Mimi_360Pro",
      isPro: true,
      views: 1,
      likes: 20,
      image: '/lovable-uploads/9ca930a7-4f31-4860-acc7-6db4d02f1553.png'
    }
  ];


  const getCategoryLabel = (category: any) => {
    // Si c'est un chiffre, on cherche dans la liste
    if (!isNaN(category)) {
      const found = getAllCategories().find((item: any) => item.id === Number(category));
      return found ? found.label : category;
    }
    // Sinon on retourne directement la valeur (ex: texte personnalisé)
    return category || "SMARTEK";
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const [isScrolled, setIsScrolled] = useState(false);
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 100) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 2;



  const filteredServices = (services ?? []).filter(
    (item: any) => item.id !== project?.id
  );

  const totalPages = Math.ceil(filteredServices.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const currentItems = filteredServices.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  const filteredAchievements = (projects ?? []).filter(
    (item: any) => item.id !== project?.id
  );

  const totalPagesAChievement = Math.ceil(filteredAchievements.length / itemsPerPage);
  const startIndexAChievement = (currentPage - 1) * itemsPerPage;
  const currentItemsAchievement = filteredAchievements.slice(
    startIndexAChievement,
    startIndexAChievement + itemsPerPage
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <>
      <Header />

      <div className="bg-white w-full flex items-stretch">
        <div className="className={`w-full  px-[10px] md:px-[40px] mx-auto grid ${isLargeScreen ? 'grid-cols-2' : 'grid-cols-1'} gap-10 items-end`}">
          <div className="grid grid-cols-1 lg:grid-cols-7 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-5 lg:pr-8 lg:border-r border-black order-2 lg:order-1">
              {/* Hero Section */}
              <div className="mb-8">
                <h1 className="text-[34px] md:text-[36px] leading-tight tracking-tight font-semibold text-gray-900 mb-3"

                  style={{
                    fontSize: "28px",
                    fontFamily: "'Inter', sans-serif",
                    fontWeight: 500,
                    color: "#040404ff",
                  }}
                >
                  {project?.title || "3D Render Mockup Product For Your Brand Luqi Pamungkas"}
                </h1>

                {/* Image Carousel */}
                <div className="relative mb-6">
                  {/* Image */}
                  <div className="relative h-screen overflow-hidden">
                    <img
                      src={images[currentImageIndex]}
                      alt="Service showcase"
                      className="w-full h-full object-cover object-center"
                      draggable={false}

                      style={{
                        borderRadius: "18px"
                      }}
                    />

                    {/* Navigation Arrows (subtle chevrons) */}
                    <button
                      type="button"
                      onClick={prevImage}
                      aria-label="Previous image"
                      className="absolute left-3 md:left-4 top-1/2 -translate-y-1/2 p-1.5 md:p-2 text-gray-600 hover:text-gray-800 transition"
                    >
                      <ChevronLeft className="w-5 h-5 md:w-6 md:h-6" />
                    </button>

                    <button
                      type="button"
                      onClick={nextImage}
                      aria-label="Next image"
                      className="absolute right-3 md:right-4 top-1/2 -translate-y-1/2 p-1.5 md:p-2 text-gray-600 hover:text-gray-800 transition"
                    >
                      <ChevronRight className="w-5 h-5 md:w-6 md:h-6" />
                    </button>
                  </div>

                  {/* Image Indicators (small dots) */}
                  <div className="flex justify-center gap-2 md:gap-2.5 pt-4">
                    {images.map((_, index) => {
                      const active = index === currentImageIndex;
                      return (
                        <button
                          key={index}
                          type="button"
                          onClick={() => setCurrentImageIndex(index)}
                          aria-label={`Go to image ${index + 1}`}
                          className={`rounded-full transition-all ${active ? "w-2.5 h-2.5 bg-gray-800" : "w-2 h-2 bg-gray-300"
                            }`}
                        />
                      );
                    })}
                  </div>
                </div>
              </div>

              <div className="w-full bg-white">
                <div className="mx-auto max-w-7xl px-6 py-8 md:py-16">
                  {/* 2-column layout */}
                  <div className="w-full flex flex-col md:flex-row gap-12 md:gap-16">
                    {/* LEFT – About this Service */}
                    <section className="md:flex-[2] w-full">
                      <h2 className="text-3xl md:text-4xl tracking-tight mb-2"
                        style={{
                          fontSize: "32px",
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: 500,
                          color: "#040404ff",
                        }}

                      >
                        About this Service
                      </h2>
                      

                      <div className="space-y-6 text-gray-800">
                        <p className="leading-7"

                          style={{
                            fontSize: "16px",
                            fontFamily: "'Inter', sans-serif",
                            fontWeight: 400,
                            color: "#040404ff",

                          }}
                        >
                          <div dangerouslySetInnerHTML={{ __html: project?.description }} />
                        </p>

                        <div
                          style={{
                            fontSize: "16px",
                            fontFamily: "'Inter', sans-serif",
                            fontWeight: 400,
                            color: "#040404ff",

                          }}
                        >
                          {/* Tableau pour "What You Get" */}
                          <p

                            style={{
                              fontSize: "16px",
                              fontFamily: "'Inter', sans-serif",
                              fontWeight: 400,
                              color: "#040404ff",

                            }}
                          >What You Get:</p>
                          <div className="ml-4">
                            {serviceDetails?.what_you_get ? (
                              <div dangerouslySetInnerHTML={{ __html: serviceDetails.what_you_get.replace(/\n/g, '<br>') }} />
                            ) : (
                              <div className="text-gray-500 italic">
                                Aucune donnée disponible
                              </div>
                            )}
                          </div>

                          {/* Tableau pour "Why Choose Me" */}
                          <p
                            style={{
                              fontSize: "16px",
                              fontFamily: "'Inter', sans-serif",
                              fontWeight: 400,
                              color: "#040404ff",
                            }}
                          >Why Choose Me?</p>
                          <div className="ml-4">
                            {serviceDetails?.why_choose_me ? (
                              <div dangerouslySetInnerHTML={{ __html: serviceDetails.why_choose_me.replace(/\n/g, '<br>') }} />
                            ) : (
                              <div className="text-gray-500 italic">
                                Aucune donnée disponible
                              </div>
                            )}
                          </div>

                          {/* Tableau pour "Who is this for" */}
                          <p className="mt-4"
                            style={{
                              fontSize: "16px",
                              fontFamily: "'Inter', sans-serif",
                              fontWeight: 400,
                              color: "#040404ff",

                            }}
                          >Who is this for?</p>
                          <div className="ml-4">
                            {serviceDetails?.who_is_this_for ? (
                              <div dangerouslySetInnerHTML={{ __html: serviceDetails.who_is_this_for.replace(/\n/g, '<br>') }} />
                            ) : (
                              <div className="text-gray-500 italic">
                                Aucune donnée disponible
                              </div>
                            )}
                          </div>

                          {/* Tableau pour "Delivery Method" */}
                          <p
                            style={{
                              fontSize: "16px",
                              fontFamily: "'Inter', sans-serif",
                              fontWeight: 400,
                              color: "#040404ff",

                            }}
                          >Delivery Method:</p>
                          <div className="ml-4">
                            {serviceDetails?.delivery_method ? (
                              <div dangerouslySetInnerHTML={{ __html: serviceDetails.delivery_method.replace(/\n/g, '<br>') }} />
                            ) : (
                              <div className="text-gray-500 italic">
                                Aucune donnée disponible
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="pt-2">
                          <p
                            style={{
                              fontSize: "16px",
                              fontFamily: "'Inter', sans-serif",
                              fontWeight: 400,
                              color: "#040404ff",

                            }}
                          >
                            Ready to Elevate Your Products Look?
                          </p>
                          <p
                            style={{
                              fontSize: "16px",
                              fontFamily: "'Inter', sans-serif",
                              fontWeight: 400,
                              color: "#040404ff",

                            }}
                          >-Luqi</p>
                        </div>
                      </div>
                    </section>

                    {/* RIGHT – Details */}
                    <aside className="md:flex-[1] w-full">
                      <h2
                        style={{
                          fontSize: "32px",
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: 500,
                          color: "#000000",
                        }}
                      >
                        Details
                      </h2>

                      <div className="space-y-3">
                        <details className="group" open>
                          <summary className="flex items-center gap-2 cursor-pointer select-none py-2">
                            <svg aria-hidden="true" className="e-font-icon-svg e-fas-caret-right" viewBox="0 0 192 512" xmlns="http://www.w3.org/2000/svg" width="13" height="16" fill="#000000"><path d="M0 384.662V127.338c0-17.818 21.543-26.741 34.142-14.142l128.662 128.662c7.81 7.81 7.81 20.474 0 28.284L34.142 398.804C21.543 411.404 0 402.48 0 384.662z"></path></svg>
                            <span className="text-gray-900 font-medium" style={{ color: "#000000", fontSize: "16px", fontFamily: "'Inter', sans-serif", fontWeight: 400 }}>What You Get ?</span>
                          </summary>
                          <div className="pl-6 mt-2 text-gray-800" style={{ color: "#000000", fontSize: "16px", fontFamily: "'Inter', sans-serif", fontWeight: 400 }}>
                            {serviceDetails?.what_you_get ? (
                              <div dangerouslySetInnerHTML={{ __html: serviceDetails.what_you_get.replace(/\n/g, '<br>') }} />
                            ) : (
                              <div className="text-gray-500 italic">
                                Aucune donnée disponible
                              </div>
                            )}
                          </div>
                        </details>

                        <details className="group" open>
                          <summary className="flex items-center gap-2 cursor-pointer select-none py-2">
                            <svg aria-hidden="true" className="e-font-icon-svg e-fas-caret-right" viewBox="0 0 192 512" xmlns="http://www.w3.org/2000/svg" width="10" height="16" fill="#000000"><path d="M0 384.662V127.338c0-17.818 21.543-26.741 34.142-14.142l128.662 128.662c7.81 7.81 7.81 20.474 0 28.284L34.142 398.804C21.543 411.404 0 402.48 0 384.662z"></path></svg>
                            <span className="text-gray-900 font-medium" style={{ color: "#000000", fontSize: "16px", fontFamily: "'Inter', sans-serif", fontWeight: 400 }}>Who is this product for ?</span>
                          </summary>
                          <div className="pl-6 mt-2 text-gray-800 leading-7 md:text-[17px]" style={{ color: "#000000", fontSize: "16px", fontFamily: "'Inter', sans-serif", fontWeight: 400 }}>
                            {serviceDetails?.who_is_this_for ? (
                              <div dangerouslySetInnerHTML={{ __html: serviceDetails.who_is_this_for.replace(/\n/g, '<br>') }} />
                            ) : (
                              <div className="text-gray-500 italic">
                                Aucune donnée disponible
                              </div>
                            )}
                          </div>
                        </details>

                        <details className="group" open>
                          <summary className="flex items-center gap-2 cursor-pointer select-none py-2">
                            <svg aria-hidden="true" className="e-font-icon-svg e-fas-caret-right" viewBox="0 0 192 512" xmlns="http://www.w3.org/2000/svg" width="10" height="16" fill="#000000"><path d="M0 384.662V127.338c0-17.818 21.543-26.741 34.142-14.142l128.662 128.662c7.81 7.81 7.81 20.474 0 28.284L34.142 398.804C21.543 411.404 0 402.48 0 384.662z"></path></svg>
                            <span className="text-gray-900 font-medium" style={{ color: "#000000", fontSize: "16px", fontFamily: "'Inter', sans-serif", fontWeight: 400 }}>The delivery method ?</span>
                          </summary>
                          <div className="pl-6 mt-2 text-gray-800 leading-7 md:text-[17px]" style={{ color: "#000000", fontSize: "16px", fontFamily: "'Inter', sans-serif", fontWeight: 400 }}>
                            {serviceDetails?.delivery_method ? (
                              <div dangerouslySetInnerHTML={{ __html: serviceDetails.delivery_method.replace(/\n/g, '<br>') }} />
                            ) : (
                              <div className="text-gray-500 italic">
                                Aucune donnée disponible
                              </div>
                            )}
                          </div>
                        </details>

                        <details className="group" open>
                          <summary className="flex items-center gap-2 cursor-pointer select-none py-2">
                            <svg aria-hidden="true" className="e-font-icon-svg e-fas-caret-right" viewBox="0 0 192 512" xmlns="http://www.w3.org/2000/svg" width="10" height="16" fill="#000000"><path d="M0 384.662V127.338c0-17.818 21.543-26.741 34.142-14.142l128.662 128.662c7.81 7.81 7.81 20.474 0 28.284L34.142 398.804C21.543 411.404 0 402.48 0 384.662z"></path></svg>
                            <span className="text-gray-900 font-medium" style={{ color: "#000000", fontSize: "16px", fontFamily: "'Inter', sans-serif", fontWeight: 400 }}>Why Choose Me ?</span>
                          </summary>
                          <div className="pl-6 mt-2 text-gray-800" style={{ color: "#000000", fontSize: "16px", fontFamily: "'Inter', sans-serif", fontWeight: 400 }}>
                            {serviceDetails?.why_choose_me ? (
                              <div dangerouslySetInnerHTML={{ __html: serviceDetails.why_choose_me.replace(/\n/g, '<br>') }} />
                            ) : (
                              <div className="text-gray-500 italic">
                                Aucune donnée disponible
                              </div>
                            )}
                          </div>
                        </details>
                      </div>
                    </aside>
                  </div>
                </div>
              </div>

              {/* Other Services Proposed */}
              {project?.price ?
                <div className="bg-white rounded-lg p-6 shadow-sm">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Other Services proposed</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* {services?.filter((item: any) => item.id !== project.id) */}
                    {currentItems.length === 0 ? (
                        <div className="w-full flex flex-col items-center justify-center py-16 text-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-16 h-16 text-gray-400 mb-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            strokeWidth={1.5}
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          <h2 className="text-lg font-semibold text-gray-700">
                            No other service found for this professional
                          </h2>
                          
                        </div>
                      ) : currentItems.map((item: any) => {
                      return (
                        <div
                          key={item.id}
                          className="group w-full bg-white overflow-hidden relative flex flex-col cursor-pointer"
                          // onClick={() => navigate(`/professionals/${item.id}`)}
                          onClick={() => {
                            navigate('/details-search', {
                              state: { service: item }
                            });
                          }}
                        >
                          {/* Image container - ratio 4:3 */}
                          <div className="relative w-full pb-[65%]">
                            {" "}
                            {/* 4:3 ratio */}
                            <div
                              style={{
                                backgroundImage: `url(${item.image_url})`,
                                borderRadius: "5px",
                              }}
                              className="absolute inset-0 bg-cover bg-center z-1000"
                            />
                          </div>

                          {/* Content container */}
                          <div className="px-0 py-0 pr-4 flex flex-col mb-4">
                            {/* Titre avec alignement fixe */}
                            <div className="flex items-center min-h-[27px]">
                              <h2
                                className="font-medium text-[18px] line-clamp-1 flex-1"
                                style={{
                                  fontFamily: "'Inter', sans-serif",
                                  color: "#000000",
                                  fontWeight: "bold",
                                }}
                              >
                                {item?.title}
                              </h2>
                              <div
                                className="flex items-center gap-4"
                                style={{ fontFamily: "Arial, sans-serif" }}
                              >
                                <span className="flex items-center gap-1 text-[12px]">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="ionicon"
                                    viewBox="0 0 512 512"
                                    width="14"
                                    height="14"
                                    fill="#787777ff"
                                  >
                                    <circle cx="256" cy="256" r="64" />
                                    <path d="M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96c-42.52 0-84.33 12.15-124.27 36.11-40.73 24.43-77.63 60.12-109.68 106.07a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416c46.71 0 93.81-14.43 136.2-41.72 38.46-24.77 72.72-59.66 99.08-100.92a32.2 32.2 0 00-.1-34.76zM256 352a96 96 0 1196-96 96.11 96.11 0 01-96 96z" />
                                  </svg>
                                  {item?.views}
                                </span>

                                <span
                                  className="flex items-center gap-1 text-[12px]"
                                // onClick={(e) => handleLike(e, item.id)}
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="ionicon"
                                    viewBox="0 0 512 512"
                                    width="14"
                                    height="14"
                                    fill="#787777ff"
                                  // fill={isLiked ? "#006EFF" : "#787777ff"}
                                  >
                                    <path d="M400 480a16 16 0 01-10.63-4L256 357.41 122.63 476A16 16 0 0196 464V96a64.07 64.07 0 0164-64h192a64.07 64.07 0 0164 64v368a16 16 0 01-16 16z"></path>
                                  </svg>
                                  {item?.likes}
                                </span>
                              </div>
                            </div>

                            <div className="flex justify-between items-end">
                              {/* Auteur en bas à gauche */}
                              <div className="flex items-center gap-2">
                                <img
                                  src={item?.avatar}
                                  alt={item?.professional_name}
                                  className="w-5 h-5 rounded-full object-cover border-white"
                                />
                                <div className="flex items-center gap-1">
                                  <h3 className="text-gray-900 text-[12px] font-medium leading-tight mr-1">
                                    {item?.professional_name}
                                  </h3>
                                  {item?.isPro && (
                                    <span className="bg-[#000000] text-white text-[10px] rounded-full px-2.5 py-0.5 inline-block">
                                      PRO
                                    </span>
                                  )}
                                </div>
                              </div>

                            </div>

                            <p className="text-[13px] mt-2">USD  <strong>{item?.price}</strong></p>

                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* ✅ Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center items-center gap-4 mt-6">
                      <button
                        // className="px-4 py-2 bg-gray-100 rounded-lg text-gray-600 hover:bg-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed"
                        className="px-4 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
                        onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        Précédent
                      </button>
                      {/* <span className="text-sm text-gray-700">
                        Page {currentPage} / {totalPages}
                      </span> */}
                      <button
                        // className="px-4 py-2 bg-gray-100 rounded-lg text-gray-600 hover:bg-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed"
                        className="px-4 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
                        onClick={() =>
                          setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                        }
                        disabled={currentPage === totalPages}
                      >
                        Suivant
                      </button>
                    </div>
                  )}
                </div>
                :
                <div className="bg-white rounded-lg p-6 shadow-sm xs:w-full">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Other Achievements proposed</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* {projects?.filter((item: any) => item.id !== project.id) */}
                    {currentItemsAchievement.length === 0 ? (
                        <div className="w-full flex flex-col items-center justify-center py-16 text-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="w-16 h-16 text-gray-400 mb-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            strokeWidth={1.5}
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          <h2 className="text-lg font-semibold text-gray-700">
                            No other achievement found for this professional
                          </h2>
                          
                        </div>
                      ) :currentItemsAchievement.map((item: any) => {
                      // const isLiked = likedProfiles.includes(item.id);
                      // const totalLikes = likesCount[item.id] || item.likes;

                      return (
                        <div
                          key={item.id}
                          className="group w-full bg-white overflow-hidden relative flex flex-col cursor-pointer"
                          // onClick={() => navigate(`/professionals/${item.id}`)}
                          onClick={() => {
                            navigate('/details-search', {
                              state: { service: item }
                            });
                          }}
                        >
                          {/* Image container - ratio 4:3 */}
                          <div className="relative w-full pb-[75%]">
                            {" "}
                            {/* 4:3 ratio */}
                            <div
                              style={{
                                backgroundImage: `url(${item?.image_url})`,
                                borderRadius: "5px",
                              }}
                              className="absolute inset-0 bg-cover bg-center z-1000"
                            />
                          </div>

                          {/* Content container */}
                          <div className="px-0 py-0 pr-4 flex flex-col mb-4">
                            {/* Titre avec alignement fixe */}
                            <div className="flex items-center min-h-[27px]">
                              <h2
                                className="font-medium text-[18px] line-clamp-1 flex-1"
                                style={{
                                  fontFamily: "'Inter', sans-serif",
                                  color: "#000000",
                                  fontWeight: "bold",
                                }}
                              >
                                {item?.title}
                              </h2>
                              {/* <div
                                className="flex items-center gap-4"
                                style={{ fontFamily: "Arial, sans-serif" }}
                              > */}
                                {/* <span className="flex items-center gap-1 text-[12px]">
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="ionicon"
                                    viewBox="0 0 512 512"
                                    width="14"
                                    height="14"
                                    fill="#787777ff"
                                  >
                                    <circle cx="256" cy="256" r="64" />
                                    <path d="M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96c-42.52 0-84.33 12.15-124.27 36.11-40.73 24.43-77.63 60.12-109.68 106.07a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416c46.71 0 93.81-14.43 136.2-41.72 38.46-24.77 72.72-59.66 99.08-100.92a32.2 32.2 0 00-.1-34.76zM256 352a96 96 0 1196-96 96.11 96.11 0 01-96 96z" />
                                  </svg>
                                  {item.views}
                                </span> */}

                                {/* <span
                                  className="flex items-center gap-1 text-[12px]"
                                // onClick={(e) => handleLike(e, item.id)}
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    className="ionicon"
                                    viewBox="0 0 512 512"
                                    width="14"
                                    height="14"
                                    fill="#787777ff"
                                  // fill={isLiked ? "#006EFF" : "#787777ff"}
                                  >
                                    <path d="M400 480a16 16 0 01-10.63-4L256 357.41 122.63 476A16 16 0 0196 464V96a64.07 64.07 0 0164-64h192a64.07 64.07 0 0164 64v368a16 16 0 01-16 16z"></path>
                                  </svg>
                                  {item.likes}
                                </span> */}
                              {/* </div> */}
                            </div>

                            <div className="flex justify-between items-end">
                              {/* Auteur en bas à gauche */}
                              <div className="flex items-center gap-2">
                                <img
                                  src={item?.avatar}
                                  alt={item?.professional_name}
                                  className="w-5 h-5 rounded-full object-cover border-white"
                                />
                                <div className="flex items-center gap-1">
                                  <h3 className="text-gray-900 text-[12px] font-medium leading-tight mr-1">
                                    {item?.professional_name}
                                  </h3>
                                  {item?.isPro && (
                                    <span className="bg-[#000000] text-white text-[10px] rounded-full px-2.5 py-0.5 inline-block">
                                      PRO
                                    </span>
                                  )}
                                </div>
                              </div>
                              {/* Métriques en bas à droite */}
                            </div>

                            {/* <p className="text-[13px] mt-2">USD  <strong>{item.price}</strong></p> */}

                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* ✅ Pagination */}
                  {totalPagesAChievement > 1 && (
                    <div className="flex justify-center items-center gap-4 mt-6">
                      <button
                        // className="px-4 py-2 bg-gray-100 rounded-lg text-gray-600 hover:bg-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed"
                        className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
                        onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                      >
                        Précédent
                      </button>
                      {/* <span className="text-sm text-gray-700">
                        Page {currentPage} / {totalPages}
                      </span> */}
                      <button
                        // className="px-4 py-2 bg-gray-100 rounded-lg text-gray-600 hover:bg-gray-200 transition disabled:opacity-50 disabled:cursor-not-allowed"
                        className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
                        onClick={() =>
                          setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                        }
                        disabled={currentPage === totalPages}
                      >
                        Suivant
                      </button>
                    </div>
                  )}
                </div>

              }
            </div>


            {/* Sidebar */}
            <div className="lg:col-span-2 flex flex-col order-1 lg:order-2">
              {/* Designer Profile - Premier sur mobile */}
              <div className="lg:hidden bg-gray-100 rounded-2xl p-2 mb-4 shadow-sm space-y-6">
                {/* Contenu de la section Designer Profile */}
                <div className="space-y-4">
                  <div
                    onClick={() => navigate(`/professionals/${professional?.id}`)}
                    className="flex items-center">
                    <img
                      src={getImageUrl(professional?.avatar)}
                      alt={`${professional?.first_name} ${professional?.last_name}`}
                      className="w-28 h-28 rounded-full object-cover mr-3"
                      onError={(e) => {
                        e.currentTarget.onerror = null;
                        e.currentTarget.src = 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
                      }}
                    />
                    <div>
                      <p className="text-xs mt-10"
                        style={{
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: "300",
                          color: "#000000"
                        }}
                      >Visite portfolio</p>
                      <h4 className="text-sm font-semibold"
                        style={{
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: "400",
                          fontSize: "16px",
                          color: "#000000"
                        }}
                      >{project?.professional_name || "Jack and Moris Render"}</h4>
                      <p
                        style={{
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: "400",
                          fontSize: "16px",
                          color: "#000000"
                        }}
                      >{professional?.title || 'Architecture visualisation'}</p>
                    </div>
                  </div>

                  <div className="flex items-center text-sm text-gray-600">
                    <span className="mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="ionicon" viewBox="0 0 512 512" width="16" height="20">
                        <circle cx="256" cy="192" r="32"></circle>
                        <path d="M256 32c-88.22 0-160 68.65-160 153 0 40.17 18.31 93.59 54.42 158.78 29 52.34 62.55 99.67 80 123.22a31.75 31.75 0 0051.22 0c17.42-23.55 51-70.88 80-123.22C397.69 278.61 416 225.19 416 185c0-84.35-71.78-153-160-153zm0 224a64 64 0 1164-64 64.07 64.07 0 01-64 64z"></path>
                      </svg>
                    </span>
                    <span
                      style={{
                        fontFamily: "'Inter', sans-serif",
                        fontWeight: "400",
                        fontSize: "14px",
                        color: "#000000"
                      }}
                    >{professional?.city}, {professional?.country}</span>
                  </div>

                  <div className="flex items-center">
                    <span
                      className="bg-[#000000] text-white rounded-full px-1.5 py-0.5 inline-block mr-3"
                      style={{
                        fontSize: "8px",
                        fontFamily: "'Inter', sans-serif",
                        fontWeight: 300,
                      }}
                    >
                      PRO
                    </span>
                    <span
                      style={{
                        fontFamily: "'Inter', sans-serif",
                        fontWeight: "400",
                        fontSize: "14px",
                        color: "#000000"
                      }}
                    >Pro acount</span>
                  </div>

                  <div className="flex items-center text-sm">
                    <span className="mr-2 text-xl text-gray-700">
                      <svg xmlns="http://www.w3.org/2000/svg" className="ionicon" viewBox="0 0 512 512" width="16" height="20">
                        <path fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="32" d="M48 112h288M192 64v48M272 448l96-224 96 224M301.5 384h133M281.3 112S257 206 199 277 80 384 80 384"></path>
                        <path d="M256 336s-35-27-72-75-56-85-56-85" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="32"></path>
                      </svg>
                    </span>
                    <ul className="flex space-x-2 list-none uppercase">
                      {professional?.languages?.map((language, index) => (
                        <li key={index}
                          style={{
                            fontFamily: "'Inter', sans-serif",
                            fontWeight: "400",
                            fontSize: "14px",
                            color: "#000000"
                          }}
                        >{language}</li>
                      ))}
                    </ul>
                  </div>

                  <button className="w-full bg-white py-2 rounded-full flex items-center justify-center hover:bg-gray-50"
                    style={{
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: "400",
                      fontSize: "14px",
                      color: "#000000"
                    }}
                  >
                    <span className="mr-2">
                      <svg aria-hidden="true" className="e-font-icon-svg e-fas-thumbtack" viewBox="0 0 384 512" xmlns="http://www.w3.org/2000/svg" width="14" height="18">
                        <path d="M298.028 214.267L285.793 96H328c13.255 0 24-10.745 24-24V24c0-13.255-10.745-24-24-24H56C42.745 0 32 10.745 32 24v48c0 13.255 10.745 24 24 24h42.207L85.972 214.267C37.465 236.82 0 277.261 0 328c0 13.255 10.745 24 24 24h136v104.007c0 1.242.289 2.467.845 3.578l24 48c2.941 5.882 11.364 5.893 14.311 0l24-48a8.008 8.008 0 0 0 .845-3.578V352h136c13.255 0 24-10.745 24-24-.001-51.183-37.983-91.42-85.973-113.733z"></path>
                      </svg>
                    </span>
                    Save for later
                  </button>
                </div>
              </div>

              {/* Contenu principal de la sidebar (toujours visible) */}
              <div
                className={`bg-white rounded-lg shadow-sm sticky ${isScrolled ? "top-[80px] py-3" : "top-0 py-0"
                  }`}
              >
                <div className="bg-gray-100 rounded-2xl p-4 shadow-sm fixed bottom-0 w-full left-0 lg:static lg:w-auto lg:left-auto">
                  {/* Style intégré pour gérer l'affichage responsive */}
                  

                  <div className="mobile-two-columns">
                    {/* Section d'informations */}
                    <div className="mobile-info-column mb-4 md:mb-6">
                      <h3
                        style={{
                          fontFamily: "'Inter', sans-serif",
                          color: "#000000",
                          fontWeight: "500",
                          fontSize: "20px"
                        }}
                      >{project?.price ? `From ${Number(project.price).toFixed(2)} USD` : ""}</h3>
                      <p className="text-gray-500 text-sm pt-2"><strong>Catégorie :</strong> {getCategoryLabel(project?.category)}</p>
                      <p className="text-gray-500 text-sm pt-2">{project?.concepts ? `Concepts ${project.concepts}` : ""}</p>
                      <p className="text-gray-500 text-sm pt-2">{project?.revisions ? `Revision ${project.revisions}` : ""}</p>
                      <p className="text-gray-500 text-sm pt-2">{project?.date_create
                        ? `Create at : ${new Date(project.date_create).toLocaleDateString("fr-FR")}`
                        : ""}</p>
                    </div>

                    {/* Conteneur des boutons */}
                    <div className="mobile-buttons-column flex flex-col space-y-2 md:space-y-3">
                      <button
                        onClick={() => setShowQuoteModal(true)}
                        className="w-full bg-black text-white py-3 rounded-full font-medium hover:bg-gray-800"
                        style={{
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: "400",
                          fontSize: "16px"
                        }}
                      >
                        Let's make a deal
                      </button>
                      <button
                        onClick={() => setShowQuoteModal(true)}
                        className="w-full bg-white text-gray-800 py-3 rounded-full font-medium hover:bg-black-50 border border-gray-300"
                        style={{
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: "400",
                          fontSize: "16px",
                          color: "#000000"
                        }}
                      >
                        Invite to an open offer
                      </button>
                    </div>
                  </div>
                </div>

                {/* Designer Profile - Caché sur mobile, visible sur desktop */}
                <div className="hidden lg:block bg-gray-100 rounded-2xl p-2 mt-4 shadow-sm sticky top-24 space-y-6">
                  {/* Même contenu que la section mobile ci-dessus */}
                  <div className="space-y-4">
                    <div
                      onClick={() => navigate(`/professionals/${professional?.id}`)}
                      className="flex items-center">
                      <img
                        src={getImageUrl(professional?.avatar)}
                        alt={`${professional?.first_name} ${professional?.last_name}`}
                        className="w-28 h-28 rounded-full object-cover mr-3"
                        onError={(e) => {
                          e.currentTarget.onerror = null;
                          e.currentTarget.src = 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
                        }}
                      />
                      <div>
                        <p className="text-xs mt-10"
                          style={{
                            fontFamily: "'Inter', sans-serif",
                            fontWeight: "300",
                            color: "#000000"
                          }}
                        >Visite portfolio</p>
                        <h4 className="text-sm font-semibold"
                          style={{
                            fontFamily: "'Inter', sans-serif",
                            fontWeight: "400",
                            fontSize: "16px",
                            color: "#000000"
                          }}
                        >{project?.professional_name || "Jack and Moris Render"}</h4>
                        <p
                          style={{
                            fontFamily: "'Inter', sans-serif",
                            fontWeight: "400",
                            fontSize: "16px",
                            color: "#000000"
                          }}
                        >{professional?.title || 'Architecture visualisation'}</p>
                      </div>
                    </div>

                    <div className="flex items-center text-sm text-gray-600">
                      <span className="mr-3">
                        <svg xmlns="http://www.w3.org/2000/svg" className="ionicon" viewBox="0 0 512 512" width="16" height="20">
                          <circle cx="256" cy="192" r="32"></circle>
                          <path d="M256 32c-88.22 0-160 68.65-160 153 0 40.17 18.31 93.59 54.42 158.78 29 52.34 62.55 99.67 80 123.22a31.75 31.75 0 0051.22 0c17.42-23.55 51-70.88 80-123.22C397.69 278.61 416 225.19 416 185c0-84.35-71.78-153-160-153zm0 224a64 64 0 1164-64 64.07 64.07 0 01-64 64z"></path>
                        </svg>
                      </span>
                      <span
                        style={{
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: "400",
                          fontSize: "14px",
                          color: "#000000"
                        }}
                      >{professional?.city}, {professional?.country}</span>
                    </div>

                    <div className="flex items-center">
                      <span
                        className="bg-[#000000] text-white rounded-full px-1.5 py-0.5 inline-block mr-3"
                        style={{
                          fontSize: "8px",
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: 300,
                        }}
                      >
                        PRO
                      </span>
                      <span
                        style={{
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: "400",
                          fontSize: "14px",
                          color: "#000000"
                        }}
                      >Pro acount</span>
                    </div>

                    <div className="flex items-center text-sm">
                      <span className="mr-2 text-xl text-gray-700">
                        <svg xmlns="http://www.w3.org/2000/svg" className="ionicon" viewBox="0 0 512 512" width="16" height="20">
                          <path fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="32" d="M48 112h288M192 64v48M272 448l96-224 96 224M301.5 384h133M281.3 112S257 206 199 277 80 384 80 384"></path>
                          <path d="M256 336s-35-27-72-75-56-85-56-85" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="32"></path>
                        </svg>
                      </span>
                      <ul className="flex space-x-2 list-none uppercase">
                        {professional?.languages?.map((language, index) => (
                          <li key={index}
                            style={{
                              fontFamily: "'Inter', sans-serif",
                              fontWeight: "400",
                              fontSize: "14px",
                              color: "#000000"
                            }}
                          >{language}</li>
                        ))}
                      </ul>
                    </div>

                    <button className="w-full bg-white py-2 rounded-full flex items-center justify-center hover:bg-gray-50"
                      style={{
                        fontFamily: "'Inter', sans-serif",
                        fontWeight: "400",
                        fontSize: "14px",
                        color: "#000000"
                      }}
                    >
                      <span className="mr-2">
                        <svg aria-hidden="true" className="e-font-icon-svg e-fas-thumbtack" viewBox="0 0 384 512" xmlns="http://www.w3.org/2000/svg" width="14" height="18">
                          <path d="M298.028 214.267L285.793 96H328c13.255 0 24-10.745 24-24V24c0-13.255-10.745-24-24-24H56C42.745 0 32 10.745 32 24v48c0 13.255 10.745 24 24 24h42.207L85.972 214.267C37.465 236.82 0 277.261 0 328c0 13.255 10.745 24 24 24h136v104.007c0 1.242.289 2.467.845 3.578l24 48c2.941 5.882 11.364 5.893 14.311 0l24-48a8.008 8.008 0 0 0 .845-3.578V352h136c13.255 0 24-10.745 24-24-.001-51.183-37.983-91.42-85.973-113.733z"></path>
                        </svg>
                      </span>
                      Save for later
                    </button>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <Footer />

      <QuoteRequestModal
        token={token}
        user={user}
        pro={professional}
        open={showQuoteModal}
        onOpenChange={setShowQuoteModal}
      />
    </>
  );
};

export default ServiceDetail;