import { fetchApi } from '../utils/fetchApi';
import { getAuthToken } from '../utils/auth';
import { API_BASE_URL } from '../config';

// Define the new unified profile data interface
export interface UnifiedProfileData {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  bio?: string;
  avatar?: string;
  social_links?: Record<string, string>;
  completion_percentage: number;
  is_professional: boolean;
  
  // Professional-specific fields
  title?: string;
  profession?: string;
  expertise?: string[];
  years_of_experience?: number;
  hourly_rate?: number;
  description?: string;
  skills?: string[];
  portfolio?: Array<{
    id: string;
    name: string;
    path: string;
    type: string;
    uploaded_at: string;
  }>;
  availability_status?: 'available' | 'unavailable' | 'busy';
  languages?: string[];
  services_offered?: string[];
  rating?: number;
  
  // Client-specific fields
  type?: 'particulier' | 'entreprise';
  company_name?: string;
  company_size?: string;
  industry?: string;
  position?: string;
  website?: string;
  birth_date?: string;
  preferences?: Record<string, boolean>;
}

// Service for the new unified profile structure
export const newProfileService = {
  // Get the profile of the authenticated user
  getProfile: async (): Promise<{ profile: UnifiedProfileData }> => {
    try {
      console.log('Retrieving unified profile...');
      const data = await fetchApi<{ profile: UnifiedProfileData }>('/api/profile/new');
      console.log('Unified profile retrieved:', data);
      
      return data;
    } catch (error) {
      console.error('Error retrieving unified profile:', error);
      throw error;
    }
  },
  
  // Update the profile of the authenticated user
  updateProfile: async (profileData: Record<string, any>): Promise<{ message: string; profile: UnifiedProfileData }> => {
    try {
      console.log('Updating unified profile...');
      console.log('Profile data to update:', profileData);
      
      const data = await fetchApi<{ message: string; profile: UnifiedProfileData }>('/api/profile/new', {
        method: 'PUT',
        body: JSON.stringify(profileData),
      });
      
      console.log('Unified profile updated:', data);
      return data;
    } catch (error) {
      console.error('Error updating unified profile:', error);
      throw error;
    }
  },
  
  // Update profile with FormData (for file uploads)
  updateProfileWithFiles: async (formData: FormData): Promise<{ message: string; profile: UnifiedProfileData }> => {
    const token = getAuthToken();
    
    if (!token) {
      throw new Error('No authentication token found');
    }
    
    const url = `${API_BASE_URL}/api/profile/new`;
    console.log('Sending PUT request with FormData to:', url);
    
    try {
      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          // Don't set Content-Type header when sending FormData
        },
        body: formData,
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(JSON.stringify({
          status: response.status,
          message: errorData.message || response.statusText,
          errors: errorData.errors,
        }));
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error updating profile with files:', error);
      throw error;
    }
  },
  
  // Complete profile (first login)
  completeProfile: async (profileData: FormData): Promise<{ message: string; profile: UnifiedProfileData }> => {
    const token = getAuthToken();
    
    if (!token) {
      throw new Error('No authentication token found');
    }
    
    const url = `${API_BASE_URL}/api/profile/new/complete`;
    console.log('Sending profile completion request to:', url);
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: profileData,
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(JSON.stringify({
          status: response.status,
          message: errorData.message || response.statusText,
          errors: errorData.errors,
        }));
      }
      
      return await response.json();
    } catch (error) {
      console.error('Error completing profile:', error);
      throw error;
    }
  },
  
  // Upload avatar
  uploadAvatar: async (file: File): Promise<{ message: string; avatar_url: string }> => {
    const formData = new FormData();
    formData.append('avatar', file);
    
    try {
      const data = await fetchApi<{ message: string; avatar_url: string }>('/api/profile/new/avatar', {
        method: 'POST',
        body: formData,
        headers: {}, // Let fetch set the Content-Type header with the boundary
      });
      
      return data;
    } catch (error) {
      console.error('Error uploading avatar:', error);
      throw error;
    }
  },
  
  // Upload portfolio items (professional only)
  uploadPortfolioItems: async (files: File[]): Promise<{ message: string; portfolio: any[] }> => {
    const formData = new FormData();
    
    files.forEach((file, index) => {
      formData.append(`portfolio_items[${index}]`, file);
    });
    
    try {
      const data = await fetchApi<{ message: string; portfolio: any[] }>('/api/profile/new/portfolio', {
        method: 'POST',
        body: formData,
        headers: {}, // Let fetch set the Content-Type header with the boundary
      });
      
      return data;
    } catch (error) {
      console.error('Error uploading portfolio items:', error);
      throw error;
    }
  },
  
  // Delete portfolio item (professional only)
  deletePortfolioItem: async (id: string): Promise<{ message: string; portfolio: any[] }> => {
    try {
      const data = await fetchApi<{ message: string; portfolio: any[] }>(`/api/profile/new/portfolio/${id}`, {
        method: 'DELETE',
      });
      
      return data;
    } catch (error) {
      console.error('Error deleting portfolio item:', error);
      throw error;
    }
  },
  
  // Update availability status (professional only)
  updateAvailability: async (status: 'available' | 'unavailable' | 'busy'): Promise<{ message: string; availability_status: string }> => {
    try {
      const data = await fetchApi<{ message: string; availability_status: string }>('/api/profile/new/availability', {
        method: 'PUT',
        body: JSON.stringify({ availability_status: status }),
      });
      
      return data;
    } catch (error) {
      console.error('Error updating availability status:', error);
      throw error;
    }
  },
  
  // Get profile completion status
  getCompletionStatus: async (): Promise<{ completion_percentage: number; is_completed: boolean }> => {
    try {
      const data = await fetchApi<{ completion_percentage: number; is_completed: boolean }>('/api/profile/new/completion');
      return data;
    } catch (error) {
      console.error('Error getting completion status:', error);
      throw error;
    }
  },
};
