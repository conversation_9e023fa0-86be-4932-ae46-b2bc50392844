import { API_BASE_URL } from '../config';
import { ProfileData } from './profileService';

// Types pour le dashboard
export interface DashboardStats {
  activeProjects: number;
  earnings?: string;
  totalSpent?: string;
  rating?: number;
  completionRate?: number;
  connectedProfessionals?: number;
  projectsCompleted?: number;
}

export interface ProjectData {
  id: number;
  title: string;
  description: string;
  category: string;
  budget: string;
  deadline: string;
  status: 'draft' | 'open' | 'in_progress' | 'completed' | 'cancelled';
  skills: string[];
  attachments?: Array<{
    name: string;
    path: string;
    type: string;
  }>;
  created_at: string;
  updated_at: string;
  client?: {
    id: number;
    name: string;
    avatar?: string;
  };
  professional?: {
    id: number;
    name: string;
    avatar?: string;
  };
}

export interface ActivityData {
  id: number;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  related_id?: number;
  related_type?: string;
  icon?: string;
}

export interface ProfessionalData {
  id: number;
  first_name: string;
  last_name: string;
  title?: string;
  avatar?: string;
  rating?: number;
  skills?: string[];
  hourly_rate?: number;
  availability_status?: string;
}

export interface DashboardData {
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    is_professional: boolean;
  };
  stats: DashboardStats;
  projects: ProjectData[];
  activities: ActivityData[];
  profile: ProfileData;
  recommendedProfessionals?: ProfessionalData[];
}

// Fonction pour récupérer le token d'authentification
const getAuthToken = (): string | null => {
  return localStorage.getItem('token');
};

// Fonction générique pour les requêtes API
const fetchApi = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const token = getAuthToken();

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...options.headers,
  };

  if (token) {
    headers['Authorization' as keyof HeadersInit] = `Bearer ${token}`;
  }

  // Add /api prefix if endpoint doesn't start with /api
  const apiEndpoint = endpoint.startsWith('/api') ? endpoint : `/api${endpoint}`;
  const url = `${API_BASE_URL}${apiEndpoint}`;

  console.log(`API Request: ${options.method || 'GET'} ${url}`);

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(JSON.stringify({
        status: response.status,
        message: errorData.message || response.statusText,
        errors: errorData.errors,
      }));
    }

    return await response.json();
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};

// Service de dashboard
export const dashboardService = {
  // Récupérer les données du dashboard
  getDashboardData: async (): Promise<DashboardData> => {
    return fetchApi('/api/dashboard');
  },

  // Récupérer les projets
  getProjects: async (): Promise<{ projects: ProjectData[] }> => {
    return fetchApi('/api/dashboard/projects');
  },

  // Récupérer un projet spécifique
  getProject: async (id: number): Promise<{ project: ProjectData }> => {
    return fetchApi(`/api/dashboard/projects/${id}`);
  },

  // Créer un nouveau projet
  createProject: async (projectData: FormData): Promise<{ message: string; project: ProjectData }> => {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found');
    }

    const url = `${API_BASE_URL}/api/dashboard/projects`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      } as HeadersInit,
      body: projectData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(JSON.stringify({
        status: response.status,
        message: errorData.message || response.statusText,
        errors: errorData.errors,
      }));
    }

    return await response.json();
  },

  // Mettre à jour un projet
  updateProject: async (id: number, projectData: FormData): Promise<{ message: string; project: ProjectData }> => {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found');
    }

    const url = `${API_BASE_URL}/api/dashboard/projects/${id}`;

    const response = await fetch(url, {
      method: 'POST', // Utiliser POST avec _method=PUT pour les formulaires multipart
      headers: {
        'Authorization': `Bearer ${token}`,
      } as HeadersInit,
      body: projectData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(JSON.stringify({
        status: response.status,
        message: errorData.message || response.statusText,
        errors: errorData.errors,
      }));
    }

    return await response.json();
  },

  // Supprimer un projet
  deleteProject: async (id: number): Promise<{ message: string }> => {
    return fetchApi(`/api/dashboard/projects/${id}`, {
      method: 'DELETE',
    });
  },

  // Supprimer une pièce jointe d'un projet
  removeAttachment: async (projectId: number, attachmentIndex: number): Promise<{ message: string }> => {
    return fetchApi(`/api/dashboard/projects/${projectId}/attachments/${attachmentIndex}`, {
      method: 'DELETE',
    });
  },
};

export default dashboardService;
