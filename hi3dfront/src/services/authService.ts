import { API_BASE_URL } from '../config';

interface LoginCredentials {
  email: string;
  password: string;
}

interface LoginResponse {
  token: string;
  user: any;
  message?: string;
}

interface RegisterData {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  password_confirmation: string;
  is_professional: boolean;
}

/**
 * Service d'authentification pour gérer la connexion, l'inscription et la déconnexion
 */
export const authService = {
  /**
   * Connexion d'un utilisateur
   * @param credentials Identifiants de connexion (email, mot de passe)
   * @returns Réponse de connexion (token, utilisateur)
   */
  login: async (credentials: LoginCredentials): Promise<LoginResponse> => {
    const response = await fetch(`${API_BASE_URL}/api/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Erreur de connexion');
    }

    const data = await response.json();

    // Stocker le token et les données utilisateur
    localStorage.setItem('token', data.token);
    localStorage.setItem('user', JSON.stringify(data.user));
    
    // Marquer comme première connexion si l'utilisateur n'a pas encore complété son profil
    if (!data.user.profile_completed) {
      localStorage.setItem('first_login', 'true');
      console.log('Première connexion détectée, profil non complété');
    } else {
      localStorage.setItem('first_login', 'false');
      console.log('Profil déjà complété');
    }

    return data;
  },

  /**
   * Inscription d'un utilisateur
   * @param userData Données d'inscription
   * @returns Réponse d'inscription
   */
  register: async (userData: RegisterData): Promise<any> => {
    const response = await fetch(`${API_BASE_URL}/api/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const errorData = await response.json();

      // Créer une erreur avec le statut HTTP préservé
      const error = new Error(errorData.message || 'Erreur d\'inscription') as any;
      error.status = response.status;
      error.errors = errorData.errors;

      throw error;
    }

    return await response.json();
  },

  /**
   * Déconnexion d'un utilisateur
   * @returns Réponse de déconnexion
   */
  logout: async (): Promise<any> => {
    const token = localStorage.getItem('token');

    if (!token) {
      throw new Error('Aucun token d\'authentification trouvé');
    }

    const response = await fetch(`${API_BASE_URL}/api/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });

    // Supprimer le token et les données utilisateur même si la déconnexion échoue
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('first_login');

    if (!response.ok) {
      const errorData = await response.json();
      console.warn('Erreur lors de la déconnexion:', errorData.message);
    }

    return await response.json();
  },

  /**
   * Vérifier si l'utilisateur est authentifié
   * @returns true si l'utilisateur est authentifié, false sinon
   */
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem('token');
  },

  /**
   * Récupérer les données de l'utilisateur connecté
   * @returns Données de l'utilisateur ou null si non connecté
   */
  getUser: (): any | null => {
    const userJson = localStorage.getItem('user');
    return userJson ? JSON.parse(userJson) : null;
  },

  /**
   * Vérifier si c'est la première connexion de l'utilisateur
   * @returns true si c'est la première connexion, false sinon
   */
  isFirstLogin: (): boolean => {
    return localStorage.getItem('first_login') === 'true';
  },
};

export default authService;
