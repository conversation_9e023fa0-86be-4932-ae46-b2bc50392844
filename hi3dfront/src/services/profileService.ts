import { API_BASE_URL } from '../config';

// Types pour les profils
export interface SocialLinks {
  linkedin?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
}

export interface ProfileData {
  id?: number;
  user_id?: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  bio?: string;
  avatar?: string;
  birth_date?: string;
  title?: string;
  profession?: string;
  expertise?: string[];
  years_of_experience?: number;
  hourly_rate?: number;
  skills?: string[];
  portfolio?: Array<{
    id: string;
    path: string;
    name: string;
    description?: string;
    type: string;
    created_at: string;
  }>;
  availability_status?: string;
  languages?: string[];
  services_offered?: string[];
  rating?: number;
  social_links?: SocialLinks;
  company_name?: string;
  company_size?: string;
  industry?: string;
  position?: string;
  website?: string;
  preferences?: {
    notifications?: boolean;
    newsletter?: boolean;
    project_updates?: boolean;
  };
  completion_percentage: number;
  created_at?: string;
  updated_at?: string;
}

// Fonction pour récupérer le token d'authentification
const getAuthToken = (): string | null => {
  return localStorage.getItem('token');
};

// Fonction pour vérifier si l'utilisateur est authentifié
const isAuthenticated = (): boolean => {
  return !!getAuthToken();
};

// Fonction générique pour les requêtes API
const fetchApi = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> => {
  const token = getAuthToken();

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    ...options.headers,
  };

  if (token) {
    headers['Authorization' as keyof HeadersInit] = `Bearer ${token}`;
  }

  // Add /api prefix if endpoint doesn't start with /api
  const apiEndpoint = endpoint.startsWith('/api') ? endpoint : `/api${endpoint}`;
  const url = `${API_BASE_URL}${apiEndpoint}`;

  console.log(`API Request: ${options.method || 'GET'} ${url}`);

  try {
    console.log("Ici profileService.ts");
    const response = await fetch(url, {
      ...options,
      headers,
    });

    console.log("Ici profileService.ts reponse ====", response);


    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(JSON.stringify({
        status: response.status,
        message: errorData.message || response.statusText,
        errors: errorData.errors,
      }));
    }

    return await response.json();
  } catch (error) {
    console.error('API Error:', error);
    throw error;
  }
};

// Service de profil
export const profileService = {
  // Récupérer le profil de l'utilisateur authentifié
  getProfile: async (): Promise<{ profile: ProfileData }> => {
    try {
      // Récupérer directement le profil générique qui fonctionne pour tous les types d'utilisateurs
      console.log('Récupération du profil utilisateur...');
      const data = await fetchApi<{ profile: ProfileData }>('/api/profile');
      console.log('Profil utilisateur récupéré:', data);

      // S'assurer que le rating est un nombre ou undefined
      if (data.profile && data.profile.rating !== undefined) {
        data.profile.rating = typeof data.profile.rating === 'number'
          ? data.profile.rating
          : undefined;
      }

      return data;
    } catch (error) {
      console.error('Erreur lors de la récupération du profil:', error);
      throw error;
    }
  },

  // Mettre à jour le profil (méthode originale, conservée pour compatibilité)
  updateProfile: async (profileData: FormData): Promise<{ message: string; profile: ProfileData }> => {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found');
    }

    const url = `${API_BASE_URL}/api/profile/client`;
    console.log('Envoi de la requête POST à:', url);
    console.log('Token d\'authentification présent:', !!token);

    // Log des données envoyées
    console.log('Données du formulaire:');
    // Fix for TypeScript error: Using Array.from to convert iterator to array
    Array.from(profileData.entries()).forEach(pair => {
      console.log(pair[0], pair[1]);
    });

    try {
      // Créer un nouvel objet FormData pour éviter de modifier l'original
      const formDataToSend = new FormData();

      // Copier toutes les entrées sauf social_links
      // Utiliser Array.from pour éviter les problèmes d'itération avec TypeScript
      Array.from(profileData.entries()).forEach(([key, value]) => {
        if (key !== 'social_links') {
          formDataToSend.append(key, value);
        }
      });

      // Traiter social_links séparément
      const socialLinksValue = profileData.get('social_links');
      if (socialLinksValue) {
        try {
          // Essayer de parser la valeur JSON
          const socialLinksObj = JSON.parse(socialLinksValue as string);

          // Ajouter chaque réseau social individuellement
          if (socialLinksObj) {
            Object.entries(socialLinksObj).forEach(([network, url]) => {
              if (url) {
                formDataToSend.append(`social_links[${network}]`, url as string);
              }
            });
          }
          console.log('Social links traités individuellement');
        } catch (e) {
          console.error('Erreur lors du parsing des social_links:', e);
          // En cas d'erreur, ajouter la valeur originale
          formDataToSend.append('social_links', socialLinksValue);
        }
      }

      // Utiliser directement la méthode POST qui est plus fiable
      console.log('Tentative avec méthode POST...');
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        } as HeadersInit,
        body: formDataToSend,
      });

      console.log('Statut de la réponse POST:', response.status);
      console.log('Headers de la réponse:', response.headers);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Erreur de réponse POST:', errorData);
        throw new Error(JSON.stringify({
          status: response.status,
          message: errorData.message || response.statusText,
          errors: errorData.errors,
        }));
      }

      const responseData = await response.json();
      console.log('Données de réponse POST:', responseData);
      return responseData;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil:', error);
      throw error;
    }
  },

  // Nouvelle méthode pour mettre à jour le profil avec JSON
  updateProfileJSON: async (profileData: any): Promise<{ message: string; profile: ProfileData }> => {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found');
    }

    const url = `${API_BASE_URL}/api/profile/client/json`;
    console.log('Envoi de la requête POST JSON à:', url);
    console.log('Token d\'authentification présent:', !!token);
    console.log('Données JSON à envoyer:', profileData);

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        } as HeadersInit,
        body: JSON.stringify(profileData),
      });

      console.log('Statut de la réponse POST JSON:', response.status);
      console.log('Headers de la réponse:', response.headers);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Erreur de réponse POST JSON:', errorData);
        throw new Error(JSON.stringify({
          status: response.status,
          message: errorData.message || response.statusText,
          errors: errorData.errors,
        }));
      }

      const responseData = await response.json();
      console.log('Données de réponse POST JSON:', responseData);
      return responseData;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil JSON:', error);
      throw error;
    }
  },

  // Nouvelle méthode pour mettre à jour le profil avec avatar
  updateProfileWithAvatar: async (profileData: FormData): Promise<{ message: string; profile: ProfileData }> => {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found');
    }

    const url = `${API_BASE_URL}/api/profile/client/with-avatar`;
    console.log('Envoi de la requête POST avec avatar à:', url);
    console.log('Token d\'authentification présent:', !!token);

    // Log des données envoyées
    console.log('Données du formulaire avec avatar:');
    Array.from(profileData.entries()).forEach(pair => {
      console.log(pair[0], typeof pair[1] === 'string' ? pair[1] : '[File]');
    });

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        } as HeadersInit,
        body: profileData,
      });

      console.log('Statut de la réponse POST avec avatar:', response.status);
      console.log('Headers de la réponse:', response.headers);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Erreur de réponse POST avec avatar:', errorData);
        throw new Error(JSON.stringify({
          status: response.status,
          message: errorData.message || response.statusText,
          errors: errorData.errors,
        }));
      }

      const responseData = await response.json();
      console.log('Données de réponse POST avec avatar:', responseData);
      return responseData;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du profil avec avatar:', error);
      throw error;
    }
  },

  // Compléter le profil (première connexion)
  completeProfile: async (profileData: FormData): Promise<{ message: string; profile: ProfileData }> => {
    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found');
    }

    const url = `${API_BASE_URL}/api/profile/complete-profile`;
    console.log('Envoi de la requête de complétion de profil à:', url);
    console.log('Token d\'authentification présent:', !!token);

    // Log des données envoyées
    console.log('Données du formulaire de complétion:');
    Array.from(profileData.entries()).forEach(pair => {
      console.log(pair[0], typeof pair[1] === 'string' ? pair[1] : '[File]');
    });

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        } as HeadersInit,
        body: profileData,
      });

      console.log('Statut de la réponse de complétion:', response.status);
      console.log('Headers de la réponse:', response.headers);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Erreur de réponse de complétion:', errorData);
        throw new Error(JSON.stringify({
          status: response.status,
          message: errorData.message || response.statusText,
          errors: errorData.errors,
        }));
      }

      const responseData = await response.json();
      console.log('Données de réponse de complétion:', responseData);
      return responseData;
    } catch (error) {
      console.error('Erreur lors de la complétion du profil:', error);
      throw error;
    }
  },

  // Récupérer le statut de complétion du profil
  getCompletionStatus: async (): Promise<{ completion_percentage: number; is_completed: boolean }> => {
    return fetchApi('/api/profile/completion');
  },

  // Uploader un avatar
  uploadAvatar: async (file: File): Promise<{ message: string; avatar_url: string }> => {
    const formData = new FormData();
    formData.append('avatar', file);

    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found');
    }

    const url = `${API_BASE_URL}/api/profile/avatar`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      } as HeadersInit,
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(JSON.stringify({
        status: response.status,
        message: errorData.message || response.statusText,
        errors: errorData.errors,
      }));
    }

    return await response.json();
  },

  // Ajouter un élément au portfolio
  uploadPortfolioItem: async (file: File, name: string, description?: string): Promise<{ message: string; portfolio_item: any }> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('name', name);

    if (description) {
      formData.append('description', description);
    }

    const token = getAuthToken();

    if (!token) {
      throw new Error('No authentication token found');
    }

    const url = `${API_BASE_URL}/api/profile/portfolio`;

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      } as HeadersInit,
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(JSON.stringify({
        status: response.status,
        message: errorData.message || response.statusText,
        errors: errorData.errors,
      }));
    }

    return await response.json();
  },

  // Supprimer un élément du portfolio
  deletePortfolioItem: async (id: string): Promise<{ message: string }> => {
    return fetchApi(`/api/profile/portfolio/${id}`, {
      method: 'DELETE',
    });
  },

  // Mettre à jour la disponibilité
  updateAvailability: async (availabilityStatus: string): Promise<{ message: string; availability_status: string }> => {
    return fetchApi('/api/profile/availability', {
      method: 'PUT',
      body: JSON.stringify({ availability_status: availabilityStatus }),
    });
  },
};

export default profileService;
