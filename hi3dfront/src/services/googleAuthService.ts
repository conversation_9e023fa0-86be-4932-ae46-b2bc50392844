import { API_BASE_URL } from '../config';

export interface GoogleAuthResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: any;
  redirect_to?: string;
  error_type?: 'user_not_found' | 'profile_incomplete' | 'server_error' | 'unknown';
  user_exists?: boolean;
  profile_completed?: boolean;
}

export interface GoogleAuthRedirectResponse {
  success: boolean;
  redirect_url?: string;
  message: string;
}

/**
 * Service d'authentification Google pour le frontend
 */
export class GoogleAuthService {
  
  /**
   * Obtenir l'URL de redirection pour l'authentification Google
   * Utilise les routes web avec sessions pour éviter les problèmes OAuth
   */
  static async getRedirectUrl(): Promise<GoogleAuthRedirectResponse> {
    try {
      // Utiliser la route web au lieu de l'API pour avoir accès aux sessions
      const redirectUrl = `${API_BASE_URL}/auth/gmail/frontend-redirect`;

      return {
        success: true,
        redirect_url: redirectUrl,
        message: 'URL de redirection générée avec succès'
      };
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'URL de redirection Google:', error);
      throw error;
    }
  }

  /**
   * Traiter le callback de Google OAuth
   * Cette méthode n'est plus utilisée car nous utilisons maintenant les routes web avec redirection
   * @deprecated Utiliser les paramètres URL directement dans le composant
   */
  static async handleCallback(code: string, state?: string): Promise<GoogleAuthResponse> {
    console.warn('handleCallback est déprécié. Utiliser les paramètres URL directement.');
    return {
      success: false,
      message: 'Méthode dépréciée',
      error_type: 'server_error',
      redirect_to: 'login'
    };
  }

  /**
   * Initier le processus d'authentification Google
   * Redirige l'utilisateur vers Google OAuth
   */
  static async initiateGoogleAuth(): Promise<void> {
    try {
      const redirectResponse = await this.getRedirectUrl();
      
      if (redirectResponse.success && redirectResponse.redirect_url) {
        // Rediriger vers Google OAuth
        window.location.href = redirectResponse.redirect_url;
      } else {
        throw new Error(redirectResponse.message || 'Impossible d\'obtenir l\'URL de redirection Google');
      }
    } catch (error) {
      console.error('Erreur lors de l\'initiation de l\'authentification Google:', error);
      throw error;
    }
  }

  /**
   * Vérifier le statut de la configuration Google
   */
  static async checkGoogleAuthStatus(): Promise<any> {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/gmail/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.message || 'Erreur lors de la vérification du statut');
      }

      return data;
    } catch (error) {
      console.error('Erreur lors de la vérification du statut Google Auth:', error);
      throw error;
    }
  }

  /**
   * Gérer les paramètres de callback dans l'URL
   * Utile pour traiter le retour de Google OAuth
   */
  static parseCallbackParams(): { code?: string; state?: string; error?: string } {
    const urlParams = new URLSearchParams(window.location.search);
    
    return {
      code: urlParams.get('code') || undefined,
      state: urlParams.get('state') || undefined,
      error: urlParams.get('error') || undefined,
    };
  }

  /**
   * Nettoyer les paramètres de callback de l'URL
   */
  static cleanupCallbackParams(): void {
    const url = new URL(window.location.href);
    url.searchParams.delete('code');
    url.searchParams.delete('state');
    url.searchParams.delete('error');
    url.searchParams.delete('scope');
    
    // Remplacer l'URL sans recharger la page
    window.history.replaceState({}, document.title, url.toString());
  }

  /**
   * Gérer les erreurs d'authentification Google
   */
  static handleAuthError(error: GoogleAuthResponse): string {
    switch (error.error_type) {
      case 'user_not_found':
        return 'Aucun compte n\'existe avec cette adresse email. Veuillez d\'abord créer un compte sur notre plateforme.';
      
      case 'profile_incomplete':
        return 'Votre compte existe mais votre inscription n\'est pas complète. Veuillez vous connecter avec votre mot de passe pour terminer votre profil.';
      
      case 'server_error':
        return 'Une erreur serveur s\'est produite. Veuillez réessayer plus tard.';
      
      default:
        return error.message || 'Une erreur inattendue s\'est produite lors de l\'authentification Google.';
    }
  }
}

export default GoogleAuthService;
