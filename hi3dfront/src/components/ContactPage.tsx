import React, { useState, useEffect } from 'react';
import PageContact from './PageContact'; // Corrigez l'importation
import Footer from './Footer';
import NavBarProfil from './NavBarProfil';
import { useProfileWizard } from '../context/ProfileWizardContext';
import ContactTabs from './ContactTabs'; // Importer le composant EditProfile

const ContactPage: React.FC = () => {
  const { isProfileWizardOpen } = useProfileWizard();
  const [isEditingProfile, setIsEditingProfile] = useState(false); // État pour basculer entre Projet et EditProfile
  const [completionPercentage, setCompletionPercentage] = useState(0); // État pour stocker le pourcentage de complétion

  return (
    <>
      <NavBarProfil />
      <div className="relative w-full bg-white border border-gray-300 rounded-lg shadow-lg">
        {/* Bannière avec image de fond */}
        <div
          className="w-full h-[200px] md:h-[250px] bg-gray-800 bg-cover bg-center relative"
          style={{
            backgroundImage:
              "url('https://plus.unsplash.com/premium_photo-1673290569880-5e8ae12f5ff8?q=80&w=1932&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')",
          }}
        >
          {/* Overlay noir semi-transparent */}
          <div className="absolute inset-0 bg-black/50"></div>

          {/* Contenu centré au-dessus de l'overlay */}
          <div className="relative z-10 flex flex-col items-center justify-center h-full text-white text-center">
            {/* Icône SVG */}
            <div className="w-12 h-12 flex items-center justify-center bg-gray-600 rounded-full mb-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 42.672 42.672"
                className="w-6 h-6 fill-white"
              >
                <path d="M31.654,10.075C19.884,10.075,10,19.322,10,31.092A21.9,21.9,0,0,0,31.654,52.747c11.77,0,21.017-9.884,21.017-21.654A20.807,20.807,0,0,0,31.654,10.075Zm10.19,24.2L31.654,43.83c-.156.156.22.637,0,.637s-.481-.481-.637-.637l-10.19-9.553c-.24-.239-.132-.325,0-.637s.3-.643.637-.637h6.369V18.992c0-.458.816-.637,1.274-.637h4.458c.458,0,1.274.179,1.274.637V33h6.369c.333,0,.512.328.637.637S42.081,34.041,41.845,34.277Z" transform="translate(-10 -10.075)"></path>
              </svg>
            </div>
            {/* Texte en gras */}
            <p className="text-sm font-bold">Ajouter une image de bannière</p>
            <p className="text-xs font-bold text-gray-300">
              Dimensions optimales 3200 x 410px
            </p>
          </div>
        </div>

        {/* Avatar plus grand et légèrement déplacé */}
        <div className="absolute bottom-[-60px] left-16 w-[120px] h-[120px] rounded-full border-[4px] border-white shadow-lg overflow-hidden">
          <img
            src="https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
            alt="Avatar"
            className="w-full h-full object-cover"
          />
        </div>
      </div>

      {/* Layout en deux colonnes */}
      <div className="grid grid-cols-4 gap-6 p-6">
        {/* ProfileDetails occupe 1 colonne */}
        <div className="col-span-1">
          <PageContact
            // setIsEditingProfile={setIsEditingProfile}
            // setCompletionPercentage={setCompletionPercentage} // Passer la fonction de rappel
          />
        </div>

        {/* Afficher EditProfile ou Projet en fonction de l'état */}
        <div className="col-span-3">
        <ContactTabs />
        </div>
      </div>

      {/* Footer */}
      <Footer />

    </>
  );
};

export default ContactPage;