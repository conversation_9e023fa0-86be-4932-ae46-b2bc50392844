// components/ServiceCreateModal.tsx
"use client";
import React, { useEffect, useState } from "react";
import Modal from "./Modal";
import { API_BASE_URL } from "../config";
import { CATEGORIES } from "./categories";

type ServiceValues = {
  title: string; // maps to previous "Service name"
  description: string; // maps to previous "About this Service"
  price: string;
  price_unit: "par image" | "par m2" | "par projet" | "";
  categories: string[];
  execution_time: string;
  concepts: string;
  revisions: string;
  is_private: boolean;
  status: "published" | "draft" | "pending" | "";
  files: File[];
  associated_project: string;
  existing_files?: string[];
  // legacy UI-only fields (not sent to backend)
  whatYouGet?: string;
  whoIsFor?: string;
  deliveryMethod?: string;
  whyChooseMe?: string;
  duration?: string;
};

export default function ServiceCreateModal({
  open,
  onClose,
  onCompleted,
  mode = "create",
  initialService,
  title = "Create a new service",
}: {
  open: boolean;
  onClose: () => void;
  onCompleted?: () => void;
  mode?: "create" | "edit";
  initialService?: Partial<ServiceValues> & { id?: number } & any;
  title?: string;
}) {
  const token = typeof window !== "undefined" ? localStorage.getItem("token") : null;
  const isAuthenticated = !!token;

  title = mode === "edit" ? "Edit service" : "Create a new service";

  const [values, setValues] = useState<ServiceValues>({
    title: "",
    description: "",
    price: "",
    price_unit: "",
    categories: [],
    execution_time: "",
    concepts: "",
    revisions: "",
    is_private: false,
    status: "published",
    files: [],
    associated_project: "",
    existing_files: [],
    whatYouGet: "",
    whoIsFor: "",
    deliveryMethod: "",
    whyChooseMe: "",
    duration: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filePreviews, setFilePreviews] = useState<string[]>([]);

  const executionTimeOptions = [
    { value: '', label: 'Sélectionnée une période' },
    { value: 'Moins de une semaine', label: 'Moins de une semaine' },
    { value: 'Une à deux semaine', label: 'Une à deux semaine' },
    { value: 'Un mois', label: 'Un mois' },
    { value: 'Deux mois', label: 'Deux mois' },
    { value: 'Trois mois', label: 'Trois mois' },
  ];

  // Fonction pour gérer la sélection/désélection des catégories
  const toggleCategory = (category: string) => {
    setValues((v) => {
      const isSelected = v.categories.includes(category);
      if (isSelected) {
        return {
          ...v,
          categories: v.categories.filter((c) => c !== category),
        };
      } else {
        return {
          ...v,
          categories: [...v.categories, category],
        };
      }
    });
  };

  useEffect(() => {
    if (open) {
      setError(null);
      if (mode === "edit" && initialService) {
        setValues({
          title: (initialService.title as string) ?? "",
          description: (initialService.description as string) ?? "",
          price: initialService.price?.toString?.() ?? "",
          price_unit: (initialService.price_unit as any) ?? "",
          categories: (initialService.categories as any) ?? [],
          execution_time: (initialService.execution_time as string) ?? "",
          concepts: (initialService.concepts as string) ?? "",
          revisions: (initialService.revisions as string) ?? "",
          is_private: Boolean(initialService.is_private),
          status: (initialService.status as any) ?? "published",
          files: [],
          associated_project: (initialService.associated_project as string) ?? "",
          existing_files: (initialService.file_urls as string[]) || [],
          // Support both camelCase (frontend) and snake_case (backend) for edit mode
          whatYouGet: (initialService as any).whatYouGet || (initialService as any).what_you_get || "",
          whoIsFor: (initialService as any).whoIsFor || (initialService as any).who_is_this_for || "",
          deliveryMethod: (initialService as any).deliveryMethod || (initialService as any).delivery_method || "",
          whyChooseMe: (initialService as any).whyChooseMe || (initialService as any).why_choose_me || "",
          duration: initialService.duration || "",
        });
        setFilePreviews((initialService.file_urls as string[]) || []);
      } else {
        setValues((v) => ({ ...v, title: "", description: "", price: "", price_unit: "", categories: [], execution_time: "", concepts: "", revisions: "", is_private: false, status: "published", files: [], associated_project: "", existing_files: [], whatYouGet: "", whoIsFor: "", deliveryMethod: "", whyChooseMe: "", duration: "" }));
        setFilePreviews([]);
      }
    }
  }, [open, mode, initialService]);

  const handleFiles = (fl: FileList | null) => {
    if (!fl) return;
    const next = Array.from(fl);
    setValues((v) => ({ ...v, files: next }));
    setFilePreviews(next.map((f) => URL.createObjectURL(f)));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isAuthenticated) {
      setError("You must be logged in.");
      return;
    }

    if (!values.title.trim()) return setError("Title is required");
    if (!values.price || isNaN(Number(values.price))) return setError("Valid price is required");
    if (!values.price_unit) return setError("Price unit is required");
    if (!values.categories || values.categories.length === 0) return setError("At least one category is required");
    if (!String(values.execution_time).trim()) return setError("Execution time is required");
    if (!String(values.concepts).trim()) return setError("Concepts is required");
    if (!String(values.revisions).trim()) return setError("Revisions is required");
    if (!values.status) return setError("Status is required");

    try {
      setLoading(true);
      setError(null);
      const endpoint =
        mode === "edit" && initialService?.id
          ? `${API_BASE_URL}/api/service-offers/${initialService.id}`
          : `${API_BASE_URL}/api/service-offers`;

      const form = new FormData();
      form.append("title", values.title.trim());
      if (values.description) form.append("description", values.description.trim());
      form.append("price", values.price.toString());
      form.append("price_unit", values.price_unit);
      values.categories.forEach((c) => form.append("categories[]", c));
      form.append("execution_time", String(values.execution_time).trim());
      form.append("concepts", String(values.concepts).trim());
      form.append("revisions", String(values.revisions).trim());
      form.append("is_private", values.is_private ? "1" : "0");
      form.append("status", values.status);
      if (values.associated_project) form.append("associated_project", values.associated_project.trim());
      // Map rich text fields to backend names
      if (values.whatYouGet) form.append("what_you_get", values.whatYouGet);
      if (values.whoIsFor) form.append("who_is_this_for", values.whoIsFor);
      if (values.deliveryMethod) form.append("delivery_method", values.deliveryMethod);
      if (values.whyChooseMe) form.append("why_choose_me", values.whyChooseMe);

      values.files.forEach((f) => form.append("files[]", f));
      (values.existing_files || []).forEach((p) => form.append("files[]", p));

      const res = await fetch(endpoint, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: form,
      });
      if (!res.ok) {
        const text = await res.text();
        throw new Error(text || "Request failed");
      }
      onCompleted?.();
      onClose();
    } catch (err: any) {
      setError(err?.message || "Failed to submit");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose} title={title} maxWidthClass="max-w-3xl">
      {error && <div className="mb-3 text-sm text-red-600">{error}</div>}
      <form onSubmit={handleSubmit} className="mt-2">
        <div className="max-h-[75vh] overflow-y-auto pr-2 space-y-6">
          {/* Service name (maps to title) */}
          <div className="space-y-2 mt-4">
            <label htmlFor="svc-name" className="block"
              style={{
                fontSize: "16px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 600,
                color: "#3d3d3dff"
              }}
            >Service name</label>
            <input
              id="svc-name"
              type="text"
              placeholder="Service Name"
              className="w-full rounded-2xl border border-gray-200 bg-white px-4 py-3 outline-none focus:ring-2"
              value={values.title}
              onChange={(e) => setValues((v) => ({ ...v, title: e.target.value }))}
              required
              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"
              }}
            />
          </div>

          {/* About this Service (maps to description) */}
          <div className="space-y-2">
            <label htmlFor="svc-about" className="block text-sm font-medium"
              style={{
                fontSize: "16px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 600,
                color: "#3d3d3dff"
              }}>About this Service</label>
            <textarea
              id="svc-about"
              rows={6}
              placeholder="Describe Your Service In Detail. More Detail Attracts More Sales."
              className="w-full rounded-2xl border border-gray-200 bg-gray-50 px-4 py-3 outline-none placeholder:text-gray-400"
              value={values.description}
              onChange={(e) => setValues((v) => ({ ...v, description: e.target.value }))}
              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"
              }}
            />
          </div>

          {/* Pricing + Unit (side by side) */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="svc-price" className="block text-sm font-medium"
                style={{
                  fontSize: "16px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 600,
                  color: "#484848"
                }}>Pricing</label>
              <input
                id="svc-price"
                type="number"
                min="0"
                step="0.01"
                placeholder="Price In USD"
                autoComplete="off"
                className="w-full rounded-2xl border border-gray-200 bg-white px-4 py-3 outline-none"
                value={values.price}
                onChange={(e) => setValues((v) => ({ ...v, price: e.target.value }))}
                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                  color: "#3d3d3dff",
                  backgroundColor: "#F6F7F8"
                }}
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium"
                style={{
                  fontSize: "16px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 600,
                  color: "#484848"
                }}>Price unit</label>
              <select
                className="w-full rounded-2xl border border-gray-200 bg-white px-4 py-3 outline-none "
                value={values.price_unit}
                onChange={(e) => setValues((v) => ({ ...v, price_unit: e.target.value as any }))
                }
                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                  color: "#3d3d3dff",
                  backgroundColor: "#F6F7F8"
                }}
              >
                <option value="">Select unit</option>
                <option value="par image">par image</option>
                <option value="par m2">par m2</option>
                <option value="par projet">par projet</option>
              </select>
            </div>
          </div>

          {/* Images (Files) - keep early in the form like original */}
          <div className="space-y-2">
            <label className="block text-sm font-medium"
              style={{
                fontSize: "16px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 600,
                color: "#3d3d3dff"
              }}>Add images / max 6</label>
            <input type="file" multiple onChange={(e) => handleFiles(e.target.files)}
              className="w-full rounded-2xl"
              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"

              }}
            />
            {values.existing_files && values.existing_files.length > 0 && values.files.length === 0 && (
              <div className="text-xs text-gray-500 whitespace-pre-wrap">Existing files:\n{(values.existing_files || []).join("\n")}</div>
            )}
            {filePreviews.length > 0 && (
              <div className="mt-2 grid grid-cols-3 gap-2">
                {filePreviews.map((src, idx) => (
                  <img key={idx} src={src} alt={`file-${idx}`} className="rounded-md h-24 w-full object-cover" />
                ))}
              </div>
            )}
            <p
              style={{
                fontSize: "15px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 600,
                color: "#717070ff",
              }}
            >
              Images should not exceed 1 MB in size. The ideal dimensions are 1200×800, and the preferred format is WebP. We will feature artists who convert their images before uploading them. Please add **only good-quality images**. You can use a free online tool such as https://www.iloveimg.com to both convert your images to WebP format **and** resize them to the correct dimensions before uploading.
            </p>
          </div>

          {/* Categories (inserted after images) */}
          <div className="space-y-2">
            <label className="block text-sm font-medium"
              style={{
                fontSize: "16px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 600,
                color: "#3d3d3dff"
              }}>Categories</label>

            <div className="flex flex-wrap gap-2">
              {CATEGORIES.map((category) => {
                const isSelected = values.categories.includes(category);
                return (
                  <button
                    key={category}
                    type="button"
                    onClick={() => toggleCategory(category)}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${isSelected
                      ? "bg-[#0D63F3] text-white"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                      }`}
                    style={{
                      fontFamily: "'Inter', sans-serif",
                    }}
                  >
                    {category}
                  </button>
                );
              })}
            </div>

            <div className="text-xs text-gray-500">
              Cliquez pour sélectionner/désélectionner les catégories
            </div>
          </div>


          {/* Legacy two-column textareas (UI only, not sent) */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block text-sm font-medium"
                style={{
                  fontSize: "16px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 600,
                  color: "#3d3d3dff"
                }}>What You Get ?</label>
              <textarea rows={3} className="w-full rounded-2xl border border-gray-200 bg-gray-50 px-4 py-3 outline-none focus:ring-2 " value={values.whatYouGet} onChange={(e) => setValues((v) => ({ ...v, whatYouGet: e.target.value }))}

                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                  color: "#3d3d3dff",
                  backgroundColor: "#F6F7F8"
                }} />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium"
                style={{
                  fontSize: "16px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 600,
                  color: "#3d3d3dff"
                }}>Who is this product for ?</label>
              <textarea rows={3} className="w-full rounded-2xl border border-gray-200 bg-gray-50 px-4 py-3 outline-none  placeholder:text-gray-400" value={values.whoIsFor} onChange={(e) => setValues((v) => ({ ...v, whoIsFor: e.target.value }))}
                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                  color: "#3d3d3dff",
                  backgroundColor: "#F6F7F8"
                }}
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium"
                style={{
                  fontSize: "16px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 600,
                  color: "#3d3d3dff"
                }}>The delivery method ?</label>
              <textarea rows={3} className="w-full rounded-2xl border border-gray-200 bg-gray-50 px-4 py-3 outline-none  placeholder:text-gray-400" value={values.deliveryMethod} onChange={(e) => setValues((v) => ({ ...v, deliveryMethod: e.target.value }))}
                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                  color: "#3d3d3dff",
                  backgroundColor: "#F6F7F8"
                }}
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium"
                style={{
                  fontSize: "16px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 600,
                  color: "#3d3d3dff"
                }}>Why Choose Me ?</label>
              <textarea rows={3} className="w-full rounded-2xl border border-gray-200 bg-gray-50 px-4 py-3 outline-none  placeholder:text-gray-400" value={values.whyChooseMe} onChange={(e) => setValues((v) => ({ ...v, whyChooseMe: e.target.value }))}
                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                  color: "#3d3d3dff",
                  backgroundColor: "#F6F7F8"
                }}
              />
            </div>
          </div>

          {/* Three small inputs - map to backend fields (execution_time / concepts / revisions) */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            {/* <div className="space-y-2">
              <label className="block text-sm font-medium">Execution time</label>
              <input type="text" className="w-full rounded-2xl border border-gray-200 bg-white px-4 py-3 outline-none " value={values.execution_time} onChange={(e) => setValues((v) => ({ ...v, execution_time: e.target.value }))} />
            </div> */}

            <div className="space-y-2">
              <label htmlFor="ach-execution_time" className="block text-sm font-medium"
                style={{
                  fontSize: "16px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 600,
                  color: "#3d3d3dff"
                }}>
                Execution time
              </label>
              <select
                id="ach-execution_time"
                className="w-full rounded-xl border border-gray-300 bg-white px-4 py-3 outline-none "
                value={values.execution_time || ""}
                onChange={(e) => setValues((v) => ({ ...v, execution_time: e.target.value }))}
                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                  color: "#3d3d3dff",
                  backgroundColor: "#F6F7F8"
                }}
              >
                {executionTimeOptions.map((c) => (
                  <option key={c.value} value={c.value}>{c.label}</option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <label className="block text-sm font-medium"
                style={{
                  fontSize: "16px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 600,
                  color: "#3d3d3dff"
                }}>Number of Conception</label>
              <input type="text" className="w-full rounded-2xl border border-gray-200 bg-white px-4 py-3 outline-none " value={values.concepts} onChange={(e) => setValues((v) => ({ ...v, concepts: e.target.value }))}

                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                  color: "#3d3d3dff",
                  backgroundColor: "#F6F7F8"
                }}
              />
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium"
                style={{
                  fontSize: "16px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 600,
                  color: "#3d3d3dff"
                }}>Number of Revision</label>
              <input type="text" className="w-full rounded-2xl border border-gray-200 bg-white px-4 py-3 outline-none " value={values.revisions} onChange={(e) => setValues((v) => ({ ...v, revisions: e.target.value }))}
                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                  color: "#3d3d3dff",
                  backgroundColor: "#F6F7F8"
                }}
              />
            </div>
          </div>

          {/* Optional Duration (legacy position kept) */}
          <div className="space-y-2">
            <label className="block text-sm font-medium"
              style={{
                fontSize: "16px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 600,
                color: "#3d3d3dff"
              }}>Duration (optional)</label>
            <input type="text" className="w-full rounded-2xl border border-gray-200 bg-white px-4 py-3 outline-none " value={values.duration} onChange={(e) => setValues((v) => ({ ...v, duration: e.target.value }))}

              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"
              }}
            />
          </div>

          {/* Privacy & Status */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <label className="inline-flex items-center gap-2 text-sm">
              <input type="checkbox" checked={values.is_private} onChange={(e) => setValues((v) => ({ ...v, is_private: e.target.checked }))}
                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                  color: "#3d3d3dff",
                  backgroundColor: "#F6F7F8"
                }}
              />
              <span>Private</span>
            </label>
            <div className="space-y-2">
              <label className="block text-sm font-medium"
                style={{
                  fontSize: "16px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 600,
                  color: "#3d3d3dff"
                }}>Status</label>
              <select className="w-full rounded-2xl border border-gray-200 bg-white px-4 py-3 outline-none " value={values.status} onChange={(e) => setValues((v) => ({ ...v, status: e.target.value as any }))}
                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                  color: "#3d3d3dff",
                  backgroundColor: "#F6F7F8"
                }}>
                <option value="published">published</option>
                <option value="draft">draft</option>
                <option value="pending">pending</option>
              </select>
            </div>
          </div>

          {/* Associated project */}
          <div className="space-y-2">
            <label className="block text-sm font-medium"
              style={{
                fontSize: "16px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 600,
                color: "#3d3d3dff"
              }}>Associated project (optional)</label>
            <input type="text" className="w-full rounded-2xl border border-gray-200 bg-white px-4 py-3 outline-none " value={values.associated_project} onChange={(e) => setValues((v) => ({ ...v, associated_project: e.target.value }))}
              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"
              }}
            />
          </div>
        </div>

        {/* footer bouton */}
        <div className="pt-5">
          <button type="submit" disabled={loading || !isAuthenticated} className="w-full bg-[#0D63F3] px-6 py-3 text-white  disabled:opacity-70"
            style={{
              fontSize: "18px",
              fontFamily: "'Inter', sans-serif",
              fontWeight: 400,
              borderRadius: "14px"
            }}
          >
            {loading ? "Saving..." : mode === "edit" ? "Save changes" : "Create it now"}
          </button>
        </div>
      </form>
    </Modal>
  );
}
