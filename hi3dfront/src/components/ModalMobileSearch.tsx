import React, { useState } from "react";

interface ModalMobileSearchProps {
  isOpen: boolean;
  onClose: () => void;
}

const ModalMobileSearch: React.FC<ModalMobileSearchProps> = ({
  isOpen,
  onClose,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Logique de recherche ici
    console.log("Recherche:", searchQuery);
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  // Fonction pour masquer les filtres
  const hideFilters = () => {
    setShowFilters(false);
  };

  return (
    <div
      className="fixed px-3 pt-3 inset-0 bg-black bg-opacity-50 flex flex-col items-center z-50"
      onClick={onClose}
      style={{boxShadow: "box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.28)"}}
    >
      {/* Premier bloc avec barre de recherche */}
      <div
        className="bg-[#F6F7F8] p-4 relative"
        style={{
          width: "100%",
          height: "128px",
          borderRadius: "16px",
          boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.28)",
          
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <form onSubmit={handleSubmit} className="relative w-full">
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Type to find your 3D artist do you need?"
            className="w-full pl-4 pr-10 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm placeholder-black"
            style={{
              fontFamily: "Inter, sans-serif",
              borderRadius: "18px",
              fontWeight: "300",
              fontSize: "14px",
              color: "#0D0C22",
            }}
          />

          {/* Icône de recherche à droite */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 11 10"
              fill="none"
            >
              <g clipPath="url(#clip0_201_12841)">
                <path
                  d="M9.35791 8.17308L6.99288 5.80766C7.37401 5.2839 7.57904 4.65267 7.57842 4.00491C7.57842 2.30947 6.199 0.930054 4.50356 0.930054C2.80813 0.930054 1.42871 2.30947 1.42871 4.00491C1.42871 5.70035 2.80813 7.07976 4.50356 7.07976C5.15132 7.08039 5.78256 6.87536 6.30631 6.49422L8.67173 8.85926L9.35791 8.17308ZM4.50356 6.10862C4.08744 6.10866 3.68064 5.9853 3.33462 5.75413C2.98861 5.52297 2.71892 5.19438 2.55965 4.80994C2.40039 4.42549 2.35871 4.00245 2.43988 3.59431C2.52106 3.18618 2.72144 2.81128 3.01569 2.51703C3.30993 2.22278 3.68483 2.0224 4.09297 1.94123C4.5011 1.86005 4.92415 1.90173 5.30859 2.061C5.69304 2.22026 6.02162 2.48995 6.25279 2.83597C6.48395 3.18198 6.60732 3.58878 6.60728 4.00491C6.60663 4.56265 6.38477 5.09735 5.99039 5.49174C5.59601 5.88612 5.0613 6.10797 4.50356 6.10862Z"
                  fill="black"
                />
              </g>
              <defs>
                <clipPath id="clip0_201_12841">
                  <rect
                    width="9.75902"
                    height="9.75902"
                    fill="white"
                    transform="translate(0.513672 0.0150146)"
                  />
                </clipPath>
              </defs>
            </svg>
          </div>
        </form>

        {/* Conteneur pour les icônes en bas */}
        <div className="flex justify-between items-center mt-2 px-1">
          {/* Icône à gauche avec gestion du clic */}
          <div
            className="bg-white p-1 flex items-center cursor-pointer"
            style={{ borderRadius: "14px", marginTop: "18px" }}
            onClick={toggleFilters}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="20"
              viewBox="0 0 24 31"
              fill="none"
              style={{
                transform: showFilters ? "rotate(90deg)" : "rotate(0deg)",
                transition: "transform 0.3s ease",
              }}
            >
              <path
                d="M0.698242 22.1836C0.698242 21.8954 0.943744 21.6499 1.23194 21.6499H5.91427C6.20247 21.6499 6.44797 21.8954 6.44797 22.1836V23.9342C6.44797 24.2224 6.20247 24.4679 5.91427 24.4679H1.23194C0.943744 24.4679 0.698242 24.2224 0.698242 23.9342V22.1836Z"
                fill="#212121"
              ></path>
              <path
                d="M3.12124 0.632812H4.02497C4.56579 0.632812 5.00342 1.07045 5.00342 1.61126V19.9456H2.14279V1.61126C2.14279 1.07045 2.58043 0.632812 3.12124 0.632812Z"
                fill="#212121"
              ></path>
              <path
                d="M4.02497 30.4133H3.12124C2.58043 30.4133 2.14279 29.9757 2.14279 29.4348V26.1686H5.00342V29.4348C5.00342 29.9757 4.56579 30.4133 4.02497 30.4133Z"
                fill="#212121"
              ></path>
              <path
                d="M12.6211 30.4133H11.7174C11.1766 30.4133 10.7389 29.9757 10.7389 29.4348V18.4442H13.5996V29.4348C13.5996 29.9757 13.1619 30.4133 12.6211 30.4133Z"
                fill="#212121"
              ></path>
              <path
                d="M9.20187 14.4556C9.20187 14.1674 9.44737 13.9219 9.73557 13.9219H14.4179C14.7061 13.9219 14.9516 14.1674 14.9516 14.4556V16.2062C14.9516 16.4944 14.7061 16.7399 14.4179 16.7399H9.73557C9.44737 16.7399 9.20187 16.4944 9.20187 16.2062V14.4556Z"
                fill="#212121"
              ></path>
              <path
                d="M11.7174 0.632812H12.6211C13.1619 0.632812 13.5996 1.07045 13.5996 1.61126V12.2212H10.7389V1.61126C10.7389 1.07045 11.1766 0.632812 11.7174 0.632812Z"
                fill="#212121"
              ></path>
              <path
                d="M21.5624 30.4133H20.6586C20.1178 30.4133 19.6802 29.9757 19.6802 29.4348V10.9012H22.5408V29.4348C22.5408 29.9757 22.1032 30.4133 21.5624 30.4133Z"
                fill="#212121"
              ></path>
              <path
                d="M18.2356 6.91269C18.2356 6.62449 18.4811 6.37899 18.7693 6.37899H23.4517C23.7399 6.37899 23.9854 6.62449 23.9854 6.91269V8.66322C23.9854 8.95142 23.7399 9.19692 23.4517 9.19692H18.7693C18.4811 9.19692 18.2356 8.95142 18.2356 8.66322V6.91269Z"
                fill="#212121"
              ></path>
              <path
                d="M20.6586 0.632812H21.5624C22.1032 0.632812 22.5408 1.07045 22.5408 1.61126V4.67826H19.6802V1.61126C19.6802 1.07045 20.1178 0.632812 20.6586 0.632812Z"
                fill="#212121"
              ></path>
            </svg>
          </div>

          {/* Icône à droite avec gestion du clic pour fermer la modal */}
          <div
            className="p-1 flex items-center cursor-pointer"
            style={{ borderRadius: "14px", marginTop: "18px" }}
            onClick={onClose}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 29 29"
              fill="none"
            >
              <g clipPath="url(#clip0_201_99288)">
                <path
                  d="M4.40454 17.6777C4.20532 17.8652 4.08813 18.1347 4.08813 18.4394C4.08813 19.0488 4.54517 19.5058 5.15454 19.5058C5.45922 19.5058 5.74047 19.4004 5.91625 19.2011L14.8928 10.0371H13.6389L22.6154 19.2011C22.8029 19.4004 23.0842 19.5058 23.3772 19.5058C23.9865 19.5058 24.4436 19.0488 24.4436 18.4394C24.4436 18.1347 24.3264 17.8652 24.1272 17.6777L15.0569 8.39648C14.8576 8.17383 14.5647 8.04492 14.2717 8.04492C13.967 8.04492 13.6858 8.17383 13.4749 8.39648L4.40454 17.6777Z"
                  fill="black"
                  fillOpacity="0.85"
                ></path>
              </g>
              <defs>
                <clipPath id="clip0_201_99288">
                  <rect
                    width="20.3555"
                    height="12.4102"
                    fill="white"
                    transform="translate(4.08813 8.04492)"
                  ></rect>
                </clipPath>
              </defs>
            </svg>
          </div>
        </div>
      </div>

      {/* Deuxième bloc avec margin-top de 5px - conditionnellement affiché */}
      {showFilters && (
        <div
          className="bg-[#F6F7F8] mt-2 p-4"
          style={{
            width: "100%",
            height: "390px",
            borderRadius: "16px",
            boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.28)",
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Contenu du deuxième bloc */}
          <div className="space-y-4">
            <h3
              className="text-sm font-medium mb-2"
              style={{
                fontFamily: "Inter, sans-serif",
                fontSize: "12px",
                color: "#333333",
              }}
            >
              Pays
            </h3>
            <div>
              <select
                className="w-full p-2 border rounded-md text-sm"
                style={{
                  borderRadius: "16px",
                  fontFamily: "Inter, sans-serif",
                  fontSize: "12px",
                  border: "none",
                }}
              >
                <option>Sélectionner le pays</option>
              </select>
            </div>

            <div>
              <h3
                className="text-sm font-medium mb-2"
                style={{
                  fontFamily: "Inter, sans-serif",
                  fontSize: "12px",
                  color: "#333333",
                }}
              >
                Language
              </h3>
              <select
                className="w-full p-2 border rounded-md text-sm"
                style={{
                  borderRadius: "16px",
                  fontFamily: "Inter, sans-serif",
                  fontSize: "12px",
                  border: "none",
                }}
              >
                <option>Select -</option>
              </select>
            </div>

            <div>
              <h3
                className="text-sm font-medium mb-2"
                style={{
                  fontFamily: "Inter, sans-serif",
                  fontSize: "12px",
                  color: "#333333",
                }}
              >
                Price range
              </h3>
              <input
                type="text"
                className="w-full p-2  rounded-md text-sm"
                style={{
                  borderRadius: "16px",
                  fontFamily: "Inter, sans-serif",
                  fontSize: "12px",
                }}
              />
            </div>

            <div>
              <h3
                className="text-sm font-medium mb-2 text-gray-700"
                style={{
                  fontFamily: "Inter, sans-serif",
                  fontSize: "12px",
                  color: "#333333",
                }}
              >
                Type of user
              </h3>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="pro-user"
                  className="mr-2 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label
                  htmlFor="pro-user"
                  className="text-gray-700 text-sm"
                  style={{ fontFamily: "Inter, sans-serif" }}
                >
                  PRO USER
                </label>
              </div>
            </div>

            {/* Conteneur pour le bouton Filter et l'icône à droite */}
            <div className="flex justify-between items-center">
              {/* Filter Button en bas à gauche */}
              <button
                className="bg-blue-600 text-white py-2 px-6 hover:bg-blue-700 transition-colors font-medium text-sm"
                style={{
                  fontFamily: "Inter, sans-serif",
                  borderRadius: "18px",
                }}
              >
                Filter
              </button>

              {/* Icône à droite avec gestion du clic pour masquer les filtres */}
              <div
                className="p-1 flex items-center cursor-pointer"
                style={{ borderRadius: "14px" }}
                onClick={hideFilters}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 29 29"
                  fill="none"
                >
                  <g clipPath="url(#clip0_201_99288)">
                    <path
                      d="M4.40454 17.6777C4.20532 17.8652 4.08813 18.1347 4.08813 18.4394C4.08813 19.0488 4.54517 19.5058 5.15454 19.5058C5.45922 19.5058 5.74047 19.4004 5.91625 19.2011L14.8928 10.0371H13.6389L22.6154 19.2011C22.8029 19.4004 23.0842 19.5058 23.3772 19.5058C23.9865 19.5058 24.4436 19.0488 24.4436 18.4394C24.4436 18.1347 24.3264 17.8652 24.1272 17.6777L15.0569 8.39648C14.8576 8.17383 14.5647 8.04492 14.2717 8.04492C13.967 8.04492 13.6858 8.17383 13.4749 8.39648L4.40454 17.6777Z"
                      fill="black"
                      fillOpacity="0.85"
                    ></path>
                  </g>
                  <defs>
                    <clipPath id="clip0_201_99288">
                      <rect
                        width="20.3555"
                        height="12.4102"
                        fill="white"
                        transform="translate(4.08813 8.04492)"
                      ></rect>
                    </clipPath>
                  </defs>
                </svg>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ModalMobileSearch;