import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Mail,
  Phone,
  MapPin,
  Briefcase,
  Building,
  Edit,
  Globe,
  Download,
  CheckCircle,
  FileText,
  User,
  Calendar
} from 'lucide-react';
import DashboardLayout from './DashboardLayout';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';
import Badge from '../ui/Badge';
import Tabs from '../ui/Tabs';
import { profileService } from '../../services/profileService';
import { getAvatarUrl, getInitials } from '../../utils/avatarUtils';
import './ProfileDashboard.css';

interface ClientProfileData {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  bio?: string;
  avatar?: string;
  birth_date?: string;
  company_name?: string;
  company_size?: string;
  industry?: string;
  position?: string;
  website?: string;
  social_links?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
    instagram?: string;
  };
  completion_percentage: number;
  created_at?: string;
  updated_at?: string;
}

const ClientProfileDashboard: React.FC = () => {
  const [profileData, setProfileData] = useState<ClientProfileData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          navigate('/login');
          return;
        }

        const { profile } = await profileService.getProfile();
        console.log('Client profile data from API:', profile);
        setProfileData(profile as unknown as ClientProfileData);
        setError(null);
      } catch (err) {
        console.error('Error fetching client profile data:', err);
        setError('Impossible de récupérer les données du profil. Veuillez réessayer plus tard.');
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [navigate]);

  const handleEditProfile = () => {
    navigate('/dashboard/client-profile/edit');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Non spécifié';

    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <div className="text-red-600 mb-4">{error}</div>
          <Button
            variant="primary"
            onClick={() => window.location.reload()}
          >
            Réessayer
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  if (!profileData) {
    return (
      <DashboardLayout>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
          <div className="text-yellow-700 mb-4">Aucune donnée de profil disponible. Veuillez compléter votre profil.</div>
          <Button
            variant="primary"
            onClick={handleEditProfile}
          >
            Compléter mon profil
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  // Utiliser la fonction utilitaire pour obtenir l'URL de l'avatar
  const getProfileAvatarUrl = () => {
    return getAvatarUrl(profileData?.avatar) || 'https://via.placeholder.com/150';
  };

  return (
    <DashboardLayout
      title="Mon Profil Client"
      subtitle="Consultez et gérez votre profil client"
      actions={
        <button
          type="button"
          onClick={handleEditProfile}
          className="edit-profile-button"
        >
          <Edit />
          Modifier mon profil
        </button>
      }
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left column - Profile summary */}
        <div className="lg:col-span-1 space-y-6">
          {/* Profile card */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="p-6 text-center">
              <div className="relative mx-auto mb-4">
                <Avatar
                  size="xl"
                  src={getProfileAvatarUrl()}
                  fallback={getInitials(profileData.first_name, profileData.last_name)}
                  className="mx-auto"
                />
              </div>

              <h3 className="text-xl font-semibold text-neutral-900">
                {profileData.first_name} {profileData.last_name}
              </h3>
              <p className="text-neutral-600 mt-1">
                {profileData.position ? `${profileData.position}` : 'Client'}
                {profileData.company_name ? ` chez ${profileData.company_name}` : ''}
              </p>

              <div className="mt-4 pt-4 border-t border-neutral-200">
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-neutral-600">Complétion du profil</span>
                  <span className="font-medium text-neutral-900">{profileData.completion_percentage}%</span>
                </div>
                <div className="w-full bg-neutral-200 rounded-full h-2">
                  <div
                    className="completion-progress-bar"
                    style={{ width: `${profileData.completion_percentage}%` }}
                  ></div>
                </div>
              </div>
            </div>

            <div className="bg-neutral-50 px-6 py-4 border-t border-neutral-200">
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <Mail className="h-4 w-4 text-neutral-500 mr-2" />
                  <span className="text-neutral-700">{profileData.email}</span>
                </div>
                <div className="flex items-center text-sm">
                  <Phone className="h-4 w-4 text-neutral-500 mr-2" />
                  <span className="text-neutral-700">{profileData.phone || 'Non renseigné'}</span>
                </div>
                <div className="flex items-center text-sm">
                  <MapPin className="h-4 w-4 text-neutral-500 mr-2" />
                  <span className="text-neutral-700">
                    {profileData.city && profileData.country
                      ? `${profileData.city}, ${profileData.country}`
                      : 'Localisation non renseignée'}
                  </span>
                </div>
                {profileData.company_name && (
                  <div className="flex items-center text-sm">
                    <Building className="h-4 w-4 text-neutral-500 mr-2" />
                    <span className="text-neutral-700">{profileData.company_name}</span>
                  </div>
                )}
                {profileData.website && (
                  <div className="flex items-center text-sm">
                    <Globe className="h-4 w-4 text-neutral-500 mr-2" />
                    <a 
                      href={profileData.website.startsWith('http') ? profileData.website : `https://${profileData.website}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary-600 hover:underline"
                    >
                      {profileData.website}
                    </a>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Account information */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h3 className="text-lg font-semibold text-neutral-900">Informations du compte</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Date de naissance</h3>
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-neutral-400 mr-2" />
                    <p className="text-neutral-900">{formatDate(profileData.birth_date || '')}</p>
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Membre depuis</h3>
                  <div className="flex items-center">
                    <User className="h-5 w-5 text-neutral-400 mr-2" />
                    <p className="text-neutral-900">{formatDate(profileData.created_at)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Social links */}
          {profileData.social_links && Object.values(profileData.social_links).some(link => link) && (
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <h2 className="text-lg font-semibold text-neutral-900">Réseaux sociaux</h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  {profileData.social_links.linkedin && (
                    <a
                      href={profileData.social_links.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-neutral-700 hover:text-primary-600"
                    >
                      <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                      </svg>
                      LinkedIn
                    </a>
                  )}
                  {profileData.social_links.twitter && (
                    <a
                      href={profileData.social_links.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-neutral-700 hover:text-primary-600"
                    >
                      <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                      </svg>
                      Twitter
                    </a>
                  )}
                  {profileData.social_links.facebook && (
                    <a
                      href={profileData.social_links.facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-neutral-700 hover:text-primary-600"
                    >
                      <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385h-3.047v-3.47h3.047v-2.642c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953h-1.514c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385c5.738-.9 10.126-5.864 10.126-11.854z"/>
                      </svg>
                      Facebook
                    </a>
                  )}
                  {profileData.social_links.instagram && (
                    <a
                      href={profileData.social_links.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-neutral-700 hover:text-primary-600"
                    >
                      <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                      </svg>
                      Instagram
                    </a>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Right column - Detailed information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Tabs avec accessibilité améliorée */}
          <Tabs
            tabs={[
              {
                id: 'overview',
                label: 'Vue d\'ensemble',
                content: (
                  <div className="space-y-6">
                    {/* Bio section */}
                    <div>
                      <h3 className="text-lg font-semibold text-neutral-900 mb-3">À propos de moi</h3>
                      <p className="text-neutral-700 whitespace-pre-line">
                        {profileData.bio || 'Aucune biographie renseignée. Ajoutez une bio pour vous présenter aux professionnels.'}
                      </p>
                    </div>

                    {/* Contact information */}
                    <div>
                      <h3 className="text-lg font-semibold text-neutral-900 mb-3">Coordonnées</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-neutral-50 p-4 rounded-lg">
                          <div className="flex items-center mb-2">
                            <Mail className="h-5 w-5 text-primary-600 mr-2" />
                            <span className="text-neutral-900 font-medium">Email</span>
                          </div>
                          <p className="text-neutral-700">{profileData.email}</p>
                        </div>
                        <div className="bg-neutral-50 p-4 rounded-lg">
                          <div className="flex items-center mb-2">
                            <Phone className="h-5 w-5 text-primary-600 mr-2" />
                            <span className="text-neutral-900 font-medium">Téléphone</span>
                          </div>
                          <p className="text-neutral-700">{profileData.phone || 'Non renseigné'}</p>
                        </div>
                        <div className="bg-neutral-50 p-4 rounded-lg">
                          <div className="flex items-center mb-2">
                            <MapPin className="h-5 w-5 text-primary-600 mr-2" />
                            <span className="text-neutral-900 font-medium">Adresse</span>
                          </div>
                          <p className="text-neutral-700">{profileData.address || 'Non renseignée'}</p>
                          <p className="text-neutral-700">
                            {profileData.city && profileData.country
                              ? `${profileData.city}, ${profileData.country}`
                              : ''}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ),
              },
              {
                id: 'company',
                label: 'Entreprise',
                content: (
                  <div>
                    <h3 className="text-lg font-semibold text-neutral-900 mb-4">Informations sur l'entreprise</h3>
                    {profileData.company_name ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="bg-white border border-neutral-200 rounded-lg p-4">
                          <h4 className="font-medium text-neutral-900 mb-2">Nom de l'entreprise</h4>
                          <p className="text-neutral-700">{profileData.company_name}</p>
                        </div>
                        {profileData.industry && (
                          <div className="bg-white border border-neutral-200 rounded-lg p-4">
                            <h4 className="font-medium text-neutral-900 mb-2">Secteur d'activité</h4>
                            <p className="text-neutral-700">{profileData.industry}</p>
                          </div>
                        )}
                        {profileData.company_size && (
                          <div className="bg-white border border-neutral-200 rounded-lg p-4">
                            <h4 className="font-medium text-neutral-900 mb-2">Taille de l'entreprise</h4>
                            <p className="text-neutral-700">{profileData.company_size}</p>
                          </div>
                        )}
                        {profileData.position && (
                          <div className="bg-white border border-neutral-200 rounded-lg p-4">
                            <h4 className="font-medium text-neutral-900 mb-2">Poste</h4>
                            <p className="text-neutral-700">{profileData.position}</p>
                          </div>
                        )}
                        {profileData.website && (
                          <div className="bg-white border border-neutral-200 rounded-lg p-4 md:col-span-2">
                            <h4 className="font-medium text-neutral-900 mb-2">Site web</h4>
                            <a
                              href={profileData.website.startsWith('http') ? profileData.website : `https://${profileData.website}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-primary-600 hover:underline"
                            >
                              {profileData.website}
                            </a>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-8 bg-neutral-50 rounded-lg">
                        <Building className="h-12 w-12 text-neutral-400 mx-auto mb-3" />
                        <h4 className="text-lg font-medium text-neutral-700 mb-2">Aucune information d'entreprise</h4>
                        <p className="text-neutral-500 mb-4">Ajoutez des informations sur votre entreprise pour aider les professionnels à mieux comprendre vos besoins.</p>
                        <button
                          type="button"
                          onClick={handleEditProfile}
                          className="services-action-button"
                        >
                          Ajouter des informations d'entreprise
                        </button>
                      </div>
                    )}
                  </div>
                ),
              },
            ]}
            defaultTab="overview"
          />
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ClientProfileDashboard;
