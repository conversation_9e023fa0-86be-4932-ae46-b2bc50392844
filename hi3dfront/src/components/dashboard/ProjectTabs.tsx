import React from "react";
import "./ProjectTabs.css";

interface ProjectTabsProps {
  activeTab?: "open" | "ongoing" | "completed" | "canceled" | "standby";
  onTabChange?: (
    tab: "open" | "ongoing" | "completed" | "canceled" | "standby"
  ) => void;
  projects: { status: string }[];
  isClientProfile?: boolean;
}

const ProjectTabs: React.FC<ProjectTabsProps> = ({
  activeTab = "open",
  onTabChange,
  projects,
  isClientProfile,
}) => {
  const counts = {
    open: projects.filter((p) => p.status === "open").length,
    ongoing: projects.filter((p) => p.status === "in_progress").length,
    completed: projects.filter((p) => p.status === "completed").length,
  };

  return (
    <div className="project-tabs-container">
      <div className="project-tabs">
        <button
          className={`border-none rounded-full py-1 md:py-2 px-3 md:px-6 font-sans font-medium text-xs md:text-sm h-8 md:h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm ${
            activeTab === "open"
              ? "bg-[#006eff] text-white hover:bg-[#006eff] hover:text-white"
              : "bg-[#006eff] text-white hover:bg-[#006eff] hover:text-white"
          }`}
          onClick={() => onTabChange && onTabChange("open")}
          style={{
            fontFamily: "'Inter', sans-serif",
            borderRadius: "20px",
            fontWeight: "400",
          }}
        >
          <span
            style={{
              fontFamily: "'Inter', sans-serif",
              fontSize: "8px",
              borderRadius: "50%",
              padding: "0px 2px",
              backgroundColor: "#131313ff",
              color: "white",
              fontWeight: "300",
              minWidth: "12px",
              width: "12px",
              height: "12px",
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              marginRight: "2px",
              lineHeight: "1",
            }}
          >
            {counts.open}
          </span>{" "}
          Open offers
        </button>
        <button
          className={`border-none rounded-full py-1 md:py-2 px-3 md:px-6 font-sans font-medium text-xs md:text-sm h-8 md:h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm ${
            activeTab === "ongoing"
              ? "bg-black text-white hover:bg-[#006eff] hover:text-white"
              : "bg-[#f1f2f6] text-[#484848] hover:bg-[#006eff] hover:text-white"
          }`}
          onClick={() => onTabChange && onTabChange("ongoing")}
          style={{
            fontFamily: "'Inter', sans-serif",
            borderRadius: "20px",
            fontWeight: "400",
          }}
        >
          <span
            style={{
              fontFamily: "'Inter', sans-serif",
              fontSize: "8px",
              borderRadius: "50%",
              padding: "0px 2px",
              backgroundColor: "#131313ff",
              color: "white",
              fontWeight: "300",
              minWidth: "12px",
              width: "12px",
              height: "12px",
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              marginRight: "2px",
              lineHeight: "1",
            }}
          >
            {counts.ongoing}
          </span>{" "}
          Ongoing
        </button>
        <button
          className={`border-none rounded-full py-1 md:py-2 px-3 md:px-6 font-sans font-medium text-xs md:text-sm h-8 md:h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm ${
            activeTab === "completed"
              ? "bg-black text-white hover:bg-[#006eff] hover:text-white"
              : "bg-[#f1f2f6] text-[#484848] hover:bg-[#006eff] hover:text-white"
          }`}
          onClick={() => onTabChange && onTabChange("completed")}
          style={{
            fontFamily: "'Inter', sans-serif",
            borderRadius: "20px",
            fontWeight: "400",
          }}
        >
          <span
            style={{
              fontFamily: "'Inter', sans-serif",
              fontSize: "8px",
              borderRadius: "50%",
              padding: "0px 2px",
              backgroundColor: "#131313ff",
              color: "white",
              fontWeight: "300",
              minWidth: "12px",
              width: "12px",
              height: "12px",
              display: "inline-flex",
              alignItems: "center",
              justifyContent: "center",
              marginRight: "2px",
              lineHeight: "1",
            }}
          >
            {counts.completed}
          </span>{" "}
          Completed
        </button>
        {isClientProfile && (
          <>
            <button
              className="border-none rounded-full py-1 md:py-2 px-3 md:px-6 font-sans font-medium text-xs md:text-sm h-8 md:h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm bg-[#f1f2f6] text-[#484848] hover:bg-[#006eff] hover:text-white"
              onClick={() => onTabChange && onTabChange("canceled" as any)}
              style={{
                fontFamily: "'Inter', sans-serif",
                borderRadius: "20px",
                fontWeight: "400",
              }}
            >
              Canceled
            </button>
            <button
              className="border-none rounded-full py-1 md:py-2 px-3 md:px-6 font-sans font-medium text-xs md:text-sm h-8 md:h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm bg-[#f1f2f6] text-[#484848] hover:bg-[#006eff] hover:text-white"
              onClick={() => onTabChange && onTabChange("standby" as any)}
              style={{
                fontFamily: "'Inter', sans-serif",
                borderRadius: "20px",
                fontWeight: "400",
              }}
            >
              Stand By
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default ProjectTabs;