import React from 'react';
import { MoreHorizontal } from 'lucide-react';
import Avatar from '../ui/Avatar';
import Button from '../ui/Button';
import Badge from '../ui/Badge';
import { getAvatarUrl, getInitials } from '../../utils/avatarUtils';

interface CivilityStateProps {
  profile: {
    first_name: string;
    last_name: string;
    city?: string;
    country?: string;
    avatar?: string;
  };
  onEditProfile: () => void;
}

const CivilityState: React.FC<CivilityStateProps> = ({ profile, onEditProfile }) => {
  const avatarUrl = getAvatarUrl(profile.avatar);
  const initials = getInitials(profile.first_name, profile.last_name);

  return (
    <div className="py-5 mt-19 mb-19">
    
        {/* Nouvelle ligne ajoutée ici */}
        <p 
        style={{ color: '#0c0c0cff', fontSize: '28px', fontFamily: "'Inter', sans-serif", fontWeight: 500 }}>
        Welcome to your project board</p>
        <h1 className="text-2xl  mb-2" style={{ color: '#0c0c0cff', fontSize: '20px', fontFamily: "'Inter', sans-serif", fontWeight: 500 }}>{profile.first_name} {profile.last_name}</h1>
        
    </div>
  );
};

export default CivilityState;