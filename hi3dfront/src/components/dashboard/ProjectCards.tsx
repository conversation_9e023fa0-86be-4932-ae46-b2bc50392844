import React from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../ui/Button';

interface ProjectCardsProps {
  projects: any[];
  activeTab: 'open' | 'ongoing' | 'completed';
}

const ProjectCards: React.FC<ProjectCardsProps> = ({ projects, activeTab }) => {
  const navigate = useNavigate();

  const getStatusFilter = () => {
    switch (activeTab) {
      case 'open':
        return 'open';
      case 'ongoing':
        return 'in_progress';
      case 'completed':
        return 'completed';
      default:
        return 'open';
    }
  };

  // Fonction pour nettoyer le texte des balises HTML
  const stripHtmlTags = (html: string) => {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
  };

  const filteredProjects = projects.filter(p => p.status === getStatusFilter());

  return (
    <div className="mt-12">
      {filteredProjects.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-xl font-semibold text-neutral-900 mb-4">
            {getStatusFilter() === "open" && "Aucun Appels d'offre ouvert"}
            {getStatusFilter() === "in_progress" && "Aucun projet en cours"}
            {getStatusFilter() === "completed" && "Aucun projet terminé récemment"}
          </div>
          <p className="text-neutral-500 mb-4">Commencez à chercher des projets qui correspondent à vos compétences.</p>
          <Button
            variant="primary"
            onClick={() => navigate('/dashboard/open-offers')}
          >
            Trouver des projets
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredProjects.map((project, idx) => (
            <div
              key={idx}
              className="bg-[#F5F5F5] rounded-lg p-4"
              style={{
                height: '123px'
              }}
              onClick={() => navigate(`/dashboard/offers/${project.id}`)}
            >
              <div className="flex flex-col h-full justify-between">
                <div className="flex justify-between items-start">
                  <h3 className="text-sm font-bold text-gray-800 line-clamp-1"
                  style={{ color: '#0c0c0cff', fontSize: '16px', fontFamily: "'Inter', sans-serif", fontWeight: 800 , lineHeight: "30px", letterSpacing: "0" }}>
                    {project.title}
                  </h3>
                </div>
                
                <div 
                  className="text-xs text-gray-600 line-clamp-2"  
                  style={{ 
                    color: '#0c0c0cff', 
                    fontSize: '10px', 
                    fontFamily: "'Inter', sans-serif", 
                    fontWeight: 300, 
                    lineHeight: "15px",
                    letterSpacing: "0",
                    marginTop: '4px',
                    marginBottom: '4px'
                  }}
                >
                  {stripHtmlTags(project.description)}
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="text-sm" style={{ color: '#0c0c0cff', fontSize: '12px', fontFamily: "'Inter', sans-serif", fontWeight: 800 , lineHeight: "30px", letterSpacing: "0" }}>
                    Budget {project.budget} usd
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProjectCards;