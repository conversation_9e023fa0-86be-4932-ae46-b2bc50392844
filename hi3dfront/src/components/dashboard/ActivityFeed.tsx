import React from 'react';
import ActivityItem, { ActivityItemProps } from './ActivityItem';

interface ActivityFeedProps {
  activities: ActivityItemProps[];
  title?: string;
  emptyMessage?: string;
  maxItems?: number;
  showViewAll?: boolean;
  onViewAll?: () => void;
  className?: string;
}

const ActivityFeed: React.FC<ActivityFeedProps> = ({
  activities,
  title = 'Activité récente',
  emptyMessage = 'Aucune activité récente',
  maxItems,
  showViewAll = false,
  onViewAll,
  className = '',
}) => {
  const displayedActivities = maxItems ? activities.slice(0, maxItems) : activities;

  return (
    <div className={`bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden ${className}`}>
      <div className="px-6 py-4 border-b border-neutral-200">
        <h3 className="text-lg font-semibold text-neutral-900">{title}</h3>
      </div>
      
      <div className="divide-y divide-neutral-200">
        {displayedActivities.length === 0 ? (
          <div className="p-6 text-center text-neutral-500">{emptyMessage}</div>
        ) : (
          displayedActivities.map((activity) => (
            <ActivityItem key={activity.id} {...activity} />
          ))
        )}
      </div>
      
      {showViewAll && activities.length > 0 && (
        <div className="px-6 py-3 border-t border-neutral-200 bg-neutral-50">
          <button
            onClick={onViewAll}
            className="text-sm font-medium text-primary-600 hover:text-primary-700 w-full text-center"
          >
            Voir toutes les activités
          </button>
        </div>
      )}
    </div>
  );
};

export default ActivityFeed;
