import React, { useState, useEffect } from 'react';
import ClientDashboard from './ClientDashboard';
import ClientProfileCompletionModal from '../profile/ClientProfileCompletionModal';
import { API_BASE_URL } from '../../config';
import { profileService } from '../../services/profileService';
import authService from '../../services/authService';

const ClientDashboardWrapper: React.FC = () => {
  const [showCompletionModal, setShowCompletionModal] = useState<boolean>(false);
  const [profileCompletionChecked, setProfileCompletionChecked] = useState<boolean>(false);
  const [completionPercentage, setCompletionPercentage] = useState<number>(0);

  useEffect(() => {
    const checkProfileCompletion = async () => {
      try {
        console.log('Vérification de la complétion du profil client...');

        const user = authService.getUser();

        // Ignorer pour les utilisateurs professionnels
        if (user?.is_professional) {
          console.log('Utilisateur professionnel détecté, ignoré');
          setProfileCompletionChecked(true);
          return;
        }

        // Vérifier si c'est la première connexion
        const isFirstLogin = authService.isFirstLogin();
        console.log('Première connexion ?', isFirstLogin);

        // Récupérer le statut de complétion du profil
        const data = await profileService.getCompletionStatus();
        console.log('Statut de complétion du profil:', data);

        // Définir le pourcentage de complétion
        setCompletionPercentage(data.completion_percentage || 0);

        // Vérifier si le profil est incomplet (moins de 70%)
        const isIncomplete = data.completion_percentage < 70;
        console.log('Profil incomplet ?', isIncomplete);

        // Afficher le modal si c'est la première connexion et que le profil est incomplet
        if (isFirstLogin && isIncomplete) {
          console.log('Affichage du modal de complétion de profil');
          setShowCompletionModal(true);
          // Marquer que le modal a été affiché
          localStorage.setItem('first_login', 'false');
        }
      } catch (err) {
        console.error('Erreur lors de la vérification de la complétion du profil:', err);
      } finally {
        setProfileCompletionChecked(true);
      }
    };

    checkProfileCompletion();
  }, []);

  const handleCompletionModalClose = () => {
    setShowCompletionModal(false);
  };

  const handleProfileCompleted = () => {
    setShowCompletionModal(false);
    setCompletionPercentage(100);
    // Reload the dashboard to reflect the updated profile
    window.location.reload();
  };

  if (!profileCompletionChecked) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <>
      <ClientDashboard completionPercentage={completionPercentage} />

      <ClientProfileCompletionModal
        isOpen={showCompletionModal}
        onClose={handleCompletionModalClose}
        onComplete={handleProfileCompleted}
      />
    </>
  );
};

export default ClientDashboardWrapper;
