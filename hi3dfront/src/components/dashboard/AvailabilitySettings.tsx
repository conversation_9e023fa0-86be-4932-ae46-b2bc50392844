import React, { useState, useEffect } from 'react';
import { Calendar, Clock, Save, X } from 'lucide-react';
import Button from '../ui/Button';
import { API_BASE_URL } from '../../config';

interface AvailabilitySettingsProps {
  onClose: () => void;
}

// Définir les types selon l'API
type AvailabilityStatus = 'available' | 'unavailable';

interface AvailabilityData {
  availability_status: AvailabilityStatus;
  estimated_response_time?: string; // Date ISO ou null
  availability_details?: {
    hours_per_week?: number;
    available_from?: string;
    custom_message?: string;
  };
}

const AvailabilitySettings: React.FC<AvailabilitySettingsProps> = ({ onClose }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Initialiser avec des valeurs par défaut
  const [availability, setAvailability] = useState<AvailabilityData>({
    availability_status: 'available', // Valeur par défaut compatible avec l'API
    availability_details: {
      hours_per_week: 40,
      available_from: '',
      custom_message: '',
    }
  });

  // Récupérer le profil de l'utilisateur pour obtenir les informations de disponibilité
  useEffect(() => {
    const fetchProfileData = async () => {
      setLoading(true);
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('Non authentifié');
        }

        // Utiliser l'endpoint du profil général qui contient les informations de disponibilité
        const response = await fetch(`${API_BASE_URL}/api/profile`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération du profil');
        }

        const data = await response.json();

        // Extraire les informations de disponibilité du profil
        if (data.profile) {
          const availabilityStatus = data.profile.availability_status || 'available';
          const availabilityDetails = data.profile.availability_details || {};

          setAvailability({
            availability_status: availabilityStatus as AvailabilityStatus,
            estimated_response_time: data.profile.estimated_response_time,
            availability_details: {
              hours_per_week: availabilityDetails.hours_per_week || 40,
              available_from: availabilityDetails.available_from || '',
              custom_message: availabilityDetails.custom_message || '',
            }
          });
        }
      } catch (err) {
        console.error('Erreur lors de la récupération du profil:', err);
        setError(err instanceof Error ? err.message : 'Une erreur est survenue');
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, []);

  // Gérer les changements dans le formulaire
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'availability_status') {
      setAvailability(prev => ({
        ...prev,
        availability_status: value as AvailabilityStatus,
      }));
    } else if (name === 'hours_per_week') {
      setAvailability(prev => ({
        ...prev,
        availability_details: {
          ...prev.availability_details,
          hours_per_week: parseInt(value) || 0,
        }
      }));
    } else if (name === 'available_from' || name === 'custom_message') {
      setAvailability(prev => ({
        ...prev,
        availability_details: {
          ...prev.availability_details,
          [name]: value,
        }
      }));
    }
  };

  // Soumettre les modifications
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Non authentifié');
      }

      // Préparer les données à envoyer selon le format attendu par l'API
      let estimated_response_time: string | null = null;

      // Si le statut est "unavailable", calculer une date estimée de réponse
      if (availability.availability_status === 'unavailable' && availability.availability_details?.available_from) {
        const availableFromDate = new Date(availability.availability_details.available_from);
        estimated_response_time = availableFromDate.toISOString();
      }

      // Vérifier que le statut est valide
      if (!['available', 'unavailable'].includes(availability.availability_status)) {
        setError('Le statut de disponibilité doit être "disponible" ou "indisponible".');
        setLoading(false);
        return;
      }

      const payload = {
        availability_status: availability.availability_status,
        availability_details: availability.availability_details,
        estimated_response_time: estimated_response_time
      };

      console.log('Envoi des données de disponibilité:', payload);

      // Utiliser l'endpoint standardisé pour mettre à jour la disponibilité
      const response = await fetch(`${API_BASE_URL}/api/profile/availability`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de la mise à jour des disponibilités');
      }

      const responseData = await response.json();
      console.log('Réponse de l\'API:', responseData);

      setSuccess('Vos disponibilités ont été mises à jour avec succès');

      // Fermer le modal après un court délai
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      console.error('Erreur lors de la mise à jour des disponibilités:', err);
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200">
          <h2 className="text-xl font-semibold text-neutral-900 flex items-center">
            <Clock className="mr-2 h-5 w-5 text-primary-600" />
            Gérer mes disponibilités
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-neutral-500 hover:text-neutral-700"
            aria-label="Fermer"
            title="Fermer"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              {success}
            </div>
          )}

          <div>
            <label htmlFor="availability_status" className="block text-sm font-medium text-neutral-700 mb-1">
              Statut de disponibilité
            </label>
            <select
              id="availability_status"
              name="availability_status"
              value={availability.availability_status}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="available">Disponible</option>
              <option value="unavailable">Indisponible</option>
            </select>
          </div>

          {/* Le champ "Heures disponibles par semaine" a été temporairement supprimé */}

          {/* Section pour le statut "unavailable" */}
          {availability.availability_status === 'unavailable' && (
            <div>
              <label htmlFor="available_from" className="block text-sm font-medium text-neutral-700 mb-1">
                Disponible à partir de (estimation)
              </label>
              <input
                type="date"
                id="available_from"
                name="available_from"
                value={availability.availability_details?.available_from || ''}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          )}

          {/* Le champ "Message personnalisé (optionnel)" a été temporairement supprimé */}

          <div className="flex justify-end space-x-3 pt-4 border-t border-neutral-200">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Annuler
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={loading}
              leftIcon={<Save className="h-5 w-5" />}
            >
              Enregistrer
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AvailabilitySettings;
