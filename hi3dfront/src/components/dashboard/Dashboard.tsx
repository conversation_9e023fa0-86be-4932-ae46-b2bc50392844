import React, { useEffect, useState, useCallback } from 'react';
import { Navigate } from 'react-router-dom';
import ProfessionalDashboard from './ProfessionalDashboard';
import ClientDashboard from './ClientDashboard';
import ClientDashboardWrapper from './ClientDashboardWrapper';
// @ts-ignore
import ProfileCompletionModal from './ProfileCompletionModal.tsx';
import { profileService } from '../../services/profileService';
import { dashboardService } from '../../services/dashboardService';
import authService from '../../services/authService';
import LoadingSpinner from '../ui/LoadingSpinner';
import Modal from '../ui/Modal';
import Alert from '../ui/Alert';
import { useToast } from '../../context/ToastContext';

const Dashboard: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);
  const [isProfessional, setIsProfessional] = useState<boolean | null>(null);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [userId, setUserId] = useState<number | null>(null);
  const [isFirstLogin, setIsFirstLogin] = useState(false);
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const toast = useToast();

  // Fonctions pour gérer le modal de complétion de profil
  const handleCloseModal = useCallback(() => {
    setShowProfileModal(false);
  }, []);

  // Fonction pour récupérer les données de complétion du profil
  const fetchProfileCompletionData = useCallback(async () => {
    try {
      console.log('Récupération des données de complétion du profil professionnel...');

      // Utiliser le service de profil pour récupérer le statut de complétion
      const data = await profileService.getCompletionStatus();
      console.log('Données de complétion récupérées:', data);

      setCompletionPercentage(data.completion_percentage || 0);

      // Vérifier si c'est la première connexion
      const isFirstLogin = authService.isFirstLogin();
      console.log('Première connexion (professionnel) ?', isFirstLogin);

      // Vérifier si le profil est incomplet (moins de 50%)
      const isIncomplete = data.completion_percentage < 50;
      console.log('Profil incomplet (professionnel) ?', isIncomplete);

      // Afficher le modal si c'est la première connexion et que le profil est incomplet
      if (isFirstLogin && isIncomplete) {
        console.log('Affichage du modal de complétion de profil professionnel');
        setIsFirstLogin(true);
        setShowProfileModal(true);
      }

      // Afficher un toast de succès si le profil est presque complet et que c'est la première fois qu'on le vérifie
      // Utiliser une variable de session pour éviter d'afficher le toast à chaque rendu
      const hasShownCompletionToast = sessionStorage.getItem('hasShownCompletionToast');
      if (data.completion_percentage >= 80 && !hasShownCompletionToast) {
        toast.showToast('success', 'Votre profil est presque complet !', 3000);
        sessionStorage.setItem('hasShownCompletionToast', 'true');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des données de complétion:', error);

      // Définir le message d'erreur
      setError(error instanceof Error ? error.message : 'Erreur lors de la récupération des données de profil');

      // Utiliser une valeur par défaut en cas d'erreur
      setCompletionPercentage(0);

      // En cas d'erreur, vérifier si c'est la première connexion
      const isFirstLogin = authService.isFirstLogin();
      if (isFirstLogin) {
        setIsFirstLogin(true);
        setShowProfileModal(true);
      }

      // Afficher un toast d'erreur (une seule fois par session)
      const hasShownErrorToast = sessionStorage.getItem('hasShownErrorToast');
      if (!hasShownErrorToast) {
        toast.showToast('error', 'Erreur lors de la récupération des données de profil', 5000);
        sessionStorage.setItem('hasShownErrorToast', 'true');
      }
    }
  }, [toast]);

  const handleCompleteProfile = useCallback(() => {
    // Marquer que ce n'est plus la première connexion
    localStorage.setItem('first_login', 'false');
    setShowProfileModal(false);

    // Afficher un toast de succès
    toast.showToast('success', 'Merci d\'avoir complété votre profil !', 3000);

    // Rafraîchir les données de complétion du profil
    fetchProfileCompletionData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [toast]);

  useEffect(() => {
    console.log('Initialisation du tableau de bord...');

    // Vérifier si l'utilisateur est authentifié
    const isAuth = authService.isAuthenticated();
    const user = authService.getUser();

    if (isAuth && user && user.id) {
      console.log('Utilisateur authentifié:', user);
      setIsAuthenticated(true);
      setIsProfessional(user.is_professional === true);
      setUserId(user.id);

      // Récupérer les données de complétion du profil
      if (user.is_professional) {
        console.log('Récupération des données de complétion pour un professionnel');
        fetchProfileCompletionData();
      }
    } else {
      console.log('Utilisateur non authentifié');
      setIsAuthenticated(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);



  // Show loading state while checking authentication
  if (isAuthenticated === null) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-neutral-50">
        <LoadingSpinner size="lg" label="Chargement du tableau de bord" />
      </div>
    );
  }



  // Redirect to login if not authenticated
  if (isAuthenticated === false) {
    return <Navigate to="/login" />;
  }

  // Show appropriate dashboard based on user type
  return (
    <>
      {/* Afficher une alerte d'erreur si nécessaire */}
      {error && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
          className="fixed top-4 right-4 z-50 max-w-md"
        >
          {error}
        </Alert>
      )}

      {isProfessional ?
        <ProfessionalDashboard completionPercentage={completionPercentage} /> :
        <ClientDashboardWrapper />
      }

      {/* Modal de complétion de profil pour les professionnels uniquement */}
      {isProfessional && (
        <ProfileCompletionModal
          isOpen={showProfileModal}
          onClose={handleCloseModal}
          onComplete={handleCompleteProfile}
          userType="professional"
        />
      )}
    </>
  );
};

// Optimiser le composant avec React.memo pour éviter les re-rendus inutiles
export default React.memo(Dashboard);
