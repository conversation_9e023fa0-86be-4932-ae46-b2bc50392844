import React, { useState, useEffect,useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  User,
  Mail,
  Phone,
  MapPin,
  X,
  Plus,
  Upload,
  Loader2,
  Save,
  ArrowLeft,
  Briefcase,
  Award,
  Check,
  Trash2,
  ChevronDown,
  Search,
} from "lucide-react";
import { API_BASE_URL } from "../../config";
import DashboardLayout from "./DashboardLayout";
import Button from "../ui/Button";
import { getAvatarUrl } from "../../utils/avatarUtils";
import {
  MAIN_CATEGORIES,
  ARCHITECTURE_3D_CATEGORIES,
  getAllCategories,
} from "../../data/categories";
import { COUNTRIES } from "../../data/countries";
import { CITIES_BY_COUNTRY } from "../../data/cities";
import LocationSelector from "../../components/ui/LocationSelector";

const PREDEFINED_LANGUAGES = [
  "Fr<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>d",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON>ois",
  "Japon<PERSON>",
  "<PERSON><PERSON>",
];

interface ProfileData {
  id?: number;
  user_id?: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  skills: string[];
  avatar?: string;
  cover_photo?: string;
  title?: string;
  hourly_rate?: number;
  languages?: string[];
  services_offered?: string[];
}

const DashboardEditProfile: React.FC = () => {
  const [profileData, setProfileData] = useState<ProfileData>({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    country: "",
    bio: "",
    skills: [],
    avatar: "",
    cover_photo:"",
    title: "",
    hourly_rate: 0,
    languages: [],
    services_offered: [],
  });

  const [activeTab, setActiveTab] = useState("personal");
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [newSkill, setNewSkill] = useState("");
  const [newLanguage, setNewLanguage] = useState("");
  const [newService, setNewService] = useState("");
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [filteredSkills, setFilteredSkills] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  const [coverPhoto, setCoverPhoto] = useState<File | null>(null);
  const [coverPhotoPreview, setCoverPhotoPreview] = useState<string | null>(null);
  const coverInputRef = useRef<HTMLInputElement | null>(null);
  const handleCoverPhotoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setCoverPhoto(file);
      setCoverPhotoPreview(URL.createObjectURL(file));

      const reader = new FileReader();
      reader.onloadend = () => setCoverPhotoPreview(reader.result as string);
      reader.readAsDataURL(file);

      try {
        const token = localStorage.getItem("token");
        if (!token) {
          navigate("/login");
          return;
        }

        const formData = new FormData();
        formData.append("cover_photo", file);

        const response = await fetch(`${API_BASE_URL}/api/profile/cover`, {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
          body: formData,
        });

        if (!response.ok) {
          throw new Error("Failed to upload cover photo");
        }

        const data = await response.json();
        setProfileData((prev) => ({ ...prev, cover_photo: data.cover_url }));
        setSuccess("Cover photo updated successfully!");
      } catch (err) {
        console.error("Cover photo upload error:", err);
        setError(err instanceof Error ? err.message : "Cover photo upload failed");
      }
    }
  };



  // Liste des compétences par catégorie
  const skillsByCategory: Record<string, string[]> = {
    modeling: [
      "Blender",
      "Maya",
      "3ds Max",
      "ZBrush",
      "Substance Painter",
      "Hard Surface Modeling",
      "Organic Modeling",
    ],
    animation: [
      "Animation de personnages",
      "Animation d'objets",
      "Motion Capture",
      "Rigging",
      "Facial Animation",
    ],
    architectural: [
      "SketchUp",
      "Revit",
      "ArchiCAD",
      "Lumion",
      "V-Ray",
      "Rendu architectural",
      "Modélisation BIM",
    ],
    product: [
      "Fusion 360",
      "SolidWorks",
      "Rhino 3D",
      "KeyShot",
      "Prototypage 3D",
      "Design industriel",
    ],
    character: [
      "Character Design",
      "Character Modeling",
      "Character Rigging",
      "Facial Rigging",
      "Sculpting",
    ],
    environment: [
      "Environment Design",
      "Landscape Modeling",
      "Terrain Generation",
      "World Building",
      "Level Design",
    ],
    vr_ar: [
      "Unity",
      "Unreal Engine",
      "WebXR",
      "A-Frame",
      "ARKit",
      "ARCore",
      "Oculus SDK",
    ],
    game_art: [
      "Game Asset Creation",
      "Low Poly Modeling",
      "Texture Baking",
      "UV Mapping",
      "PBR Texturing",
    ],
  };

  // Liste complète de toutes les compétences
  const [allSkills, setAllSkills] = useState<string[]>([]);

  const navigate = useNavigate();

  // Initialiser la liste complète des compétences
  useEffect(() => {
    const skills: string[] = [];
    Object.values(skillsByCategory).forEach((categorySkills) => {
      categorySkills.forEach((skill) => {
        if (!skills.includes(skill)) {
          skills.push(skill);
        }
      });
    });
    setAllSkills(skills.sort());
    setFilteredSkills(skills.sort());
  }, []);

  // Filtrer les compétences en fonction de la catégorie sélectionnée
  useEffect(() => {
    if (selectedCategory && skillsByCategory[selectedCategory]) {
      setFilteredSkills(skillsByCategory[selectedCategory]);
    } else if (selectedCategory === "") {
      setFilteredSkills(allSkills);
    }
  }, [selectedCategory, allSkills]);

  // Filtrer les compétences en fonction du terme de recherche
  useEffect(() => {
    if (searchTerm) {
      const baseSkills = selectedCategory
        ? skillsByCategory[selectedCategory]
        : allSkills;
      const filtered = baseSkills.filter((skill) =>
        skill.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredSkills(filtered);
    } else {
      if (selectedCategory && skillsByCategory[selectedCategory]) {
        setFilteredSkills(skillsByCategory[selectedCategory]);
      } else {
        setFilteredSkills(allSkills);
      }
    }
  }, [searchTerm, selectedCategory, allSkills]);

  useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const token = localStorage.getItem("token");
        if (!token) {
          navigate("/login");
          return;
        }

        const response = await fetch(`${API_BASE_URL}/api/profile`, {
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) throw new Error("Failed to fetch profile data");

        const data = await response.json();

        if (data.profile) {
          setProfileData({
            ...data.profile,
            skills: Array.isArray(data.profile.skills)
              ? data.profile.skills
              : [],
            languages: Array.isArray(data.profile.languages)
              ? data.profile.languages
              : [],
            services_offered: Array.isArray(data.profile.services_offered)
              ? data.profile.services_offered
              : [],
          });
        }
      } catch (err) {
        console.error("Error fetching profile:", err);
        setError("Could not fetch your profile data");
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [navigate]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    if (name === "hourly_rate") {
      setProfileData((prev) => ({
        ...prev,
        [name]: value === "" ? 0 : parseFloat(value),
      }));
    } else {
      setProfileData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleAddSkill = (skillToAdd?: string) => {
    const skillValue = skillToAdd || newSkill.trim();
    if (!skillValue) return;

    setProfileData((prev) => {
      const currentSkills = Array.isArray(prev.skills) ? prev.skills : [];
      if (!currentSkills.includes(skillValue)) {
        return { ...prev, skills: [...currentSkills, skillValue] };
      }
      return prev;
    });

    if (!skillToAdd) setNewSkill("");
  };

  // Ajouter une compétence depuis la liste
  const handleAddSkillFromList = (skill: string) => {
    if (!profileData.skills.includes(skill)) {
      setProfileData((prev) => ({
        ...prev,
        skills: [...prev.skills, skill],
      }));
    }
  };

  const handleRemoveSkill = (skillToRemove: string) => {
    setProfileData((prev) => ({
      ...prev,
      skills: prev.skills.filter((skill) => skill !== skillToRemove),
    }));
  };

  // Gestion de la recherche
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleAddLanguage = (languageToAdd?: string) => {
    const languageValue = languageToAdd || newLanguage.trim();
    if (!languageValue) return;

    setProfileData((prev) => {
      const currentLanguages = Array.isArray(prev.languages)
        ? prev.languages
        : [];
      if (!currentLanguages.includes(languageValue)) {
        return { ...prev, languages: [...currentLanguages, languageValue] };
      }
      return prev;
    });

    if (!languageToAdd) setNewLanguage("");
  };

  const handleRemoveLanguage = (languageToRemove: string) => {
    setProfileData((prev) => ({
      ...prev,
      languages:
        prev.languages?.filter((lang) => lang !== languageToRemove) || [],
    }));
  };

  const handleAddService = () => {
    const serviceValue = newService.trim();
    if (!serviceValue) return;

    setProfileData((prev) => {
      const currentServices = Array.isArray(prev.services_offered)
        ? prev.services_offered
        : [];
      if (!currentServices.includes(serviceValue)) {
        return {
          ...prev,
          services_offered: [...currentServices, serviceValue],
        };
      }
      return prev;
    });

    setNewService("");
  };

  const handleRemoveService = (serviceToRemove: string) => {
    setProfileData((prev) => ({
      ...prev,
      services_offered:
        prev.services_offered?.filter(
          (service) => service !== serviceToRemove
        ) || [],
    }));
  };

  const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.[0]) {
      const file = e.target.files[0];
      setAvatarFile(file);

      const reader = new FileReader();
      reader.onloadend = () => setAvatarPreview(reader.result as string);
      reader.readAsDataURL(file);

      try {
        const token = localStorage.getItem("token");
        if (!token) {
          navigate("/login");
          return;
        }

        const formData = new FormData();
        formData.append("avatar", file);

        const response = await fetch(`${API_BASE_URL}/api/profile/avatar`, {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
          body: formData,
        });

        if (!response.ok) {
          throw new Error("Failed to upload avatar");
        }

        const data = await response.json();
        setProfileData((prev) => ({ ...prev, avatar: data.avatar_path }));
        setSuccess("Avatar updated successfully!");
      } catch (err) {
        console.error("Avatar upload error:", err);
        setError(err instanceof Error ? err.message : "Avatar upload failed");
      }
    }
  };

  const handleRemoveAvatar = async () => {
    if (!window.confirm("Are you sure you want to remove your avatar?")) return;

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        navigate("/login");
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/profile/avatar`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) throw new Error("Failed to remove avatar");

      setAvatarFile(null);
      setAvatarPreview(null);
      setProfileData((prev) => ({ ...prev, avatar: "" }));
      setSuccess("Avatar removed successfully!");
    } catch (err) {
      console.error("Error removing avatar:", err);
      setError(err instanceof Error ? err.message : "Failed to remove avatar");
    }
  };

  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      const token = localStorage.getItem("token");
      if (!token) {
        navigate("/login");
        return;
      }

      // Préparer les données à envoyer
      const payload = {
        first_name: profileData.first_name,
        last_name: profileData.last_name,
        phone: profileData.phone,
        address: profileData.address,
        city: profileData.city,
        country: profileData.country,
        bio: profileData.bio,
        title: profileData.title,
        hourly_rate: profileData.hourly_rate,
        skills: profileData.skills,
        languages: profileData.languages,
        services_offered: profileData.services_offered,
      };

      console.log("Sending payload:", payload);

      const response = await fetch(`${API_BASE_URL}/api/profile`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update profile");
      }

      const data = await response.json();
      setProfileData((prev) => ({ ...prev, ...data.profile }));
      setSuccess("Profile updated successfully!");

      setTimeout(() => navigate("/dashboard/profile"), 2000);
    } catch (err) {
      console.error("Profile update error:", err);
      setError(err instanceof Error ? err.message : "Profile update failed");
    } finally {
      setSaving(false);
    }
  };

  const getAvatarUrlFromProfile = () => {
    if (avatarPreview) return avatarPreview;
    if (profileData?.avatar) return getAvatarUrl(profileData.avatar);
    return "https://via.placeholder.com/150";
  };

  const getCoverUrlFromProfile = () => {
    if (coverPhotoPreview) return coverPhotoPreview;
    if (profileData?.cover_photo) return getAvatarUrl(profileData.cover_photo);
    return "https://via.placeholder.com/150";
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <Loader2 className="animate-spin h-12 w-12 text-primary-600" />
        </div>
      </DashboardLayout>
    );
  }

  if (!profileData) {
    return (
      <DashboardLayout>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
          <div className="text-yellow-700 mb-4">
            Aucune  donnée de profil disponible. Veuillez réessayer plus tard.
          </div>
          <Button
            variant="primary"
            onClick={() => navigate("/dashboard/profile")}
          >
            Retour au profil
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Modifier le profil"
      subtitle="Mettez à jour vos informations personnelles et les paramètres de votre profil"
      actions={
        <Button
          variant="outline"
          leftIcon={<ArrowLeft className="h-5 w-5" />}
          onClick={() => navigate("/dashboard/profile")}
        >
          Retour au profil
        </Button>
      }
    >
      <div className="bg-white rounded-lg shadow-sm border border-neutral-200">
        <div className="border-b border-neutral-200">
          <div className="flex overflow-x-auto">
            {["personal", "professional", "skills"].map((tab) => (
              <button
                key={tab}
                type="button"
                className={`px-4 py-4 font-medium text-sm border-b-2 transition-colors ${
                  activeTab === tab
                    ? "border-primary-600 text-primary-600"
                    : "border-transparent text-neutral-500 hover:text-neutral-700"
                }`}
                onClick={() => setActiveTab(tab)}
              >
                {tab === "personal" && "Informations personnelles"}
                {tab === "professional" && "Détails professionnels"}
                {tab === "skills" && "Spécialités & Langues"}
              </button>
            ))}
          </div>
        </div>

        <div className="p-6">
          {error && (
            <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}

          {success && (
            <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              {success}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {activeTab === "personal" && (
              <div className="space-y-6">

                <div className="relative w-full rounded-lg overflow-hidden border-2 border-neutral-300 shadow mb-8">

                {/* Affichage de l'image ou zone vide */}
                {getCoverUrlFromProfile() ? (
                  <img
                    src={getCoverUrlFromProfile()}
                    alt="Photo de couverture"
                    className="w-full h-48 object-cover"
                  />
                ) : (
                  <div className="w-full h-48 bg-neutral-100 flex items-center justify-center text-neutral-500 text-sm">
                    Aucune photo de couverture sélectionnée
                  </div>
                )}

                {/* Bouton modification toujours visible */}
                <div
                  onClick={() => coverInputRef.current?.click()}
                  className="absolute bottom-3 right-3 bg-black/70 text-white backdrop-blur-sm rounded-full p-2 cursor-pointer hover:bg-black/90 transition shadow-md"
                  title="Modifier la photo de couverture"
                >
                  <Upload className="w-5 h-5" />
                </div>

                {/* Input invisible pour chargement de fichier */}
                <input
                  type="file"
                  ref={coverInputRef}
                  accept="image/*"
                  className="hidden"
                  onChange={handleCoverPhotoChange}
                />
              </div>

                <div className="flex items-center space-x-6 mb-6">
                  <div className="relative">
                    <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-neutral-200">
                      <img
                        src={getAvatarUrlFromProfile()}
                        alt="Profile"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div
                      onClick={() =>
                        document.getElementById("avatar-upload")?.click()
                      }
                      className="absolute bottom-0 right-0 bg-primary-600 text-white rounded-full p-2 cursor-pointer hover:bg-primary-700 transition-colors"
                    >
                      <Upload className="h-4 w-4" />
                    </div>
                    <input
                      type="file"
                      id="avatar-upload"
                      className="hidden"
                      accept="image/*"
                      onChange={handleAvatarChange}
                    />
                  </div>
                  <div>
                    <h3 className="text-lg font-medium text-neutral-900">
                      Photo de profil
                    </h3>
                    <div className="mt-2 flex space-x-2">
                      <button
                        type="button"
                        onClick={() =>
                          document.getElementById("avatar-upload")?.click()
                        }
                        className="text-xs px-2 py-1 bg-primary-50 text-primary-600 rounded border border-primary-200 hover:bg-primary-100 transition-colors flex items-center"
                      >
                        <Upload className="h-3 w-3 mr-1" />
                        Modifier
                      </button>
                      {profileData.avatar && (
                        <button
                          type="button"
                          onClick={handleRemoveAvatar}
                          className="text-xs px-2 py-1 bg-red-50 text-red-600 rounded border border-red-200 hover:bg-red-100 transition-colors flex items-center"
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Supprimer
                        </button>
                      )}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {[
                    {
                      id: "first_name",
                      label: "Prénom",
                      icon: <User className="h-5 w-5 text-neutral-400" />,
                    },
                    {
                      id: "last_name",
                      label: "Nom",
                      icon: <User className="h-5 w-5 text-neutral-400" />,
                    },
                    {
                      id: "email",
                      label: "E-mail",
                      icon: <Mail className="h-5 w-5 text-neutral-400" />,
                      disabled: true,
                    },
                    {
                      id: "phone",
                      label: "Téléphone",
                      icon: <Phone className="h-5 w-5 text-neutral-400" />,
                    },
                  ].map((field) => (
                    <div key={field.id}>
                      <label
                        htmlFor={field.id}
                        className="block text-sm font-medium text-neutral-700 mb-1"
                      >
                        {field.label}
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          {field.icon}
                        </div>
                        <input
                          type={field.id === "email" ? "email" : "text"}
                          id={field.id}
                          name={field.id}
                          value={
                            profileData[field.id as keyof ProfileData] as string
                          }
                          onChange={handleInputChange}
                          className={`block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 ${
                            field.disabled
                              ? "bg-neutral-50 text-neutral-500"
                              : ""
                          }`}
                          required={!field.disabled}
                          disabled={field.disabled}
                        />
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6">
                  <LocationSelector
                    country={profileData.country}
                    city={profileData.city}
                    address={profileData.address}
                    onCountryChange={(country: string) =>
                      setProfileData({ ...profileData, country })
                    }
                    onCityChange={(city: string) =>
                      setProfileData({ ...profileData, city })
                    }
                    onAddressChange={(address: string) =>
                      setProfileData({ ...profileData, address })
                    }
                    required
                  />
                </div>
              </div>
            )}

            {activeTab === "professional" && (
              <div className="space-y-6">
                <div>
                  <label
                    htmlFor="title"
                    className="block text-sm font-medium text-neutral-700 mb-1"
                  >
                    Titre professionnel
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Briefcase className="h-5 w-5 text-neutral-400" />
                    </div>
                    <input
                      type="text"
                      id="title"
                      name="title"
                      value={profileData.title || ""}
                      onChange={handleInputChange}
                      className="block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g. Senior 3D Artist"
                    />
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="hourly_rate"
                    className="block text-sm font-medium text-neutral-700 mb-1"
                  >
                    Tarif horaire (€)
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Award className="h-5 w-5 text-neutral-400" />
                    </div>
                    <input
                      type="number"
                      id="hourly_rate"
                      name="hourly_rate"
                      value={profileData.hourly_rate || 0}
                      onChange={handleInputChange}
                      className="block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g. 25"
                      min="0"
                      step="0.01"
                    />
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="bio"
                    className="block text-sm font-medium text-neutral-700 mb-1"
                  >
                    Biographie / À propos de moi
                  </label>
                  <textarea
                    id="bio"
                    name="bio"
                    value={profileData.bio}
                    onChange={handleInputChange}
                    rows={5}
                    className="block w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Tell clients about yourself, your experience, and your expertise..."
                  />
                </div>
              </div>
            )}

            {activeTab === "skills" && (
              <div className="space-y-8">
                <div>
                  <h3 className="text-lg font-medium text-neutral-900 mb-3">
                    Compétences
                  </h3>

                  {/* Sélection de catégorie */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-neutral-700 mb-1">
                      Catégorie de compétences
                    </label>
                    <div className="relative">
                      <button
                        type="button"
                        onClick={() =>
                          setShowCategoryDropdown(!showCategoryDropdown)
                        }
                        className="w-full flex items-center justify-between px-4 py-2 border border-neutral-300 rounded-lg bg-white text-left focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      >
                        <span>
                          {selectedCategory
                            ? MAIN_CATEGORIES.find(
                                (cat) => cat.value === selectedCategory
                              )?.label || selectedCategory
                            : "Toutes les catégories"}
                        </span>
                        <ChevronDown
                          size={20}
                          className={`transition-transform ${
                            showCategoryDropdown ? "rotate-180" : ""
                          }`}
                        />
                      </button>

                      {showCategoryDropdown && (
                        <div className="absolute z-10 mt-1 w-full bg-white border border-neutral-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                          <div
                            className="px-4 py-2 hover:bg-neutral-100 cursor-pointer"
                            onClick={() => {
                              setSelectedCategory("");
                              setShowCategoryDropdown(false);
                            }}
                          >
                            Toutes les catégories
                          </div>
                          {MAIN_CATEGORIES.map((category) => (
                            <div
                              key={category.id}
                              className="px-4 py-2 hover:bg-neutral-100 cursor-pointer"
                              onClick={() => {
                                setSelectedCategory(category.value);
                                setShowCategoryDropdown(false);
                              }}
                            >
                              {category.label}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Recherche de compétences */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-neutral-700 mb-1">
                      Compétences suggérées
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Search size={18} className="text-neutral-400" />
                      </div>
                      <input
                        type="text"
                        placeholder="Rechercher une compétence..."
                        value={searchTerm}
                        onChange={handleSearchChange}
                        className="pl-10 w-full px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      />
                    </div>

                    {/* Liste des compétences suggérées */}
                    <div className="grid grid-cols-2 gap-2 mt-2 max-h-40 overflow-y-auto p-2 border border-neutral-200 rounded-md">
                      {filteredSkills.map((skill, index) => (
                        <button
                          key={index}
                          type="button"
                          onClick={() => handleAddSkillFromList(skill)}
                          disabled={profileData.skills?.includes(skill)}
                          className={`text-left px-3 py-2 rounded-lg text-sm ${
                            profileData.skills?.includes(skill)
                              ? "bg-neutral-100 text-neutral-500 cursor-not-allowed"
                              : "bg-primary-50 text-primary-700 hover:bg-primary-100"
                          }`}
                          title={
                            profileData.skills?.includes(skill)
                              ? "Déjà ajouté"
                              : "Ajouter cette compétence"
                          }
                        >
                          {skill}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Ajout manuel de compétence */}
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">
                      Ajouter une compétence personnalisée
                    </label>
                    <div className="flex">
                      <input
                        type="text"
                        value={newSkill}
                        onChange={(e) => setNewSkill(e.target.value)}
                        onKeyPress={(e) =>
                          e.key === "Enter" &&
                          (e.preventDefault(), handleAddSkill())
                        }
                        className="block w-full px-3 py-2 border border-neutral-300 rounded-l-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                        placeholder="Saisir une compétence personnalisée"
                      />
                      <button
                        type="button"
                        onClick={() => handleAddSkill()}
                        className="bg-primary-600 text-white px-4 py-2 rounded-r-md hover:bg-primary-700 transition-colors"
                        title="Ajouter la compétence"
                        aria-label="Ajouter la compétence"
                      >
                        <Plus className="h-5 w-5" />
                      </button>
                    </div>
                  </div>

                  <label className="block text-sm font-medium text-neutral-700 mb-1 mt-4">
                    Liste des compétences sélectionnées
                  </label>
                  {/* Liste des compétences ajoutées */}
                  <div className="p-4 border border-neutral-200 rounded-lg bg-neutral-50 min-h-[100px] mb-6 mt-2">
                    {profileData.skills?.length > 0 ? (
                      <div className="flex flex-wrap gap-2">
                        {profileData.skills.map((skill, index) => (
                          <div
                            key={index}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-white border border-neutral-300 shadow-sm"
                          >
                            {skill}
                            <button
                              type="button"
                              onClick={() => handleRemoveSkill(skill)}
                              className="ml-1.5 text-neutral-500 hover:text-red-500"
                              title={`Supprimer ${skill}`}
                              aria-label={`Supprimer ${skill}`}
                            >
                              <X className="h-3.5 w-3.5" />
                            </button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center h-full text-neutral-500">
                        Aucune compétence ajoutée
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-neutral-900 mb-3">
                    Langues
                  </h3>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {profileData.languages?.map((language, index) => (
                      <div
                        key={index}
                        className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium flex items-center"
                      >
                        {language}
                        <button
                          type="button"
                          onClick={() => handleRemoveLanguage(language)}
                          className="ml-2 text-blue-500 hover:text-blue-700"
                          title={`Supprimer ${language}`}
                          aria-label={`Supprimer ${language}`}
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 mb-4 max-h-60 overflow-y-auto p-2 border border-neutral-200 rounded-md">
                    {PREDEFINED_LANGUAGES.map((language) => {
                      const isSelected =
                        profileData.languages?.includes(language);
                      return (
                        <div
                          key={language}
                          onClick={() =>
                            isSelected
                              ? handleRemoveLanguage(language)
                              : handleAddLanguage(language)
                          }
                          className={`flex items-center p-2 rounded-md cursor-pointer transition-colors ${
                            isSelected
                              ? "bg-blue-50 text-blue-700 border border-blue-200"
                              : "bg-white hover:bg-neutral-50 border border-neutral-200"
                          }`}
                        >
                          <div
                            className={`w-5 h-5 rounded-full mr-2 flex items-center justify-center ${
                              isSelected
                                ? "bg-blue-500 text-white"
                                : "border border-neutral-300"
                            }`}
                          >
                            {isSelected && <Check className="h-3 w-3" />}
                          </div>
                          <span className="text-sm">{language}</span>
                        </div>
                      );
                    })}
                  </div>

                  <div className="flex mt-2">
                    <input
                      type="text"
                      value={newLanguage}
                      onChange={(e) => setNewLanguage(e.target.value)}
                      onKeyPress={(e) =>
                        e.key === "Enter" &&
                        (e.preventDefault(), handleAddLanguage())
                      }
                      className="block w-full px-3 py-2 border border-neutral-300 rounded-l-md shadow-sm focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Add a custom language..."
                    />
                    <button
                      type="button"
                      onClick={() => handleAddLanguage()}
                      className="bg-blue-600 text-white px-4 py-2 rounded-r-md hover:bg-blue-700 transition-colors"
                      title="Ajouter la langue"
                      aria-label="Ajouter la langue"
                    >
                      <Plus className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </form>

          <div className="mt-8 pt-6 border-t border-neutral-200 flex justify-end gap-4">
            <Button
              variant="outline"
              onClick={() => navigate("/dashboard/profile")}
            >
              Annuler
            </Button>
            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={saving}
              leftIcon={
                saving ? (
                  <Loader2 className="animate-spin h-5 w-5" />
                ) : (
                  <Save className="h-5 w-5" />
                )
              }
            >
              {saving
                ? "Enregistrement en cours..."
                : "Enregistrer les modifications"}
            </Button>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default DashboardEditProfile;
