import React, { ReactNode } from 'react';

interface StatCardProps {
  title: string;
  value: string | number;
  icon?: ReactNode;
  change?: {
    value: number;
    isPositive: boolean;
  };
  description?: string;
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  change,
  description,
  className = '',
}) => {
  return (
    <div className={`bg-white rounded-lg border border-neutral-200 shadow-sm p-6 ${className}`}>
      <div className="flex justify-between items-start">
        <div>
          <p className="text-sm font-medium text-neutral-500">{title}</p>
          <h3 className="text-2xl font-bold mt-1 text-neutral-900">{value}</h3>
          
          {change && (
            <div className="flex items-center mt-1">
              <span
                className={`text-sm font-medium ${
                  change.isPositive ? 'text-green-600' : 'text-red-600'
                }`}
              >
                {change.isPositive ? '+' : ''}
                {change.value}%
              </span>
              <span className="text-xs text-neutral-500 ml-1">vs. mois précédent</span>
            </div>
          )}
          
          {description && (
            <p className="text-sm text-neutral-600 mt-2">{description}</p>
          )}
        </div>
        
        {icon && (
          <div className="p-3 rounded-full bg-primary-50 text-primary-600">
            {icon}
          </div>
        )}
      </div>
    </div>
  );
};

export default StatCard;
