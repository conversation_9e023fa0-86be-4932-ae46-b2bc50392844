// src/components/Notifications.tsx
import React from 'react';

const Notifications: React.FC = () => {
  const notifications = [
    {
      id: 1,
      title: "Nouvelle offre reçue",
      message: "Vous avez reçu une nouvelle offre pour votre projet.",
      date: "il y a 10 minutes",
    },
    {
      id: 2,
      title: "Message de John Doe",
      message: "John Doe a répondu à votre message.",
      date: "il y a 1 heure",
    },
    {
      id: 3,
      title: "Votre profil a été consulté",
      message: "Un recruteur a consulté votre profil professionnel.",
      date: "il y a 2 heures",
    },
  ];

  return (
    <div className="p-8 bg-white rounded-lg shadow-md max-w-xl mx-auto my-10">
      <h1 className="text-2xl font-bold mb-6">Notifications</h1>
      {notifications.length === 0 ? (
        <p className="text-center text-gray-500 py-4">Aucune notification.</p>
      ) : (
        <ul>
          {notifications.map((notification) => (
            <li key={notification.id} className="border-b last:border-0 py-4">
              <h3 className="text-lg font-semibold">{notification.title}</h3>
              <p className="text-gray-700">{notification.message}</p>
              <span className="block text-sm text-gray-500 mt-1">{notification.date}</span>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default Notifications;