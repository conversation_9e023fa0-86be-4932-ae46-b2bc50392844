import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import ServiceCreateModal from "./ServiceCreateModal";
import { API_BASE_URL } from "../config";

type Props = {
  items?: any[] | undefined;
  itemsPro?: any[] | undefined;
  onRefresh?: () => void;
};

const GalleryServicePro: React.FC<Props> = ({ items, itemsPro, onRefresh }) => {

  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [open, setOpen] = useState(false);
  const [mode, setMode] = useState<"create" | "edit">("create");
  const [editing, setEditing] = useState<any | null>(null);
  const perPage = 6;

  const token = localStorage.getItem("token");
  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const isPro = !!user?.is_professional;

  const getImageService = (imagePath: string | undefined, defaultImage: string = "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}/storage${imagePath}`;
    return `${API_BASE_URL}/storage/${imagePath}`;
  };

  const handleDelete = async (id: number) => {
    if (!token) return;
    const ok = window.confirm("Supprimer ce service ?");
    if (!ok) return;
    try {
      const res = await fetch(`${API_BASE_URL}/api/service-offers/${id}`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!res.ok) throw new Error(await res.text());
      onRefresh?.();
    } catch (e) {
      console.error(e);
      alert("Suppression échouée.");
    }
  };

  // Pro view
  if (itemsPro) {

    // Découpage front
    const indexOfLast = currentPage * perPage;
    const indexOfFirst = indexOfLast - perPage;
    const currentServices = itemsPro.slice(indexOfFirst, indexOfLast);

    const handlePrev = () => {
      if (currentPage > 1) setCurrentPage((p) => p - 1);
    };

    const handleNext = () => {
      if (currentPage < Math.ceil(itemsPro.length / perPage)) setCurrentPage((p) => p + 1);
    };

    return (
      <>
        <div className="w-full mx-auto py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4 justify-items-center mb-8 px-[10px] md:px-[40px]">

            <div
              onClick={() => { setMode("create"); setEditing(null); setOpen(true); }}
              className="group w-full overflow-hidden relative flex flex-col cursor-pointer h-[370px]"
            >
              <div className="relative w-full h-full">
                <div
                  style={{
                    backgroundColor: "#f3f4f6",
                    borderRadius: "5px",
                  }}
                  className="absolute inset-0 flex items-center justify-center"
                >
                  <span
                    className="text-gray-600 text-lg font-semibold"
                    style={{
                      fontSize: "20px",
                      fontWeight: 400,
                      fontFamily: "'Inter', sans-serif",
                      color: "#000000"
                    }}
                  >
                    Add a new Service
                  </span>
                </div>
              </div>
            </div>

            {currentServices.map((item) => (
              <div
                key={item.id}
                onClick={() => isPro ? (setMode("edit"), setEditing(item), setOpen(true)) : setOpen(true)}
                className="group w-full bg-white overflow-hidden relative flex flex-col cursor-pointer h-[370px]"
              >
                {/* Image container (60% height) */}
                <div className="relative w-full h-[320px]">
                  <div
                    style={{
                      backgroundImage: `url(${item?.files?.length > 0
                          ? getImageService(item.files[0].path)
                          : item?.image
                            ? getImageService(item.image)
                            : getImageService(item.image_url)
                        })`,
                      borderRadius: "5px",
                    }}
                    className="absolute inset-0 bg-cover bg-center z-1000"
                  />
                </div>

                {/* Content container (40% height) */}
                <div className="px-0 py-0 pr-4 flex flex-col mb-4 h-[50px]">
                  {/* Titre */}
                  <div className="flex items-center min-h-[27px]">
                    <h2
                      className="font-medium text-[18px] line-clamp-1 flex-1"
                      style={{
                        fontSize: "16px",
                        fontFamily: "'Inter', sans-serif",
                        fontWeight: 800,
                        lineHeight: "30px",
                      }}
                    >
                      {item.title}
                    </h2>
                    <div className="flex items-center gap-2">
                      {isPro && (
                        <>
                          <button
                            className="px-3 py-1 text-xs rounded bg-white/90 hover:bg-white border border-gray-300"
                            onClick={(e) => { e.stopPropagation(); setMode("edit"); setEditing(item); setOpen(true); }}
                          >
                            Edit
                          </button>
                          <button
                            className="px-3 py-1 text-xs rounded bg-white/90 hover:bg-white border border-red-300 text-red-600"
                            onClick={(e) => { e.stopPropagation(); handleDelete(item.id); }}
                          >
                            Delete
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                  <p
                    className="mt-1"
                    style={{
                      fontSize: "12px",
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: 400,
                    }}
                  >
                    USD <strong>{item.price}</strong>
                  </p>
                </div>
              </div>
            ))}
          </div>
          {itemsPro.length > 6 && (
            <div className="flex justify-center items-center gap-4 mt-6">
              <button
                className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
                style={{
                  fontFamily: "'Inter', sans-serif",
                }}
                onClick={handlePrev}
                disabled={currentPage === 1}
              >
                Précédent
              </button>
              <button
                className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
                style={{
                  fontFamily: "'Inter', sans-serif",
                }}
                onClick={handleNext}
                disabled={currentPage === Math.ceil(itemsPro.length / perPage)}
              >
                Suivant
              </button>
            </div>
          )}
        </div>

        <ServiceCreateModal
          open={open}
          onClose={() => setOpen(false)}
          onCompleted={() => { setOpen(false); onRefresh?.(); }}
          mode={mode}
          initialService={editing || undefined}
          title={mode === "edit" ? "Edit service" : "Create a new service"}
        />
      </>
    );
  }

  // Public view
  if (items?.length === 0 || items === undefined) {
    return (
      <div className="text-center py-20 text-gray-600 flex flex-col items-center justify-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-24 h-24 text-gray-400 mb-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth={1.5}
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M3 7a2 2 0 012-2h5l2 2h7a2 2 0 012 2v7a2 2 0 01-2 2H5a2 2 0 01-2-2V7z"
          />
        </svg>
        <h2 className="text-2xl font-semibold">No Services found</h2>
        <p className="text-gray-500 mt-2 max-w-md">
          We couldn't find any service offer at the moment. Please check back later or try a different search.
        </p>
      </div>
    );
  }

  // Découpage front
  const indexOfLast = currentPage * perPage;
  const indexOfFirst = indexOfLast - perPage;
  const currentServices = items.slice(indexOfFirst, indexOfLast);

  const handlePrev = () => {
    if (currentPage > 1) setCurrentPage((p) => p - 1);
  };

  const handleNext = () => {
    if (currentPage < Math.ceil(items.length / perPage)) setCurrentPage((p) => p + 1);
  };

  return (
    <div className="w-full mx-auto py-8">


      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4 justify-items-center mb-8 px-[10px] md:px-[40px]">
        {currentServices.map((item) => (
          <div
            key={item.id}
            onClick={() => {
              navigate("/details-search", {
                state: { service: item },
              });
            }}
            className="group w-full bg-white overflow-hidden relative flex flex-col cursor-pointer h-[370px]"
          >
            {/* Image container (60% height) */}
            <div className="relative w-full h-[320px]">
              <div
                style={{
                  backgroundImage: `url(${item.image_url})`,
                  borderRadius: "5px",
                }}
                className="absolute inset-0 bg-cover bg-center z-1000"
              />
            </div>

            {/* Content container (40% height) */}
            <div className="px-0 py-0 pr-4 flex flex-col mb-4 h-[50px]">
              {/* Titre */}
              <div className="flex items-center min-h-[27px]">
                <h2
                  className="font-medium text-[18px] line-clamp-1 flex-1"
                  style={{
                    fontSize: "16px",
                    fontFamily: "'Inter', sans-serif",
                    fontWeight: 800,
                    lineHeight: "30px",
                  }}
                >
                  {item.title}
                </h2>
              </div>
              <p
                className="mt-1"
                style={{
                  fontSize: "12px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
                }}
              >
                USD <strong>{item.price}</strong>
              </p>
            </div>
          </div>
        ))}
      </div>
      {items.length > 6 && (
        <div className="flex justify-center items-center gap-4 mt-6">
          <button
            className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
            style={{
              fontFamily: "'Inter', sans-serif",
            }}
            onClick={handlePrev}
            disabled={currentPage === 1}
          >
            Précédent
          </button>
          <button
            className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
            style={{
              fontFamily: "'Inter', sans-serif",
            }}
            onClick={handleNext}
            disabled={currentPage === Math.ceil(items.length / perPage)}
          >
            Suivant
          </button>
        </div>
      )}
    </div>
  );
};

export default GalleryServicePro;