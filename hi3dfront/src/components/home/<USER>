import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronRight } from 'lucide-react';
import Card, { CardImage, CardBody, CardTitle } from '../ui/Card';
import Avatar from '../ui/Avatar';
import Badge from '../ui/Badge';
import Button from '../ui/Button';
import Section from '../layout/Section';
import Grid from '../layout/Grid';

interface Professional {
  id: number;
  firstName: string;
  lastName: string;
  title: string;
  skills: string[];
  rating: number;
  reviewCount: number;
  imageUrl: string;
  location: string;
  availability: 'available' | 'busy' | 'unavailable';
}

interface FeaturedProfessionalsProps {
  professionals: Professional[];
}

const FeaturedProfessionals: React.FC<FeaturedProfessionalsProps> = ({ professionals }) => {
  const navigate = useNavigate();

  return (
    <Section background="white" spacing="lg">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h2 className="text-2xl md:text-3xl font-bold text-neutral-900">Professionnels à découvrir</h2>
          <p className="text-neutral-600 mt-2">Découvrez les meilleurs talents en création 3D</p>
        </div>
        <Button 
          variant="outline"
          rightIcon={<ChevronRight className="h-4 w-4" />}
          onClick={() => navigate('/lists-independants')}
        >
          Voir tous
        </Button>
      </div>

      <Grid cols={1} mdCols={2} lgCols={4} gap={6}>
        {professionals.map((professional) => (
          <Card 
            key={professional.id} 
            hoverable 
            onClick={() => navigate(`/artist/${professional.id}`)}
          >
            <CardImage 
              src={professional.imageUrl} 
              alt={`${professional.firstName} ${professional.lastName}`}
              aspectRatio="video"
            />
            <CardBody className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center">
                  <Avatar 
                    size="md" 
                    fallback={`${professional.firstName[0]}${professional.lastName[0]}`}
                    className="mr-3 border-2 border-white shadow-sm"
                  />
                  <div>
                    <CardTitle className="text-base">{professional.firstName} {professional.lastName}</CardTitle>
                    <p className="text-sm text-neutral-600">{professional.title}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <span className="text-yellow-500 mr-1">★</span>
                  <span className="text-sm font-medium">{professional.rating}</span>
                  <span className="text-xs text-neutral-500 ml-1">({professional.reviewCount})</span>
                </div>
              </div>
              
              <div className="flex flex-wrap gap-1 mb-3">
                {professional.skills.slice(0, 3).map((skill, index) => (
                  <Badge key={index} variant="neutral" size="sm">
                    {skill}
                  </Badge>
                ))}
                {professional.skills.length > 3 && (
                  <Badge variant="neutral" size="sm">
                    +{professional.skills.length - 3}
                  </Badge>
                )}
              </div>
              
              <div className="flex justify-between items-center mt-2 text-sm">
                <span className="text-neutral-600">{professional.location}</span>
                <Badge 
                  variant={
                    professional.availability === 'available' 
                      ? 'success' 
                      : professional.availability === 'busy' 
                        ? 'warning' 
                        : 'error'
                  }
                  size="sm"
                >
                  {professional.availability === 'available' 
                    ? 'Disponible' 
                    : professional.availability === 'busy' 
                      ? 'Occupé' 
                      : 'Indisponible'
                  }
                </Badge>
              </div>
            </CardBody>
          </Card>
        ))}
      </Grid>
    </Section>
  );
};

export default FeaturedProfessionals;
