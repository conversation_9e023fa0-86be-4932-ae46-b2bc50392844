import React from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../ui/Button';
import Section from '../layout/Section';

interface CallToActionProps {
  title?: string;
  description?: string;
  primaryButtonText?: string;
  primaryButtonLink?: string;
  secondaryButtonText?: string;
  secondaryButtonLink?: string;
  backgroundImage?: string;
}

const CallToAction: React.FC<CallToActionProps> = ({
  title = "Prêt à commencer ?",
  description = "Rejoignez Hi 3D Artist dès aujourd'hui et commencez à explorer des opportunités passionnantes dans le monde de la 3D.",
  primaryButtonText = "Trouver un professionnel",
  primaryButtonLink = "/lists-independants",
  secondaryButtonText = "Créer un compte",
  secondaryButtonLink = "/register",
  backgroundImage = "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
}) => {
  const navigate = useNavigate();

  return (
    <div className="relative overflow-hidden" style={{ display: 'block', width: '100%', position: 'relative', zIndex: 1 }}>
      {/* Background Image with Overlay */}
      <div
        className="absolute inset-0 bg-cover bg-center z-0"
        style={{ backgroundImage: `url(${backgroundImage})`, position: 'absolute', top: 0, left: 0, right: 0, bottom: 0 }}
      >
        <div className="absolute inset-0 bg-primary-600 opacity-90" style={{ position: 'absolute', top: 0, left: 0, right: 0, bottom: 0, backgroundColor: '#2980b9' }}></div>
      </div>

      <div className="py-16 relative z-10" style={{ position: 'relative', zIndex: 10, padding: '4rem 0' }}>
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            {title}
          </h2>
          <p className="text-white text-opacity-90 mb-8 max-w-2xl mx-auto">
            {description}
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <button
              type="button"
              onClick={() => navigate(primaryButtonLink)}
              style={{
                backgroundColor: 'white',
                color: '#2980b9',
                padding: '0.75rem 1.5rem',
                borderRadius: '9999px',
                fontWeight: '600',
                fontSize: '1rem',
                border: 'none',
                cursor: 'pointer',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                minWidth: '180px',
                textShadow: 'none'
              }}
            >
              {primaryButtonText}
            </button>
            <button
              type="button"
              onClick={() => navigate(secondaryButtonLink)}
              style={{
                backgroundColor: 'transparent',
                color: 'white',
                padding: '0.75rem 1.5rem',
                borderRadius: '9999px',
                fontWeight: '500',
                fontSize: '1rem',
                border: '1px solid white',
                cursor: 'pointer',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                minWidth: '180px'
              }}
            >
              {secondaryButtonText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallToAction;
