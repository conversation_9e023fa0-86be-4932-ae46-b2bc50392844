// components/EditProfileModal.tsx
"use client";
import React, { useEffect, useMemo, useRef, useState } from "react";
import Modal from "./Modal"; // réutilise ton Modal corrigé (onMouseDown, z-index)
import { API_BASE_URL } from "./../config";
import { getAvatarUrl } from "./../utils/avatarUtils";
import Button from "./ui/Button";
import {
  ArrowLeft,
  Award,
  Briefcase,
  Check,
  ChevronDown,
  Loader2,
  Mail,
  Phone,
  Search,
  Trash2,
  Upload,
  User,
} from "lucide-react";
import LocationSelector from "./../components/ui/LocationSelector";
import SkillPicker, { SkillsByCategory } from "./SkillPicker";

type ProfileData = {
  id?: number;
  user_id?: number;
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  skills: string[];
  avatar?: string;
  cover_photo?: string;
  title?: string;
  hourly_rate?: number;
  languages?: string[];
  services_offered?: string[];
};

const PREDEFINED_LANGUAGES = [
  "Français",
  "Anglais",
  "Espagnol",
  "Allemand",
  "Italien",
  "Portugais",
  "Russe",
  "Chinois",
  "Japonais",
  "Arabe",
];

type Props = {
  open: boolean;
  onClose: () => void;
  onSaved?: (p: ProfileData) => void; // callback après sauvegarde
};

export default function EditProfileModal({ open, onClose, onSaved }: Props) {
  const [activeTab, setActiveTab] = useState<"personal" | "professional" | "skills">("personal");
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [profile, setProfile] = useState<ProfileData>({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    country: "",
    bio: "",
    skills: [],
    avatar: "",
    cover_photo: "",
    title: "",
    hourly_rate: 0,
    languages: [],
    services_offered: [],
  });

  // Cover upload
  const coverInputRef = useRef<HTMLInputElement | null>(null);
  const [coverPreview, setCoverPreview] = useState<string | null>(null);

  // Avatar upload
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

  // Edit helpers
  const [newSkill, setNewSkill] = useState("");
  const [newLanguage, setNewLanguage] = useState("");

  // Liste des compétences par catégorie (ta liste)
const skillsByCategory: SkillsByCategory = {
    modeling: [
      "Blender",
      "Maya",
      "3ds Max",
      "ZBrush",
      "Substance Painter",
      "Hard Surface Modeling",
      "Organic Modeling",
    ],
    animation: [
      "Animation de personnages",
      "Animation d'objets",
      "Motion Capture",
      "Rigging",
      "Facial Animation",
    ],
    architectural: [
      "SketchUp",
      "Revit",
      "ArchiCAD",
      "Lumion",
      "V-Ray",
      "Rendu architectural",
      "Modélisation BIM",
    ],
    product: [
      "Fusion 360",
      "SolidWorks",
      "Rhino 3D",
      "KeyShot",
      "Prototypage 3D",
      "Design industriel",
    ],
    character: [
      "Character Design",
      "Character Modeling",
      "Character Rigging",
      "Facial Rigging",
      "Sculpting",
    ],
    environment: [
      "Environment Design",
      "Landscape Modeling",
      "Terrain Generation",
      "World Building",
      "Level Design",
    ],
    vr_ar: [
      "Unity",
      "Unreal Engine",
      "WebXR",
      "A-Frame",
      "ARKit",
      "ARCore",
      "Oculus SDK",
    ],
    game_art: [
      "Game Asset Creation",
      "Low Poly Modeling",
      "Texture Baking",
      "UV Mapping",
      "PBR Texturing",
    ],
  };

  // Chargement du profil quand la popup s'ouvre
  useEffect(() => {
    if (!open) return;
    (async () => {
      setLoading(true);
      setError(null);
      try {
        const token = localStorage.getItem("token");
        if (!token) throw new Error("Unauthenticated");
        const res = await fetch(`${API_BASE_URL}/api/profile`, {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (!res.ok) throw new Error("Failed to fetch profile");
        const data = await res.json();
        if (data.profile) {
          setProfile((prev) => ({
            ...prev,
            ...data.profile,
            skills: Array.isArray(data.profile.skills) ? data.profile.skills : [],
            languages: Array.isArray(data.profile.languages) ? data.profile.languages : [],
            services_offered: Array.isArray(data.profile.services_offered)
              ? data.profile.services_offered
              : [],
          }));
        }
      } catch (e: any) {
        setError(e?.message || "Could not fetch your profile data");
      } finally {
        setLoading(false);
      }
    })();
  }, [open]);

  // Helpers d’URL
  const avatarUrl = useMemo(() => {
    if (avatarPreview) return avatarPreview;
    if (profile.avatar) return getAvatarUrl(profile.avatar);
    return "https://via.placeholder.com/150";
  }, [avatarPreview, profile.avatar]);

  const coverUrl = useMemo(() => {
    if (coverPreview) return coverPreview;
    if (profile.cover_photo) return getAvatarUrl(profile.cover_photo);
    return "";
  }, [coverPreview, profile.cover_photo]);

  // Handlers de base
  const onField =
    (name: keyof ProfileData) =>
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const value = name === "hourly_rate" ? (e.target.value ? parseFloat(e.target.value) : 0) : e.target.value;
      setProfile((p) => ({ ...p, [name]: value as any }));
    };

  const addSkill = () => {
    const s = newSkill.trim();
    if (!s) return;
    setProfile((p) => (p.skills.includes(s) ? p : { ...p, skills: [...p.skills, s] }));
    setNewSkill("");
  };
  const removeSkill = (s: string) =>
    setProfile((p) => ({ ...p, skills: p.skills.filter((x) => x !== s) }));

  const addLanguage = (l?: string) => {
    const lang = (l ?? newLanguage).trim();
    if (!lang) return;
    setProfile((p) =>
      p.languages?.includes(lang) ? p : { ...p, languages: [...(p.languages ?? []), lang] }
    );
    if (!l) setNewLanguage("");
  };
  const removeLanguage = (l: string) =>
    setProfile((p) => ({ ...p, languages: (p.languages ?? []).filter((x) => x !== l) }));

  // Upload avatar
  const handleAvatar = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onloadend = () => setAvatarPreview(reader.result as string);
    reader.readAsDataURL(file);

    try {
      const token = localStorage.getItem("token");
      if (!token) throw new Error("Unauthenticated");
      const form = new FormData();
      form.append("avatar", file);
      const res = await fetch(`${API_BASE_URL}/api/profile/avatar`, {
        method: "POST",
        headers: { Authorization: `Bearer ${token}` },
        body: form,
      });
      if (!res.ok) throw new Error("Failed to upload avatar");
      const data = await res.json();
      setProfile((p) => ({ ...p, avatar: data.avatar_path }));
      setSuccess("Avatar updated successfully!");
    } catch (e: any) {
      setError(e?.message || "Avatar upload failed");
    }
  };

  const removeAvatar = async () => {
    if (!window.confirm("Remove your avatar?")) return;
    try {
      const token = localStorage.getItem("token");
      if (!token) throw new Error("Unauthenticated");
      const res = await fetch(`${API_BASE_URL}/api/profile/avatar`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}`, "Content-Type": "application/json" },
      });
      if (!res.ok) throw new Error("Failed to remove avatar");
      setAvatarPreview(null);
      setProfile((p) => ({ ...p, avatar: "" }));
      setSuccess("Avatar removed successfully!");
    } catch (e: any) {
      setError(e?.message || "Failed to remove avatar");
    }
  };

  // Upload cover
  const handleCover = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const reader = new FileReader();
    reader.onloadend = () => setCoverPreview(reader.result as string);
    reader.readAsDataURL(file);

    try {
      const token = localStorage.getItem("token");
      if (!token) throw new Error("Unauthenticated");
      const form = new FormData();
      form.append("cover_photo", file);
      const res = await fetch(`${API_BASE_URL}/api/profile/cover`, {
        method: "POST",
        headers: { Authorization: `Bearer ${token}` },
        body: form,
      });
      if (!res.ok) throw new Error("Failed to upload cover");
      const data = await res.json();
      setProfile((p) => ({ ...p, cover_photo: data.cover_url }));
      setSuccess("Cover photo updated successfully!");
    } catch (e: any) {
      setError(e?.message || "Cover photo upload failed");
    }
  };

  // Submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);
    try {
      const token = localStorage.getItem("token");
      if (!token) throw new Error("Unauthenticated");

      const payload = {
        first_name: profile.first_name,
        last_name: profile.last_name,
        phone: profile.phone,
        address: profile.address,
        city: profile.city,
        country: profile.country,
        bio: profile.bio,
        title: profile.title,
        hourly_rate: profile.hourly_rate,
        skills: profile.skills,
        languages: profile.languages,
        services_offered: profile.services_offered,
      };

      const res = await fetch(`${API_BASE_URL}/api/profile`, {
        method: "PUT",
        headers: { Authorization: `Bearer ${token}`, "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const err = await res.json().catch(() => ({}));
        throw new Error(err.message || "Failed to update profile");
      }
      const data = await res.json();
      setProfile((p) => ({ ...p, ...data.profile }));
      setSuccess("Profile updated successfully!");
      onSaved?.(data.profile);
      // Laisse la popup ouverte; ferme si tu veux:
      // onClose();
    } catch (e: any) {
      setError(e?.message || "Profile update failed");
    } finally {
      setSaving(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose} title="Modifier le profil" maxWidthClass="max-w-4xl">
      {loading ? (
        <div className="flex items-center justify-center h-40">
          <Loader2 className="animate-spin h-10 w-10 text-blue-600" />
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="mt-1">
          {/* zone scrollable */}
          <div className="max-h-[70vh] overflow-y-auto pr-2">
            {/* Cover */}
            <div className="relative w-full rounded-2xl overflow-hidden border border-neutral-200 shadow-sm mb-6">
              {coverUrl ? (
                <img src={coverUrl} alt="Cover" className="w-full h-44 object-cover" />
              ) : (
                <div className="w-full h-44 bg-neutral-100 grid place-items-center text-neutral-500 text-sm">
                  Aucune photo de couverture
                </div>
              )}
              <button
                type="button"
                onClick={() => coverInputRef.current?.click()}
                className="absolute bottom-3 right-3 bg-black/70 text-white rounded-full p-2 hover:bg-black/90 transition"
                title="Modifier la photo de couverture"
              >
                <Upload className="w-5 h-5" />
              </button>
              <input
                ref={coverInputRef}
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleCover}
              />
            </div>

            {/* Header profil */}
            <div className="flex items-center gap-4 mb-6">
              <div className="relative">
                <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-neutral-200">
                  <img src={avatarUrl} alt="Avatar" className="w-full h-full object-cover" />
                </div>
                <label
                  htmlFor="avatar-upload"
                  className="absolute bottom-0 right-0 bg-blue-600 text-white rounded-full p-2 hover:bg-blue-700 cursor-pointer"
                  title="Modifier la photo de profil"
                >
                  <Upload className="h-4 w-4" />
                </label>
                <input id="avatar-upload" type="file" className="hidden" accept="image/*" onChange={handleAvatar} />
              </div>

              <div className="flex gap-2">
                {profile.avatar ? (
                  <button
                    type="button"
                    onClick={removeAvatar}
                    className="text-xs px-3 py-1 bg-red-50 text-red-600 rounded border border-red-200 hover:bg-red-100 flex items-center"
                  >
                    <Trash2 className="h-3.5 w-3.5 mr-1" /> Supprimer
                  </button>
                ) : null}
              </div>
            </div>

            {/* Tabs */}
            <div className="sticky top-0 bg-white/70 backdrop-blur z-10 -mx-6 px-6 border-b border-neutral-200 mb-6">
              <div className="flex gap-4 overflow-x-auto">
                {[
                  { id: "personal", label: "Infos personnelles" },
                  { id: "professional", label: "Détails pro" },
                  { id: "skills", label: "Compétences & Langues" },
                ].map((t) => (
                  <button
                    key={t.id}
                    type="button"
                    onClick={() => setActiveTab(t.id as any)}
                    className={`py-3 border-b-2 font-medium text-sm ${
                      activeTab === t.id ? "border-blue-600 text-blue-600" : "border-transparent text-neutral-500 hover:text-neutral-700"
                    }`}
                  >
                    {t.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Content */}
            <div className="space-y-6">
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                  {error}
                </div>
              )}
              {success && (
                <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                  {success}
                </div>
              )}

              {activeTab === "personal" && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {[
                      { id: "first_name", label: "Prénom", icon: <User className="h-5 w-5 text-neutral-400" /> },
                      { id: "last_name", label: "Nom", icon: <User className="h-5 w-5 text-neutral-400" /> },
                      { id: "email", label: "E-mail", icon: <Mail className="h-5 w-5 text-neutral-400" />, disabled: true },
                      { id: "phone", label: "Téléphone", icon: <Phone className="h-5 w-5 text-neutral-400" /> },
                    ].map((f) => (
                      <div key={f.id}>
                        <label htmlFor={f.id} className="block text-sm font-medium text-neutral-700 mb-1">
                          {f.label}
                        </label>
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            {f.icon}
                          </div>
                          <input
                            id={f.id}
                            name={f.id}
                            type={f.id === "email" ? "email" : "text"}
                            value={(profile as any)[f.id] ?? ""}
                            onChange={onField(f.id as keyof ProfileData)}
                            className={`block w-full pl-10 pr-3 py-2 border rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 ${
                              f.disabled ? "bg-neutral-50 text-neutral-500 border-neutral-300" : "border-neutral-300"
                            }`}
                            disabled={!!f.disabled}
                            required={!f.disabled}
                          />
                        </div>
                      </div>
                    ))}
                  </div>

                  <LocationSelector
                    country={profile.country}
                    city={profile.city}
                    address={profile.address}
                    onCountryChange={(country: string) => setProfile((p) => ({ ...p, country }))}
                    onCityChange={(city: string) => setProfile((p) => ({ ...p, city }))}
                    onAddressChange={(address: string) => setProfile((p) => ({ ...p, address }))}
                    required
                  />
                </div>
              )}

              {activeTab === "professional" && (
                <div className="space-y-6">
                  <div>
                    <label htmlFor="title" className="block text-sm font-medium text-neutral-700 mb-1">
                      Titre professionnel
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Briefcase className="h-5 w-5 text-neutral-400" />
                      </div>
                      <input
                        id="title"
                        name="title"
                        type="text"
                        value={profile.title ?? ""}
                        onChange={onField("title")}
                        className="block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g. Senior 3D Artist"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="hourly_rate" className="block text-sm font-medium text-neutral-700 mb-1">
                      Tarif horaire (€)
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Award className="h-5 w-5 text-neutral-400" />
                      </div>
                      <input
                        id="hourly_rate"
                        name="hourly_rate"
                        type="number"
                        min="0"
                        step="0.01"
                        value={profile.hourly_rate ?? 0}
                        onChange={onField("hourly_rate")}
                        className="block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                        placeholder="e.g. 25"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="bio" className="block text-sm font-medium text-neutral-700 mb-1">
                      Biographie / À propos de moi
                    </label>
                    <textarea
                      id="bio"
                      name="bio"
                      rows={5}
                      value={profile.bio}
                      onChange={onField("bio")}
                      className="block w-full px-3 py-2 border border-neutral-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                      placeholder="Parle de toi, de ton expérience et de ton expertise…"
                    />
                  </div>
                </div>
              )}

              {activeTab === "skills" && (
                <div className="space-y-8">
                  {/* Skills */}
                  {/* <div>
                    <h3 className="text-lg font-medium text-neutral-900 mb-3">Compétences</h3>
                    <div className="flex">
                      <input
                        type="text"
                        value={newSkill}
                        onChange={(e) => setNewSkill(e.target.value)}
                        onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addSkill())}
                        className="block w-full px-3 py-2 border border-neutral-300 rounded-l-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                        placeholder="Ajouter une compétence…"
                      />
                      <button
                        type="button"
                        onClick={addSkill}
                        className="px-4 py-2 rounded-r-lg bg-blue-600 text-white hover:bg-blue-700"
                      >
                        Ajouter
                      </button>
                    </div>

                    <div className="p-4 border border-neutral-200 rounded-lg bg-neutral-50 min-h-[88px] mt-3">
                      {profile.skills.length ? (
                        <div className="flex flex-wrap gap-2">
                          {profile.skills.map((s) => (
                            <span
                              key={s}
                              className="inline-flex items-center px-3 py-1 rounded-full bg-white border border-neutral-300 shadow-sm text-sm"
                            >
                              {s}
                              <button
                                type="button"
                                onClick={() => removeSkill(s)}
                                className="ml-1.5 text-neutral-500 hover:text-red-500"
                                aria-label={`Supprimer ${s}`}
                                title={`Supprimer ${s}`}
                              >
                                ×
                              </button>
                            </span>
                          ))}
                        </div>
                      ) : (
                        <div className="text-neutral-500 text-sm">Aucune compétence ajoutée</div>
                      )}
                    </div>
                  </div> */}

                <div>
                    <h3 className="text-lg font-medium text-neutral-900 mb-3">Compétences</h3>
                    <SkillPicker
                        categories={skillsByCategory}
                        value={profile.skills}
                        onChange={(skills) => setProfile((p) => ({ ...p, skills }))}
                        allowCustom={false} // mets à true si tu veux garder l'ajout manuel
                    />
                    </div>

                  {/* Languages */}
                  <div>
                    <h3 className="text-lg font-medium text-neutral-900 mb-3">Langues</h3>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 mb-3">
                      {PREDEFINED_LANGUAGES.map((lang) => {
                        const isSel = (profile.languages ?? []).includes(lang);
                        return (
                          <button
                            key={lang}
                            type="button"
                            onClick={() => (isSel ? removeLanguage(lang) : addLanguage(lang))}
                            className={`flex items-center justify-between px-3 py-2 rounded-lg border text-sm ${
                              isSel
                                ? "bg-blue-50 border-blue-200 text-blue-700"
                                : "bg-white border-neutral-200 hover:bg-neutral-50"
                            }`}
                          >
                            {lang}
                            {isSel && <Check className="w-4 h-4" />}
                          </button>
                        );
                      })}
                    </div>

                    <div className="flex">
                      <input
                        type="text"
                        value={newLanguage}
                        onChange={(e) => setNewLanguage(e.target.value)}
                        onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addLanguage())}
                        className="block w-full px-3 py-2 border border-neutral-300 rounded-l-lg shadow-sm focus:ring-2 focus:ring-blue-500"
                        placeholder="Ajouter une langue…"
                      />
                      <button
                        type="button"
                        onClick={() => addLanguage()}
                        className="px-4 py-2 rounded-r-lg bg-blue-600 text-white hover:bg-blue-700"
                      >
                        Ajouter
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer sticky */}
          <div className="pt-5 mt-6 border-t border-neutral-200 flex gap-3 justify-end">
            <Button variant="outline" onClick={onClose} leftIcon={<ArrowLeft className="h-5 w-5" />}>
              Annuler
            </Button>
            <Button
              variant="primary"
              type="submit"
              disabled={saving}
              leftIcon={saving ? <Loader2 className="animate-spin h-5 w-5" /> : undefined}
            >
              {saving ? "Enregistrement…" : "Enregistrer"}
            </Button>
          </div>
        </form>
      )}
    </Modal>
  );
}
