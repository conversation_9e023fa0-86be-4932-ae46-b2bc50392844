"use client";
import React, { useEffect, useState, ChangeEvent } from "react";
import Modal from "./Modal";
import { Save, Upload, Trash2, X } from "lucide-react";
import { API_BASE_URL } from "../config";
import { profileService } from "../services/profileService";
import { getAvatarUrl, getInitials } from "../utils/avatarUtils";
import { useToast } from "../context/ToastContext";
import LocationSelector from "./ui/LocationSelector";

interface ClientProfileFormData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  bio: string;
  birth_date: string;
  company_name: string;
  company_size: string;
  industry: string;
  position: string;
  website: string;
  social_links: {
    linkedin: string;
    twitter: string;
    facebook: string;
    instagram: string;
  };
  avatar?: File | null;
  current_avatar?: string;
}

function formatDateForInput(dateString: string | null | undefined): string {
  if (!dateString) return "";
  return dateString.split("T")[0] || dateString.split(" ")[0];
}

export default function ClientProfileEditModal({
  open,
  onClose,
  onSaved,
  title = "Modifier mon profil",
  portal = true,
}: {
  open: boolean;
  onClose: () => void;
  onSaved?: (data: any) => void; // callback facultatif après sauvegarde
  title?: string;
  portal?: boolean; // passe à false si modale imbriquée dans une autre
}) {
  const { showToast } = useToast();

  const [formData, setFormData] = useState<ClientProfileFormData>({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    country: "",
    bio: "",
    birth_date: "",
    company_name: "",
    company_size: "",
    industry: "",
    position: "",
    website: "",
    social_links: { linkedin: "", twitter: "", facebook: "", instagram: "" },
    avatar: null,
    current_avatar: "",
  });

  const [loading, setLoading] = useState<boolean>(false);
  const [saving, setSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

  // Charger le profil à l’ouverture
  useEffect(() => {
    if (!open) return;
    (async () => {
      setLoading(true);
      setError(null);
      try {
        const data = await profileService.getProfile();
        const profile = data.profile;

        setFormData({
          first_name: profile.first_name || "",
          last_name: profile.last_name || "",
          email: profile.email || "",
          phone: profile.phone || "",
          address: profile.address || "",
          city: profile.city || "",
          country: profile.country || "",
          bio: profile.bio || "",
          birth_date: formatDateForInput(profile.birth_date),
          company_name: profile.company_name || "",
          company_size: profile.company_size || "",
          industry: profile.industry || "",
          position: profile.position || "",
          website: profile.website || "",
          social_links: {
            linkedin: profile.social_links?.linkedin || "",
            twitter: profile.social_links?.twitter || "",
            facebook: profile.social_links?.facebook || "",
            instagram: profile.social_links?.instagram || "",
          },
          avatar: null,
          current_avatar: profile.avatar || "",
        });

        setAvatarPreview(profile.avatar ? getAvatarUrl(profile.avatar) || "" : null);
      } catch (err) {
        console.error("Error fetching profile:", err);
        setError("Impossible de charger le profil. Veuillez réessayer plus tard.");
      } finally {
        setLoading(false);
      }
    })();
  }, [open]);

  const handleInputChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;

    if (name.includes(".")) {
      const [parent, child] = name.split(".");
      setFormData((prev) => {
        const parentObj = prev[parent as keyof ClientProfileFormData];
        if (typeof parentObj === "object" && parentObj !== null) {
          return { ...prev, [parent]: { ...(parentObj as any), [child]: value } };
        }
        return prev;
      });
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleAvatarChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFormData((prev) => ({ ...prev, avatar: file }));

      const reader = new FileReader();
      reader.onloadend = () => setAvatarPreview(reader.result as string);
      reader.readAsDataURL(file);
    }
  };

  const removeAvatar = () => {
    setFormData((prev) => ({ ...prev, avatar: null, current_avatar: "" }));
    setAvatarPreview(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      if (formData.avatar) {
        const formDataToSend = new FormData();
        const copy = { ...formData };
        delete (copy as any).avatar;
        delete (copy as any).current_avatar;

        formDataToSend.append("profile_data", JSON.stringify(copy));
        formDataToSend.append("avatar", formData.avatar);

        const response = await profileService.updateProfileWithAvatar(formDataToSend);
        showToast("success", "Profil mis à jour avec succès!");
        onSaved?.(response);
        onClose();
      } else {
        const dataToSend = { ...formData };
        delete (dataToSend as any).avatar;

        const response = await profileService.updateProfileJSON(dataToSend);
        showToast("success", "Profil mis à jour avec succès!");
        onSaved?.(response);
        onClose();
      }
    } catch (apiError: any) {
      console.error("Erreur API lors de la mise à jour du profil:", apiError);
      const msg = apiError instanceof Error ? apiError.message : "Erreur inconnue";
      setError(`Erreur API: ${msg}`);
    } finally {
      setSaving(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose} title={title} maxWidthClass="max-w-4xl" portal={portal}>
      {loading ? (
        <div className="flex items-center justify-center h-60">
          <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-black/70" />
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="mt-2">
          <div className="max-h-[70vh] overflow-y-auto pr-2 space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">{error}</div>
            )}

            {/* Avatar */}
            <section className="bg-white rounded-2xl border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <h2 className="text-lg font-semibold text-neutral-900">Photo de profil</h2>
              </div>
              <div className="p-6">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-6">
                  <div className="relative">
                    <div className="w-24 h-24 rounded-full overflow-hidden bg-neutral-200 flex items-center justify-center">
                      {avatarPreview ? (
                        <img
                          src={avatarPreview}
                          alt="Avatar preview"
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            (e.currentTarget as HTMLImageElement).style.display = "none";
                            setAvatarPreview(null);
                          }}
                        />
                      ) : (
                        <span className="text-3xl font-bold text-neutral-400">
                          {getInitials(formData.first_name, formData.last_name)}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col space-y-3">
                    <label className="inline-flex items-center px-4 py-2 border border-neutral-300 rounded-md shadow-sm text-sm font-medium text-neutral-700 bg-white hover:bg-neutral-50 cursor-pointer">
                      <Upload className="h-4 w-4 mr-2" />
                      Choisir une image
                      <input type="file" className="hidden" accept="image/*" onChange={handleAvatarChange} />
                    </label>

                    {avatarPreview && (
                      <button
                        type="button"
                        onClick={removeAvatar}
                        className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Supprimer
                      </button>
                    )}
                    <p className="text-xs text-neutral-500">Formats acceptés : JPG, PNG. Taille max : 2 Mo.</p>
                  </div>
                </div>
              </div>
            </section>

            {/* Infos personnelles */}
            <section className="bg-white rounded-2xl border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <h2 className="text-lg font-semibold text-neutral-900">Informations personnelles</h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Prénom *</label>
                    <input
                      type="text"
                      name="first_name"
                      value={formData.first_name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Nom *</label>
                    <input
                      type="text"
                      name="last_name"
                      value={formData.last_name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Email *</label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Téléphone</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Date de naissance</label>
                    <input
                      type="date"
                      name="birth_date"
                      value={formData.birth_date}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                </div>

                <div className="mt-6">
                  <label className="block text-sm font-medium text-neutral-700 mb-1">Biographie</label>
                  <textarea
                    name="bio"
                    value={formData.bio}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Parlez-nous de vous..."
                  />
                </div>
              </div>
            </section>

            {/* Adresse */}
            <section className="bg-white rounded-2xl border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <h2 className="text-lg font-semibold text-neutral-900">Adresse</h2>
              </div>
              <div className="p-6">
                <LocationSelector
                  country={formData.country}
                  city={formData.city}
                  address={formData.address}
                  onCountryChange={(country) => setFormData({ ...formData, country })}
                  onCityChange={(city) => setFormData({ ...formData, city })}
                  onAddressChange={(address) => setFormData({ ...formData, address })}
                />
              </div>
            </section>

            {/* Infos pro */}
            <section className="bg-white rounded-2xl border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <h2 className="text-lg font-semibold text-neutral-900">Informations professionnelles</h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Nom de l'entreprise</label>
                    <input
                      type="text"
                      name="company_name"
                      value={formData.company_name}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Poste</label>
                    <input
                      type="text"
                      name="position"
                      value={formData.position}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Secteur d'activité</label>
                    <select
                      name="industry"
                      value={formData.industry}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    >
                      <option value="">Sélectionnez un secteur</option>
                      <option value="Technology">Technologie</option>
                      <option value="Finance">Finance</option>
                      <option value="Healthcare">Santé</option>
                      <option value="Education">Éducation</option>
                      <option value="Retail">Commerce de détail</option>
                      <option value="Manufacturing">Fabrication</option>
                      <option value="Media">Médias</option>
                      <option value="Construction">Construction</option>
                      <option value="Transportation">Transport</option>
                      <option value="Energy">Énergie</option>
                      <option value="Other">Autre</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Taille de l'entreprise</label>
                    <select
                      name="company_size"
                      value={formData.company_size}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    >
                      <option value="">Sélectionnez une taille</option>
                      <option value="1-10">1-10 employés</option>
                      <option value="11-50">11-50 employés</option>
                      <option value="51-200">51-200 employés</option>
                      <option value="201-500">201-500 employés</option>
                      <option value="501-1000">501-1000 employés</option>
                      <option value="1001+">1001+ employés</option>
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Site web</label>
                    <input
                      type="url"
                      name="website"
                      value={formData.website}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="https://..."
                    />
                  </div>
                </div>
              </div>
            </section>

            {/* Réseaux sociaux */}
            <section className="bg-white rounded-2xl border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <h2 className="text-lg font-semibold text-neutral-900">Réseaux sociaux</h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">LinkedIn</label>
                    <input
                      type="url"
                      id="social_links.linkedin"
                      name="social_links.linkedin"
                      value={formData.social_links.linkedin}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="https://linkedin.com/in/..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Twitter</label>
                    <input
                      type="url"
                      id="social_links.twitter"
                      name="social_links.twitter"
                      value={formData.social_links.twitter}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="https://twitter.com/..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Facebook</label>
                    <input
                      type="url"
                      id="social_links.facebook"
                      name="social_links.facebook"
                      value={formData.social_links.facebook}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="https://facebook.com/..."
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-neutral-700 mb-1">Instagram</label>
                    <input
                      type="url"
                      id="social_links.instagram"
                      name="social_links.instagram"
                      value={formData.social_links.instagram}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="https://instagram.com/..."
                    />
                  </div>
                </div>
              </div>
            </section>
          </div>

          {/* Footer sticky */}
          <div className="mt-6 pt-5 border-t border-neutral-200 flex items-center justify-end gap-3">
            <button
              type="button"
              onClick={onClose}
              className="inline-flex items-center px-4 py-2 rounded-md border border-neutral-300 bg-white hover:bg-neutral-50 text-sm"
            >
              <X className="h-4 w-4 mr-2" />
              Annuler
            </button>
            <button
              type="submit"
              disabled={saving}
              className="inline-flex items-center px-4 py-2 rounded-md bg-[#2980b9] text-white hover:opacity-90 disabled:opacity-70 text-sm font-semibold"
            >
              <Save className="h-4 w-4 mr-2" />
              {saving ? "Enregistrement..." : "Enregistrer"}
            </button>
          </div>
        </form>
      )}
    </Modal>
  );
}
