import React, { useState } from "react";

interface MessagesBriefAttachementTabProps {
  onTabChange?: (tab: string) => void;
}

const MessagesBriefAttachementTab: React.FC<MessagesBriefAttachementTabProps> = ({ 
  onTabChange 
}) => {
  const [selectedTab, setSelectedTab] = useState("Messages");

  const handleTabClick = (tab: string) => {
    setSelectedTab(tab);
    if (onTabChange) {
      onTabChange(tab);
    }
  };

  return (
    <div className="bg-white w-full flex items-stretch mt-2">
      <div className="w-full px-[10px] md:px-[40px] mx-auto grid grid-cols-1 gap-10 items-end">
        <div className="flex items-center space-x-4 mb-6">
          <button
            type="button"
            className={`border-none rounded-full py-2 px-6 font-sans font-medium text-sm h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm
              ${
                selectedTab === "Messages"
                  ? "bg-blue-600 text-white hover:bg-[#F5F5F5] hover:text-gray-500"
                  : "bg-[#F5F5F5] text-gray-500 hover:bg-blue-600 hover:text-white"
              }`}
            style={{ fontFamily: "Arial, sans-serif", fontSize: "16px" }}
            onClick={() => handleTabClick("Messages")}
          >
            Messages
          </button>
          
          <button
            type="button"
            className={`border-none rounded-full py-2 px-6 font-sans font-medium text-sm h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm
              ${
                selectedTab === "Brief"
                  ? "bg-blue-600 text-white hover:bg-[#F5F5F5] hover:text-gray-500"
                  : "bg-[#F5F5F5] text-gray-500 hover:bg-blue-600 hover:text-white"
              }`}
            style={{ fontFamily: "Arial, sans-serif", fontSize: "16px" }}
            onClick={() => handleTabClick("Brief")}
          >
            Brief
          </button>
          
          <button
            type="button"
            className={`border-none rounded-full py-2 px-6 font-sans font-medium text-sm h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm
              ${
                selectedTab === "Attachments"
                  ? "bg-blue-600 text-white hover:bg-[#F5F5F5] hover:text-gray-500"
                  : "bg-[#F5F5F5] text-gray-500 hover:bg-blue-600 hover:text-white"
              }`}
            style={{ fontFamily: "Arial, sans-serif", fontSize: "16px" }}
            onClick={() => handleTabClick("Attachments")}
          >
            Attachments
          </button>
        </div>
      </div>
    </div>
  );
};

export default MessagesBriefAttachementTab;