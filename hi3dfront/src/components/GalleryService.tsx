import React, { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { API_BASE_URL } from "../config";

type Props = {
  items: any[] | undefined;
  marginBottom?: number | string;
  onDislike?: (id: number) => void;
  query?: string;
  executionTimes?:any;
};

const GalleryService: React.FC<Props> = ({
  items,
  marginBottom,
  onDislike,
  query,
  executionTimes,
}) => {
  const navigate = useNavigate();
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const [currentPage, setCurrentPage] = useState(1);
  const [services, setServices] = useState<any[]>(items || []);
  const perPage = 6; // nombre d’items affichés par page

  const token = localStorage.getItem('token');

  // throttle légère du resize pour réduire les re-renders
  useEffect(() => {
    let timeoutId: number | null = null;
    const handleResize = () => {
      if (timeoutId !== null) return;
      timeoutId = window.setTimeout(() => {
        setScreenWidth(window.innerWidth);
        timeoutId = null;
      }, 150);
    };

    window.addEventListener("resize", handleResize);
    return () => {
      if (timeoutId !== null) window.clearTimeout(timeoutId);
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const gridColsClass = useMemo(() => {
    if (screenWidth >= 1025) {
      return "lg:grid-cols-3"; // 3 colonnes
    } else if (screenWidth >= 768) {
      return "md:grid-cols-2"; // 2 colonnes
    } else if (screenWidth >= 767) {
      return "grid-cols-2"; // 2 colonnes (767px)
    } else {
      return "grid-cols-1"; // 1 colonne
    }
  }, [screenWidth]);

  // helper non utilisé supprimé (statuts gérés par l'effet batched)

  // garder services en phase avec items si pas connecté
  useEffect(() => {
    setServices(items || []);
  }, [items]);

  // 🔸 Charger statut de tous les services à l'ouverture (batch + annulation)
  const statusesAbortControllerRef = useRef<AbortController | null>(null);
  useEffect(() => {
    const fetchAllStatuses = async () => {
      if (!items || !token || items.length === 0) return;

      // rendu immédiat
      setServices(items);

      // annuler série précédente
      if (statusesAbortControllerRef.current) {
        statusesAbortControllerRef.current.abort();
      }
      const controller = new AbortController();
      statusesAbortControllerRef.current = controller;

      try {
        const batchSize = 6; // limiter la concurrence
        const results: any[] = [];

        for (let i = 0; i < items.length; i += batchSize) {
          const slice = items.slice(i, i + batchSize);
          const batch = await Promise.all(
            slice.map(async (item) => {
              try {
                const response = await fetch(
                  `${API_BASE_URL}/api/service-offers/${item.id}/like/status`,
                  {
                    method: "GET",
                    headers: {
                      Accept: "application/json",
                      Authorization: `Bearer ${token}`,
                    },
                    signal: controller.signal,
                  }
                );
                const data = await response.json();
                if (data.success) {
                  return {
                    ...item,
                    liked: data.data.liked,
                    likes: data.data.total_likes,
                  };
                }
              } catch (err) {
                // ignorer pour ne pas bloquer la série
              }
              return item;
            })
          );
          results.push(...batch);
        }

        setServices(results);
      } catch (error) {
        if ((error as any)?.name !== "AbortError") {
          console.error("Erreur statut like :", error);
        }
      }
    };

    fetchAllStatuses();

    return () => {
      if (statusesAbortControllerRef.current) {
        statusesAbortControllerRef.current.abort();
      }
    };
  }, [items, token]);



const toggleLikeService = async (id : number, isLiked : boolean) => {
  if (!token) {
  // alert("Vous devez être connecté pour liker un service.");
    navigate("/login");
    return;
  }
  setServices((prev) =>
    prev.map((item) =>
      item.id === id
        ? {
            ...item,
            likes: isLiked? item.likes-1 : item.likes+1,
            liked: isLiked? false : true,
          }
        : item
    )
  );
  try {
    const url = `${API_BASE_URL}/api/service-offers/${id}/like`;
    const options = {
      method: isLiked ? "DELETE" : "POST",
      headers: {
        "Accept": "application/json",
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`,
      },
    };

    const response = await fetch(url, options);
    const data = await response.json();

    if (response.ok && data.success) {
      setServices((prev) =>
        prev.map((item) =>
          item.id === id
            ? {
                ...item,
                likes: data.data.total_likes,
                liked: data.data.liked,
              }
            : item
        )
      );
    } else {
      alert(data.message || "Erreur lors du like");
    }
  } catch (error) {
    console.error("Erreur API:", error);
    alert("Impossible de se connecter au serveur.");
  }
};


  // toggleLike conservé plus haut comme référence; l'action réelle utilise toggleLikeService

  const isEmpty = !items || items.length === 0;

   // Découpage front
  // const indexOfLast = currentPage * perPage;
  // const indexOfFirst = indexOfLast - perPage;
  // const currentServices = items.slice(indexOfFirst, indexOfLast);

  // const handlePrev = () => {
  //   if (currentPage > 1) setCurrentPage((p) => p - 1);
  // };

  // const handleNext = () => {
  //   if (currentPage < Math.ceil(items.length / perPage)) setCurrentPage((p) => p + 1);
  // };

  // 🔸 Pagination
  const indexOfLast = currentPage * perPage;
  const indexOfFirst = indexOfLast - perPage;
  const currentServices = useMemo(() => services.slice(indexOfFirst, indexOfLast), [services, indexOfFirst, indexOfLast]);

  const handlePrev = useCallback(() => {
    if (currentPage > 1) setCurrentPage((p) => p - 1);
  }, [currentPage]);

  const totalPages = useMemo(() => Math.ceil(services.length / perPage), [services.length]);
  const handleNext = useCallback(() => {
    if (currentPage < totalPages) setCurrentPage((p) => p + 1);
  }, [currentPage, totalPages]);

  return (
    <div
      className="w-full mx-auto"
      style={{marginBottom: marginBottom || "0px"}}
    >
      {isEmpty && (
        <div className="text-center py-20 text-gray-600 flex flex-col items-center justify-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-24 h-24 text-gray-400 mb-6"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            strokeWidth={1.5}
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="M3 7a2 2 0 012-2h5l2 2h7a2 2 0 012 2v7a2 2 0 01-2 2H5a2 2 0 01-2-2V7z"
            />
          </svg>
          <h2 className="text-2xl font-semibold">No Services found</h2>
          <p className="text-gray-500 mt-2 max-w-md">
            We couldn't find any services matching your search or category. Try
            again with a different filter.
          </p>
        </div>
      )}
      {query && query.trim() !== "" && (
        <div className="mb-8 px-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-3">
            {query} Services
          </h1>
          <p className="text-lg text-gray-600">{items?.length || 0} results found in {executionTimes}ms</p>
        </div>
      )}

      {!isEmpty && (
      <div
        className={`grid ${gridColsClass} gap-6 px-4 justify-items-center mb-8  px-[10px] md:px-[40px]`}
      >
        {currentServices?.map((item) => {
          // const primaryImage = Array.isArray(item.files) && item.files.length > 0
          //   ? getImageService(item.files[0]?.path)
          //   : getImageService(item.image);
          const primaryImage = item?.image_url
          const professionalName = item?.user ? `${item.user.first_name || ""} ${item.user.last_name || ""}`.trim() : "";
          // const avatar = getAvatarUrl(item?.user?.avatar);
          const avatar = item?.avatar;
          const views = item?.views ?? 0;
          const likes = item?.likes ?? 0;
          const price = item?.price ?? "";
          return (
            <div
              key={item.id}
              className="group w-full bg-white overflow-hidden relative flex flex-col cursor-pointer h-[410px]"
            >
              {/* Image container (60% height) */}
              <div 
              className="relative w-full h-[340px]"
              onClick={() => {
                navigate("/details-search", {
                  state: { service: item },
                });
              }}
              >
                <div
                  style={{
                    backgroundImage: `url(${primaryImage})`,
                    borderRadius: "5px",
                  }}
                  className="absolute inset-0 bg-cover bg-center z-1000"
                />
              </div>

              {/* Content container (40% height) */}
              <div className="px-0 py-0 pr-4 flex flex-col mb-4 h-[70px]">
                {/* Titre */}
                <div className="flex items-center min-h-[27px] pt-4 pb-4">
                  <h2
                    className="font-medium text-[18px] line-clamp-1 flex-1"
                    style={{
                      fontSize: "16px",
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: 800,
                      lineHeight: "30px",
                    }}
                    onClick={() => {
                      navigate("/details-search", {
                        state: { service: item },
                      });
                    }}
                  >
                    {item.title}
                  </h2>
                  <div
                    className="flex items-center gap-4"
                    style={{ fontFamily: "'Inter', sans-serif" }}
                  >
                    <span className="flex items-center gap-1 text-[12px]">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="ionicon"
                        viewBox="0 0 512 512"
                        width="14"
                        height="14"
                        fill="#787777ff"
                      >
                        <circle cx="256" cy="256" r="64" />
                        <path d="M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96c-42.52 0-84.33 12.15-124.27 36.11-40.73 24.43-77.63 60.12-109.68 106.07a31.92 31.92 0 00-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416c46.71 0 93.81-14.43 136.2-41.72 38.46-24.77 72.72-59.66 99.08-100.92a32.2 32.2 0 00-.1-34.76zM256 352a96 96 0 1196-96 96.11 96.11 0 01-96 96z" />
                      </svg>
                      <p
                        style={{
                          fontSize: "12px",
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: 400,
                          lineHeight: "30px",
                        }}
                      >
                        {views}
                      </p>
                    </span>

                    <span 
                    className="flex items-center gap-1 text-[12px]"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (typeof item?.id === "number") toggleLikeService(item.id, item.liked); //toggle
                    }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="ionicon"
                        viewBox="0 0 512 512"
                        width="14"
                        height="14"
                        fill={item.liked ? "#ef4444" : "#787777ff"}
                      >
                        <path d="M400 480a16 16 0 01-10.63-4L256 357.41 122.63 476A16 16 0 0196 464V96a64.07 64.07 0 0164-64h192a64.07 64.07 0 0164 64v368a16 16 0 01-16 16z"></path>
                      </svg>
                      <p
                        style={{
                          fontSize: "12px",
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: 400,
                          lineHeight: "30px",
                        }}
                      >
                        {likes}
                      </p>
                    </span>
                  </div>
                </div>

                <div className="flex justify-between items-end">
                  {/* Auteur */}
                  <div className="flex items-center gap-2">
                    <img
                      src={avatar}
                      alt={professionalName}
                      className="w-6 h-6 rounded-full object-cover border-white"
                    />
                    <div className="flex items-center gap-1">
                      <h3
                        className="font-medium leading-tight mr-1 hover:text-black transition-colors duration-200"
                        style={{
                          fontSize: "12px",
                          fontFamily: "'Inter', sans-serif",
                          color: "#313131ff",
                          fontWeight: 300,
                        }}
                      >
                        {professionalName}
                      </h3>
                    </div>
                  </div>
                </div>

                <p
                  className="pt-3 pb-3 mb-3"
                  style={{
                    fontSize: "13px",
                    fontFamily: "'Inter', sans-serif",
                    fontWeight: 400,
                  }}
                >
                  USD <strong>{price}</strong>
                </p>
              </div>
            </div>
          );
        })}
      </div>
      )}

      {!isEmpty && services.length > 6 && (
        <div className="flex justify-center items-center gap-4 mt-6">
          <button
            className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
            style={{
                    fontFamily: "'Inter', sans-serif",
                  }}
            onClick={handlePrev}
            disabled={currentPage === 1}
          >
            Précédent
          </button>
          {/* <span className="text-sm text-gray-700">
            Page {currentPage} / {totalPages}
          </span> */}
          <button
            className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
            style={{
                    fontFamily: "'Inter', sans-serif",
                  }}
            onClick={handleNext}
            disabled={currentPage === totalPages}
          >
            Suivant
          </button>
        </div>
      )}
      
    </div>
  );
};

export default GalleryService;
