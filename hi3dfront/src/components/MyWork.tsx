import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Link } from 'react-router-dom';
import { API_BASE_URL } from '../config';
import Button from './ui/Button';

type Project = {
  id: number;
  title: string;
  cover_photo: string;
  description: string;
  likes?: number;
  views?: number;
};

const MyWork: React.FC = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<number | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/api/dashboard/projects`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          }
        });

        if (!response.ok) {
          throw new Error('Failed to fetch projects');
        }

        const data = await response.json();
        // The API returns { projects: [...] }, so we need to access data.projects
        setProjects(data.projects || []);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const handleDelete = async () => {
    if (!projectToDelete) return;

    try {
      const token = localStorage.getItem('token');
      await fetch(`${API_BASE_URL}/api/dashboard/projects/${projectToDelete}`, {
        method: 'DELETE',
        headers: { 'Authorization': `Bearer ${token}` }
      });
      setProjects(projects.filter(p => p.id !== projectToDelete));
      setShowDeleteModal(false);
      setProjectToDelete(null);
    } catch (error) {
      console.error('Erreur lors de la suppression', error);
    }
  };

  const getImageUrl = (imagePath: string) => {
    if (!imagePath) return '';
    if (imagePath.startsWith('http')) return imagePath;
    return `${API_BASE_URL.replace('/api', '')}/storage/${imagePath}`;
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 sm:gap-8">
      {projects.map(item => (
        <div
          key={item.id}
          className="group w-full max-w-[315px] bg-white rounded-lg overflow-hidden relative shadow-none flex flex-col"
        >
          {/* Bouton de modification en haut à droite, visible au survol */}
          <div
            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 flex space-x-2"
          >
            <button
              onClick={e => {
                e.stopPropagation();
                navigate(`/dashboard/create-project/${item.id}`);
              }}
              className="bg-white rounded-full p-2 shadow hover:bg-blue-100"
              title="Modifier ce projet"
            >
              <svg width="20" height="20" fill="none" stroke="#2980b9" strokeWidth="2" viewBox="0 0 24 24">
                <path d="M15.232 5.232l3.536 3.536M9 13l6-6 3 3-6 6H9v-3z"/>
                <path d="M16 7l1.5-1.5a2.121 2.121 0 1 1 3 3L19 10"/>
              </svg>
            </button>
            <button
              onClick={e => {
                e.stopPropagation();
                setProjectToDelete(item.id);
                setShowDeleteModal(true);
              }}
              className="bg-white rounded-full p-2 shadow hover:bg-red-100"
              title="Supprimer ce projet"
            >
              <svg width="20" height="20" fill="none" stroke="#e74c3c" strokeWidth="2" viewBox="0 0 24 24">
                <path d="M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"/>
              </svg>
            </button>
          </div>
          <div
            style={{
              backgroundImage: `url(${getImageUrl(item.cover_photo)})`,
            }}
            className="bg-[#2d241b] bg-cover bg-center w-full h-[220px] rounded-lg"
            aria-label={item.title}
            role="img"
          />
          <div
            className="w-full flex justify-between items-center px-0 pt-[18px] pb-2.5 min-h-[60px] text-[12px]" style={{ fontFamily: 'Arial, sans-serif' }}
          >
            <div className="flex items-center gap-2.5">
              <span className="font-semibold truncate max-w-[180px]">{item.title}</span>
            </div>
            <div className="flex items-center gap-4 text-[#888]">
              <span className="flex items-center gap-1">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="#b3b3b3" className="mr-0.5"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41 0.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>
                {item.likes || 0}
              </span>
              <span className="flex items-center gap-1">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="#b3b3b3" className="mr-0.5"><path d="M12 5c-7 0-10 7-10 7s3 7 10 7 10-7 10-7-3-7-10-7zm0 12c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8a3 3 0 100 6 3 3 0 000-6z"/></svg>
                {item.views || 0}
              </span>
            </div>
          </div>
        </div>
      ))}
      {/* Add work card */}
      <div
        className="w-full max-w-[315px] bg-white rounded-lg overflow-hidden relative shadow-none flex flex-col border-2 border-dashed border-[#222] min-h-[220px] items-center justify-center cursor-pointer"
        onClick={() => navigate('/dashboard/create-project')}
        tabIndex={0}
        role="button"
        aria-label="Add work"
        onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') navigate('/add-work'); }}
      >
        <div style={{ fontSize: 32, marginBottom: 8, color: '#222' }}>+</div>
        <div style={{ fontSize: 18, color: '#222' }}>Add work</div>
      </div>

      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl">
            <h3 className="text-lg font-bold">Confirmer la suppression</h3>
            <p className="my-4">Êtes-vous sûr de vouloir supprimer ce projet ? Cette action est irréversible.</p>
            <div className="flex justify-end space-x-4">
              <Button variant="outline" onClick={() => setShowDeleteModal(false)}>Annuler</Button>
              <Button variant="danger" onClick={handleDelete}>Supprimer</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MyWork; 