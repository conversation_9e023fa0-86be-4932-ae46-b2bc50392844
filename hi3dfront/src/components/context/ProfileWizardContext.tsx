import React, { createContext, useContext, useState, ReactNode } from 'react';

// Interface pour le contexte
interface ProfileWizardContextType {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  formData: any;
  updateFormData: (data: any) => void;
  resetForm: () => void;
  isWizardOpen: boolean;
  openWizard: () => void;
  closeWizard: () => void;
}

// Valeurs par défaut du contexte
const defaultContextValue: ProfileWizardContextType = {
  currentStep: 1,
  setCurrentStep: () => {},
  formData: {},
  updateFormData: () => {},
  resetForm: () => {},
  isWizardOpen: false,
  openWizard: () => {},
  closeWizard: () => {},
};

// Création du contexte
const ProfileWizardContext = createContext<ProfileWizardContextType>(defaultContextValue);

// Hook personnalisé pour utiliser le contexte
export const useProfileWizard = () => useContext(ProfileWizardContext);

// Props du provider
interface ProfileWizardProviderProps {
  children: ReactNode;
}

// Provider du contexte
export const ProfileWizardProvider: React.FC<ProfileWizardProviderProps> = ({ children }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({});
  const [isWizardOpen, setIsWizardOpen] = useState(false);

  const updateFormData = (data: any) => {
    setFormData((prevData) => ({ ...prevData, ...data }));
  };

  const resetForm = () => {
    setFormData({});
    setCurrentStep(1);
  };

  const openWizard = () => {
    setIsWizardOpen(true);
  };

  const closeWizard = () => {
    setIsWizardOpen(false);
  };

  return (
    <ProfileWizardContext.Provider
      value={{
        currentStep,
        setCurrentStep,
        formData,
        updateFormData,
        resetForm,
        isWizardOpen,
        openWizard,
        closeWizard,
      }}
    >
      {children}
    </ProfileWizardContext.Provider>
  );
};

export default ProfileWizardContext;
