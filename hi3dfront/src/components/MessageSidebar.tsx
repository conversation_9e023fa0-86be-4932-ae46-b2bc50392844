import React, { useState } from 'react';
import Avatar from './ui/Avatar';
import Modal from './ui/Modal';
import Button from './ui/Button';
import { Search, X } from "lucide-react";
import MessageContent from './MessageContent';
import MessageProfile from './MessageProfile';

type MessageSidebarProps = {
  offerDetail?: any;
  setSelectedApplication?: (application: any) => void;
  onSelectProfessional?: (id: number) => void;
  isClient?: boolean;
  offer?: any;
  offerComplet?: any;
  selectedFiles?: File[];
  setSelectedFiles?: React.Dispatch<React.SetStateAction<File[]>>;
};

type Professional = {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  avatar: string;
  title: string;
  specialty?: string;
};

type Application = {
  id: number;
  created_at: string;
  status: string;
  proposal: string;
  freelance_profile: {
    user_id: number;
    first_name: string;
    last_name: string;
    avatar: string;
  };
};

const MessageSidebar: React.FC<MessageSidebarProps> = ({ 
  offerDetail, 
  setSelectedApplication, 
  onSelectProfessional,
  isClient = false,
  offer = {},
  offerComplet = {},
  selectedFiles = [],
  setSelectedFiles = () => {}
}) => {
  type ApplicationStatus = "all" | "invited" | "accepted";

  const [activeFilter, setActiveFilter] = useState<ApplicationStatus>("all");
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [selectedProfessionalId, setSelectedProfessionalId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [inviteError, setInviteError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [selectedApplicationState, setSelectedApplicationState] = useState<any>(null);
  const [selectedProfessionalData, setSelectedProfessionalData] = useState<Professional | null>(null);

  // Données statiques des professionnels
  const staticProfessionals: Professional[] = [
    {
      id: 1,
      user_id: 1,
      first_name: 'Jack and Moris',
      last_name: 'Render',
      avatar: 'https://randomuser.me/api/portraits/men/41.jpg',
      title: 'Architecture visualisation',
      specialty: '3D Artist'
    },
    {
      id: 2,
      user_id: 2,
      first_name: 'The 3d',
      last_name: 'Boss',
      avatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      title: 'Architecture visualisation',
      specialty: '3D Designer'
    },
    {
      id: 3,
      user_id: 3,
      first_name: 'Sofie',
      last_name: 'Render',
      avatar: 'https://randomuser.me/api/portraits/women/45.jpg',
      title: 'Architecture visualisation',
      specialty: '3D Animator'
    },
    {
      id: 4,
      user_id: 4,
      first_name: 'Maris 3d',
      last_name: 'magik',
      avatar: 'https://randomuser.me/api/portraits/women/32.jpg',
      title: 'Architecture visualisation',
      specialty: '3D Modeler'
    },
    {
      id: 5,
      user_id: 5,
      first_name: 'Alex',
      last_name: 'Design',
      avatar: 'https://randomuser.me/api/portraits/men/23.jpg',
      title: 'Game Development',
      specialty: 'UI/UX Designer'
    },
    {
      id: 6,
      user_id: 6,
      first_name: 'Lena',
      last_name: 'Creative',
      avatar: 'https://randomuser.me/api/portraits/women/33.jpg',
      title: 'Architecture visualisation',
      specialty: 'Texture Artist'
    },
    {
      id: 7,
      user_id: 7,
      first_name: 'Max',
      last_name: 'RenderPro',
      avatar: 'https://randomuser.me/api/portraits/men/44.jpg',
      title: 'Film Production',
      specialty: 'VFX Artist'
    },
    {
      id: 8,
      user_id: 8,
      first_name: 'Eva',
      last_name: 'Digital',
      avatar: 'https://randomuser.me/api/portraits/women/55.jpg',
      title: 'Product Design',
      specialty: '3D Prototyper'
    },
    {
      id: 9,
      user_id: 9,
      first_name: 'Tom',
      last_name: 'Visual',
      avatar: 'https://randomuser.me/api/portraits/men/66.jpg',
      title: 'Architecture visualisation',
      specialty: 'Lighting Artist'
    },
    {
      id: 10,
      user_id: 10,
      first_name: 'Sophia',
      last_name: 'Art',
      avatar: 'https://randomuser.me/api/portraits/women/77.jpg',
      title: 'Game Development',
      specialty: 'Character Designer'
    }
  ];

  // Données statiques pour les applications (si offerDetail n'est pas fourni)
  const staticApplications: Application[] = [
    {
      id: 1,
      created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      status: "invited",
      proposal: "Je suis intéressé par votre projet de création d'environnement 3D pour jeu mobile.",
      freelance_profile: {
        user_id: 1,
        first_name: "Jean",
        last_name: "Dupont",
        avatar: "https://randomuser.me/api/portraits/men/41.jpg"
      }
    },
    {
      id: 2,
      created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      status: "accepted",
      proposal: "J'ai une grande expérience dans la modélisation 3D pour jeux mobiles.",
      freelance_profile: {
        user_id: 2,
        first_name: "Marie",
        last_name: "Martin",
        avatar: "https://randomuser.me/api/portraits/women/22.jpg"
      }
    },
    {
      id: 3,
      created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
      status: "invited",
      proposal: "Spécialiste en modélisation architecturale avec 5 ans d'expérience.",
      freelance_profile: {
        user_id: 5,
        first_name: "Alex",
        last_name: "Design",
        avatar: "https://randomuser.me/api/portraits/men/23.jpg"
      }
    },
    {
      id: 4,
      created_at: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
      status: "accepted",
      proposal: "Expert en création de textures réalistes pour environnements 3D.",
      freelance_profile: {
        user_id: 6,
        first_name: "Lena",
        last_name: "Creative",
        avatar: "https://randomuser.me/api/portraits/women/33.jpg"
      }
    }
  ];

  // Utiliser soit les données de l'offre, soit les données statiques
  const applications = offerDetail?.applications || staticApplications;
  const professionals = staticProfessionals;

  // Ouvrir la modal d'invitation
  const handleOpenInviteModal = () => {
    setInviteError(null);
    setSelectedProfessionalId(null);
    setSearchQuery('');
    setShowInviteModal(true);
  };

  // Fermer la modal d'invitation
  const handleCloseInviteModal = () => {
    setShowInviteModal(false);
  };

  // Ouvrir la modal de message
  const handleOpenMessageModal = (application: any, professionalId: number) => {
    // Trouver les données du professionnel sélectionné
    const professionalData = staticProfessionals.find(pro => pro.user_id === professionalId);
    setSelectedProfessionalData(professionalData || null);
    
    setSelectedApplicationState(application);
    setSelectedProfessionalId(professionalId);
    setShowMessageModal(true);
    
    if (setSelectedApplication) {
      setSelectedApplication(application);
    }
    
    if (onSelectProfessional) {
      onSelectProfessional(professionalId);
    }
  };

  // Fermer la modal de message
  const handleCloseMessageModal = () => {
    setShowMessageModal(false);
    setSelectedApplicationState(null);
    setSelectedProfessionalId(null);
    setSelectedProfessionalData(null);
  };

  // Filtrer les professionnels en fonction de la recherche
  const filteredProfessionals = professionals.filter(pro => {
    const fullName = `${pro.first_name || ''} ${pro.last_name || ''}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  // Simuler l'envoi d'une invitation à un professionnel
  const handleInviteProfessional = () => {
    if (!selectedProfessionalId) {
      setInviteError('Veuillez sélectionner un professionnel');
      return;
    }

    // Simulation d'invitation réussie
    setShowInviteModal(false);
    setSuccessMessage('Invitation envoyée avec succès au professionnel');

    // Afficher le message de succès pendant 3 secondes
    setTimeout(() => {
      setSuccessMessage(null);
    }, 3000);
  };

  const statusLabels: Record<ApplicationStatus, string> = {
    all: "All",
    invited: "Invited",
    accepted: "Approved",
  };

  const getUrlProlfil = (path: string) => {
    return path || 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
  };

  // Formater la date en style "il y a x minutes"
  function formatRelativeDate(dateString: string) {
    const date = new Date(dateString);
    const diffMs = Date.now() - date.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);
    if (diffMinutes < 1) return "À l'instant";
    if (diffMinutes < 60) return `Il y a ${diffMinutes} minute${diffMinutes > 1 ? "s" : ""}`;
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `Il y a ${diffHours} heure${diffHours > 1 ? "s" : ""}`;
    const diffDays = Math.floor(diffHours / 24);
    return `Il y a ${diffDays} jour${diffDays > 1 ? "s" : ""}`;
  }

  // Raccourcir une proposition
  function truncateText(text: string, maxLength: number) {
    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
  }

  const getFilteredApplications = () => {
    if (!applications) return [];
    if (activeFilter === "all") return applications;
    return applications.filter((app: any) => app.status === activeFilter);
  };

  const countByStatus = (status: ApplicationStatus) => {
    if (!applications) return 0;
    if (status === "all") return staticProfessionals.length;
    return applications.filter((app: any) => app.status === status).length;
  };

  const filteredApplications = getFilteredApplications();

  return (
    <>
      <div className="p-0 m-0 ml-0 pl-0 h-full flex flex-col" style={{ maxHeight: '500px' }}>
        {/* Message de succès */}
        {successMessage && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
            {successMessage}
          </div>
        )}

        {/* Onglets filtres */}
        <div className="flex gap-4 mb-6 border-b pb-3 border-black-200 ml-0 pl-0">
          {Object.entries(statusLabels).map(([statusKey, label]) => {
            const key = statusKey as ApplicationStatus;
            return (
              <button
                key={key}
                className={`py-2 px-4 text-sm font-semibold ml-0 pl-0 ${activeFilter === key ? "text-black font-bold border-b-2 border-black" : "text-gray-400"
                  }`}
                onClick={() => setActiveFilter(key)}
                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
                }}
              >
                {label} ({countByStatus(key)})
              </button>
            );
          })}
        </div>

        {/* Conteneur avec défilement - Ajusté pour prendre toute la hauteur disponible */}
        <div className="flex-1 overflow-y-auto pr-2" style={{ maxHeight: 'calc(500px - 120px)' }}>
          {activeFilter === "all" ? (
            // Affichage des cartes pour le filtre "All"
            <div className="mb-6 ml-0 pl-0">
              <div className="space-y-3 ml-0 pl-0">
                {staticProfessionals.map((professional) => (
                  <div
                    key={professional.user_id}
                    className="bg-[#F5F5F5] p-4 cursor-pointer hover:bg-gray-200 transition-colors duration-200"
                    onClick={() => handleOpenMessageModal(null, professional.user_id)}
                    style={{
                      borderRadius: "18px",
                    }}
                  >
                    <div className="flex items-center">
                      <img
                        src={getUrlProlfil(professional.avatar)}
                        alt={`${professional.first_name} ${professional.last_name}`}
                        className="w-12 h-12 rounded-full object-cover mr-4"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <span className="text-[#1a1a1a]"
                            style={{
                              fontSize: "13px",
                              fontFamily: "'Inter', sans-serif",
                              fontWeight: 700,
                            }}
                          >
                            {professional.first_name} {professional.last_name}
                          </span>
                        </div>
                        <div className="text-gray-600 mt-1"
                          style={{
                            fontSize: "11px",
                            fontFamily: "'Inter', sans-serif",
                            fontWeight: 600,
                          }}
                        >
                          {professional.title}
                        </div>
                        {professional.specialty && (
                          <div className="text-xs text-gray-500 mt-1"
                            style={{
                              fontSize: "11px",
                              fontFamily: "'Inter', sans-serif",
                              fontWeight: 600,
                            }}
                          >
                            {professional.specialty}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : filteredApplications.length > 0 ? (
            // Affichage normal pour les autres filtres
            <div className="mb-6 ml-0 pl-0">
              <div className="space-y-1 ml-0 pl-0">
                {filteredApplications.map((application: any) => {
                  const freelance = application.freelance_profile;
                  return (
                    <div
                      key={application.id}
                      className={`flex items-center rounded-lg px-2 py-2 cursor-pointer ml-0 pl-0 ${selectedProfessionalId === freelance.user_id ? "bg-gray-100" : "hover:bg-gray-100"
                        }`}
                      onClick={() => handleOpenMessageModal(application, freelance.user_id)}
                    >
                      <input
                        type="checkbox"
                        className="mr-3 w-4 h-4"
                        checked={selectedProfessionalId === freelance.user_id}
                        readOnly
                      />
                      <img
                        src={getUrlProlfil(freelance.avatar)}
                        alt={`${freelance.first_name} ${freelance.last_name}`}
                        className="w-10 h-10 rounded-full object-cover mr-3"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <span className="font-bold text-sm text-[#1a1a1a]">
                            {freelance.first_name} {freelance.last_name}
                          </span>
                          <span className="text-xs text-gray-400 whitespace-nowrap ml-2">
                            {formatRelativeDate(application.created_at)}
                          </span>
                        </div>
                        <div className="flex items-center text-[11px] text-gray-400 truncate">
                          <svg
                            className="w-4 h-4 mr-1"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M3 10v4a1 1 0 001 1h3m10-5l-5-5m0 0l-5 5m5-5v12"
                            />
                          </svg>
                          {application.proposal
                            ? `${freelance.first_name} a postulé : ${truncateText(application.proposal, 30)}`
                            : `Candidature reçue de ${freelance.first_name}`}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ) : (
            <div className="text-center text-gray-400 text-sm py-12 ml-0 pl-0">
              <div className="w-24 h-24 mx-auto mb-4 flex items-center justify-center bg-gray-100 rounded-full text-4xl opacity-50">
                📭
              </div>
              <p>Aucune candidature trouvée pour le filtre <strong>{statusLabels[activeFilter]}</strong></p>
              <p className="text-xs mt-2">Essayez un autre filtre ou invitez de nouveaux artistes !</p>
            </div>
          )}
        </div>

        <button
          className="w-full py-3 px-4 rounded-full bg-white border border-gray-300 font-semibold cursor-pointer ml-0 pl-0 mt-4"
          onClick={handleOpenInviteModal}
        >
          Invite a new artiste
        </button>
      </div>

      {/* 📦 Modal d'invitation */}
      <Modal
        isOpen={showInviteModal}
        onClose={handleCloseInviteModal}
        title="Invite a professional"
      >
        <div className="p-4">
          {inviteError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {inviteError}
            </div>
          )}

          {/* 🔍 Search */}
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search a professional..."
                className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* 👥 List */}
          <div className="max-h-60 overflow-y-auto mb-4">
            {filteredProfessionals.length === 0 ? (
              <div className="text-center py-4 text-neutral-500">
                No professional found.
              </div>
            ) : (
              <div className="space-y-2">
                {filteredProfessionals.map((professional) => (
                  <div
                    key={professional.user_id}
                    className={`flex items-center p-3 rounded-lg cursor-pointer ${selectedProfessionalId === professional.user_id
                      ? "bg-primary-50 border border-primary-200"
                      : "hover:bg-neutral-50 border border-transparent"
                      }`}
                    onClick={() => setSelectedProfessionalId(professional.user_id)}
                  >
                    <Avatar
                      src={getUrlProlfil(professional.avatar)}
                      fallback={
                        (professional.first_name?.[0] || "") + (professional.last_name?.[0] || "")
                      }
                      size="md"
                      className="mr-3"
                    />
                    <div>
                      <h4 className="font-medium text-neutral-900">
                        {professional.first_name} {professional.last_name}
                      </h4>
                      {professional.title && (
                        <p className="text-sm text-neutral-500">{professional.title}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* ✅ Actions */}
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCloseInviteModal}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleInviteProfessional}
              disabled={!selectedProfessionalId}
            >
              Invite
            </Button>
          </div>
        </div>
      </Modal>

      {/* 💬 Modal de message (plein écran) */}
      {showMessageModal && (
        <div className="fixed inset-0 z-50 flex flex-col bg-white animate-slide-in">
          {/* En-tête avec bouton de fermeture */}
          <div className="flex items-center justify-between p-4 border-b">
            <button 
              onClick={handleCloseMessageModal}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <X size={24} />
            </button>
            <h2 className="text-lg font-semibold">Conversation</h2>
            <div className="w-10"></div> {/* Pour l'équilibrage */}
          </div>
          
          {/* Contenu principal */}
          <div className="flex-1 flex flex-col md:flex-row overflow-hidden">
            {/* MessageProfile en haut sur mobile, à gauche sur desktop */}
            <div className="w-full md:w-1/3 lg:w-1/4 border-b md:border-b-0 md:border-r">
              <MessageProfile 
                application={selectedApplicationState} 
                offerDetail={offerComplet}
              />
            </div>
            
            {/* MessageContent */}
            <div className="flex-1 overflow-auto">
              <MessageContent 
                offerId={offer?.id}
                offerTitle={offer?.title}
                clientId={offer?.client?.id}
                clientName={offer?.client?.name || "Client"}
                clientAvatar={offer?.client?.avatar}
                professionalId={selectedProfessionalId ?? undefined}
                professionalName={selectedProfessionalData ? 
                  `${selectedProfessionalData.first_name} ${selectedProfessionalData.last_name}` : 
                  "Professionnel"}
                professionalAvatar={selectedProfessionalData?.avatar}
                isClient={isClient}
                onBack={handleCloseMessageModal}
                selectedFiles={selectedFiles}
                setSelectedFiles={setSelectedFiles}
              />
            </div>
          </div>
        </div>
      )}

      {/* Animation CSS */}
      <style>
        {`
          @keyframes slideIn {
            from {
              transform: translateX(-100%);
            }
            to {
              transform: translateX(0);
            }
          }
          
          .animate-slide-in {
            animation: slideIn 0.3s ease-out forwards;
          }
        `}
      </style>
    </>
  );
};

export default MessageSidebar;