import React, { useState, useEffect,useRef, useCallback } from "react";
import { useLocation } from "react-router-dom";
import Header from "./Header";
import Footer from "./Footer";
import { exploreService, Professional, Project } from './../services/exploreService';
import { API_BASE_URL } from './../config';
import PortfolioPage from './PortfolioPage';

// Interfaces détaillées inutilisées supprimées

// interface GalleryPhoto supprimée (non utilisée)

// interface Achievement supprimée (non utilisée)

// interfaces inutilisées supprimées

const SearchGlobal = () => {
  const { state } = useLocation();
  const initialSearch = state?.search || '';
  const initialType = (state?.type as string) || 'Services';

  const initialFilterLocation = state?.location || '';
  const initialFilterLanguage = state?.language || '';
  const initialFilterMinPrice = state?.minPrice || 0;
  const initialFilterMaxPrice = state?.maxPrice || 0;

  console.log("Global search : ", initialSearch)

  const [loading, setLoading] = useState(true);
    const [searchLoading, setSearchLoading] = useState(false);
    const [professionals, setProfessionals] = useState<Professional[]>([]);
    const [filteredPros, setFilteredPros] = useState<Professional[]>([]);
    const [searchType,setSearchType] = useState<string>(initialType);
    const [searchQuery,setSearchQuery] = useState<string>(initialSearch);
    const [filteredService, setFiltereService] = useState<Project[]>([]);
    const [allService, setAllService] = useState<Project[]>([]);
    // résultats groupés non utilisés
  
    // Annulation et numérotation des requêtes
  const searchAbortRef = useRef<AbortController | null>(null);
  const lastRequestRef = useRef(0);

  const [executionTimes,setExecutionTimes] = useState<number>(0.0);
  
  
    const API_URL = `${API_BASE_URL}/api/search`;
  
    // const getUrlProlfil = (path : string)  => {
    //       return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
    // };

    const getUrlProlfil = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
          if (!imagePath) return defaultImage;
          if (imagePath.startsWith('http')) return imagePath;
          if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
          return `${API_BASE_URL}/${imagePath}`;
        };
    const getImageService = (imagePath: string | undefined, defaultImage: string = "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") => {
          if (!imagePath) return defaultImage;
            console.log("Liens image : ",imagePath)  
            if (imagePath.startsWith('http')) return imagePath;  
            if (imagePath.startsWith('/')) return `${API_BASE_URL}/storage${imagePath}`;
            return `${API_BASE_URL}/storage/${imagePath}`;
          };
  
    const fetchProfessionals = async () => {
        const response = await exploreService.getProfessionals();
        return response.professionals.map(pro => ({
          id: pro.id,
          id_user: pro.user_id,
          first_name: pro.first_name,
          last_name: pro.last_name,
          title: pro.title || 'Artiste 3D',
          skills: pro.skills || ['Modélisation', 'Blender', 'Maya'],
          languages:pro.languages ||[],
          rating: pro.rating || 4.5,
          review_count: pro.review_count || 10,
          profile_picture_path: pro.profile_picture_path || 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          city: pro.city || 'Paris',
          country: pro.country || 'France',
          availability_status: (pro.availability_status || 'available') as 'available' | 'busy' | 'unavailable',
          likes_count: pro.likes_count || 0,
          views_count: pro.views_count || 0,
          service_offer: (pro.service_offer || []).map((proj: any) => ({
            avatar: pro.profile_picture_path? getUrlProlfil(String(pro.profile_picture_path)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            id: proj.id,
            title: proj.title,
            concepts : proj.concepts,
            revisions : proj.revisions,
            description: proj.description || 'Projet réalisé avec passion et expertise technique.',
            image_url : Array.isArray(proj.files) && proj.files.length > 0
            ? getImageService(proj.files[0].path)//`${API_BASE_URL}/storage/${proj.files[0].path}`
            : proj.image?getImageService(proj.image):'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',     
            file_urls: Array.isArray(proj.files)
            ? proj.files.map((file: any) => getImageService(file.path))//`${API_BASE_URL}/storage/${file.path}`)
            : [],
            category: proj.categories ? proj.categories.join(" - ") : "",
            client_name: proj.execution_time,
            date_create: proj.created_at,
            price: proj.price,
            user_id: pro.user_id,
            professional_name: `${pro?.first_name || ''} ${pro?.last_name || ''}`.trim(),
            professional_id: pro.id || 1,
          })),
          achievements: (pro.achievements || []).map((ach: any) => ({
            avatar: pro.profile_picture_path? getUrlProlfil(String(pro.profile_picture_path)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            id: ach.id,
            title: ach.title,
            description: ach.description || 'Réalisé avec expertise.',
            image_url : ach.cover_photo
            ? getImageService(ach.cover_photo)//`${API_BASE_URL}/storage/${ach.cover_photo}`
            : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
              file_urls: Array.isArray(ach.gallery_photos)
            ? ach.gallery_photos.map((file: any) => getImageService(file.path))//`${API_BASE_URL}/storage/${file.path}`)
            : [],
            category: ach.category,
            client_name: '',
            date_create: ach.created_at,
            price: '',
            user_id: pro.user_id,
            professional_name: `${pro?.first_name || ''} ${pro?.last_name || ''}`.trim(),
            professional_id: pro.id || 1,
          })),
        }));
      };
  
    const fetchServices = async () => {
        let allServices: Project[] = [];
        let currentPage = 1;
        let lastPage = 1;
        try {
          do {
          const response = await fetch(`${API_BASE_URL}/api/explorer/services?page=${currentPage}`);
          const data = await response.json();
          console.log("Donnée service : ",data)
          if (data.success) {
          const formatted =  data.services.map((proj: any) => ({
            id: proj.id,
            title: proj.title,
            description: proj.description || 'Projet réalisé avec passion et expertise technique.',
            concepts : proj.concepts,
            revisions : proj.revisions,
            // image_url: proj.files?.[0]?.path
            //   ? `${API_BASE_URL}/storage/${proj.files[0].path}`
            //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            image_url : Array.isArray(proj.files) && proj.files.length > 0
              ? getImageService(proj.files[0].path)//`${API_BASE_URL}/storage/${proj.files[0].path}`
              : proj.image?getImageService(proj.image):'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',     
            file_urls: Array.isArray(proj.files)
              ? proj.files.map((file: any) => getImageService(file.path))//`${API_BASE_URL}/storage/${file.path}`)
              : [],
            category: proj.categories ? proj.categories.join(" - ") : "",
            client_name: proj.execution_time,
            professional_name: proj.professional.first_name + ' ' + proj.professional.last_name,
            professional_id: proj.professional.id || 1,
            date_create: proj.created_at,
            price: proj.price,
            user_id: proj.professional.user_id,
            views : proj.views,
            likes : proj.likes,
            liked : false,
            avatar: proj.professional.avatar? getUrlProlfil(String(proj.professional.avatar)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          }));
    
          allServices = [...allServices, ...formatted];
    
          // Pagination
          currentPage = data.pagination.current_page + 1;
          lastPage = data.pagination.last_page;
    
          } else {
            break;
          }
        } while (currentPage <= lastPage);
        console.log("✅ Tous les services récupérés :", allServices);
          return allServices;
        } catch (error) {
          console.error("Erreur lors du fetch des services :", error);
          return [];
        }
        };
    // fonction fetchServicesOrigine supprimée (inutile)
  
    const fetchData = async () => {
      setLoading(true);
  
      try {
        const professionals = await fetchProfessionals();
        const services_offers = await fetchServices();
  
        if (professionals.length > 0) {
          setProfessionals(professionals);
          setFilteredPros(professionals);
        }
  
        setFiltereService(services_offers);
        setAllService(services_offers);
      } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        // Ici, tu peux insérer une logique de fallback si tu veux afficher des données fictives
      } finally {
        setLoading(false);
      }
    };
  
    useEffect(() => {
      fetchData();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },[]);

    // Handler de recherche, mémorisé pour éviter des ré-exécutions en boucle
    const handleSearch = useCallback(async (query: string, type: string) => {
    const q = (query ?? "").trim();
  
    if (!q || q.length < 2) {
      // Si on retombe en "peu de caractères" : réinitialise + annule la requête en vol
      setSearchType(type);
      setSearchQuery("");
      setFilteredPros(professionals);
      setFiltereService(allService);
  
      if (searchAbortRef.current) searchAbortRef.current.abort();
      setSearchLoading(false);
      return;
    }
  
    setSearchType(type);
    setSearchQuery(q);
  
    const buildSearchUrl = (qq: string, t: string) => {
      const typesParams =
        t === "Services"
          ? "&types[]=service_offers"
          : "&types[]=professional_profiles&types[]=achievements";
      return `${API_URL}?q=${encodeURIComponent(qq)}${typesParams}`;
    };
  
    const resetResults = () => {
      setFilteredPros([]);
      setFiltereService([]);
    };
  
    // ---- identifiant unique pour CETTE requête ----
    const requestId = ++lastRequestRef.current;
  
    try {
      // Annule la requête précédente
      if (searchAbortRef.current) searchAbortRef.current.abort();
      const controller = new AbortController();
      searchAbortRef.current = controller;
  
      setSearchLoading(true);
  
      const response = await fetch(buildSearchUrl(q, type), { signal: controller.signal });
      const data = await response.json();
  
      // Si ce n'est plus la requête la plus récente, on ignore (last-write-wins)
      if (requestId !== lastRequestRef.current) return;
  
      if (data?.success) {
        const results = data.data?.results_by_type ?? {};

        setExecutionTimes(data.data?.performance?.total_execution_time_ms?? data.data?.performance?.total_meilisearch_time_ms ?? data.data?.performance?.cache_retrieval_time_ms ??0.0);
  
        if (type === "Services" && Array.isArray(results.service_offers)) {
          const filteredServices = allService.filter((service) =>
            results.service_offers.some((r: any) => r.id === service.id)
          );
          setFiltereService(filteredServices);
        } else {
          const achievementsResult = results.achievements || [];
          const profileResult = results.professional_profiles || [];
  
          if (achievementsResult.length === 0) {
            const prosFromProfileResults = professionals.filter((pro) =>
              profileResult.some((res: any) => res.id === pro.id)
            );
            setFilteredPros(prosFromProfileResults);
          } else {
            const prosWithMatchingAchievements = professionals
              .map((pro) => {
                if (!pro || !pro.achievements) return null;
                const matching = pro.achievements.filter((ach: any) =>
                  achievementsResult.some((ra: any) => ra.id === ach.id)
                );
                return matching.length > 0 ? { ...pro, achievements: matching } : null;
              })
              .filter((p): p is Professional => p !== null);
            setFilteredPros(prosWithMatchingAchievements);
          }
        }
      } else {
        resetResults();
      }
    } catch (err: any) {
      // Abort “normal” => on ne considère pas comme une erreur
      if (err?.name === "AbortError") return;
  
      // Réponse tardive d’une ancienne requête => on ignore
      if (requestId !== lastRequestRef.current) return;
  
      console.error("Erreur API:", err);
      resetResults();
    } finally {
      // N’éteins le loader que si c’est TOUJOURS la requête active
      if (requestId === lastRequestRef.current) {
        setSearchLoading(false);
      }
    }
  }, [API_URL, allService, professionals])

  
  

  // Initialiser depuis l'état de navigation (terme + type) et appliquer les filtres (pays, langue, prix) une fois les données chargées
    useEffect(() => {
      if (loading) return;

      const normalize = (value: unknown): string =>
        (value ?? "").toString().trim().toLowerCase();

      const hasCountry = normalize(initialFilterLocation) !== "";
      const hasLanguage = normalize(initialFilterLanguage) !== "";
      const minVal = Number(initialFilterMinPrice) || 0;
      const maxVal = Number(initialFilterMaxPrice) || 0;
      const hasPriceFilter = minVal > 0 || maxVal > 0;

      // Helpers pour matcher pays/langue sur le profil
      const matchesCountry = (pro: Professional): boolean => {
        if (!hasCountry) return true;
        return normalize(pro.country).includes(normalize(initialFilterLocation));
      };

      const extractLanguages = (pro: any): string[] => {
        const possible = [
          pro?.languages,
          pro?.language,
          pro?.languages_spoken,
          pro?.spoken_languages,
          pro?.skills, // fallback si le backend n'a pas encore le champ languages
        ];
        const found = possible.find((p) => Array.isArray(p) || typeof p === "string");
        if (Array.isArray(found)) return found.map((s) => normalize(s));
        if (typeof found === "string") {
          // split communs: virgule, point-virgule, barre verticale
          return found
            .split(/[,;|]/)
            .map((s) => normalize(s))
            .filter(Boolean);
        }
        return [];
      };

      const matchesLanguage = (pro: Professional): boolean => {
        if (!hasLanguage) return true;
        const langs = extractLanguages(pro);
        return langs.includes(normalize(initialFilterLanguage));
      };

      // Appliquer filtres professionnels si pays/langue présents
      if (hasCountry || hasLanguage) {
        const pros = professionals.filter((p) => matchesCountry(p) && matchesLanguage(p));
        setFilteredPros(pros);
        setSearchType("Artist 3D");
      } else {
        // Si pas de filtre pays/langue, garder la logique existante sur le type initial
        setSearchType(initialType);
      }

      // Appliquer filtres prix sur services si demandés
      if (hasPriceFilter) {
        const services = allService.filter((s) => {
          const priceNum = Number((s.price as unknown as string) ?? 0) || 0;
          if (minVal > 0 && priceNum < minVal) return false;
          if (maxVal > 0 && priceNum > maxVal) return false;
          return true;
        });
        setFiltereService(services);
        setSearchType("Services");
      }

      // Si aucun filtre spécifique, et qu'un terme de recherche est fourni, exécuter la recherche initiale
      const hasAnyFilter = hasCountry || hasLanguage || hasPriceFilter;
      if (!hasAnyFilter) {
        if (initialSearch && initialSearch.trim() !== "") {
          handleSearch(initialSearch, initialType);
        } else {
          setSearchType(initialType);
        }
      }
    }, [loading, initialSearch, initialType, initialFilterLocation, initialFilterLanguage, initialFilterMinPrice, initialFilterMaxPrice, professionals, allService, handleSearch]);
  
  
    if (loading) {
        return (
          <div className="min-h-screen bg-white">
            <Header />
            <div className="flex justify-center items-center h-96">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
            </div>
            <Footer />
          </div>
        );
      }

  return (
    <>
      <Header onSearchGlobal={handleSearch}/>
      
          {filteredPros.length === professionals.length && !searchType && (
            <PortfolioPage pros={filteredPros} viewType="default" services={filteredService} searchLoading={searchLoading} executionTimes = {executionTimes}/>
            )}
            {searchType !== "Services" ? (
              <PortfolioPage
                pros={filteredPros}
                viewType="designers"
                query={searchQuery}
                services={filteredService}
                searchLoading={searchLoading}
                executionTimes = {executionTimes}
              />
            ) : (
              <PortfolioPage
                pros={filteredPros}
                viewType="services"
                query={searchQuery}
                services={filteredService}
                searchLoading={searchLoading}
                executionTimes = {executionTimes}
              />
            )}
        
      <Footer />
    </>
  );
};

export default SearchGlobal;
