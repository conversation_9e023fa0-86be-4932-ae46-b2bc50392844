import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Edit, Mail, Phone, MapPin, Calendar, Briefcase, Award, FileText, User, Settings } from 'lucide-react';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import Avatar from '../ui/Avatar';
import { API_BASE_URL } from '../../config';
import { profileService } from '../../services/profileService';
import { getAvatarUrl, getInitials } from '../../utils/avatarUtils';

// Utiliser l'interface ProfileData du service profileService
import { ProfileData } from '../../services/profileService';

// Alias ClientProfile pour maintenir la compatibilité avec le reste du code
type ClientProfile = ProfileData;

const ClientProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const [profile, setProfile] = useState<ClientProfile | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      try {
        // Utiliser le service API réel pour récupérer les données du profil
        const data = await profileService.getProfile();
        setProfile(data.profile);
        setError(null);
        console.log('Profil récupéré avec succès:', data.profile);
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError('Impossible de charger le profil. Veuillez réessayer plus tard.');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, []);

  const handleEditProfile = () => {
    navigate('/dashboard/client-profile/edit');
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Non spécifié';

    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };



  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !profile) {
    return (
      <DashboardLayout>
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <div className="text-red-500 mb-4">⚠️</div>
          <h2 className="text-lg font-semibold text-red-700 mb-2">Erreur</h2>
          <p className="text-red-600">{error || 'Profile not found'}</p>
          <Button
            variant="primary"
            className="mt-4"
            onClick={() => navigate('/dashboard')}
          >
            Retour au tableau de bord
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Mon Profil"
      subtitle="Consultez et gérez vos informations personnelles"
      actions={
        <Button
          variant="primary"
          leftIcon={<Edit className="h-5 w-5" />}
          onClick={handleEditProfile}
          style={{ backgroundColor: '#2980b9', color: 'black' }}
        >
          Modifier mon profil
        </Button>
      }
    >
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main profile information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Profile header */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="p-6 sm:p-8">
              <div className="flex flex-col sm:flex-row sm:items-center">
                <Avatar
                  src={getAvatarUrl(profile.avatar)}
                  fallback={getInitials(profile.first_name, profile.last_name)}
                  size="xl"
                  className="mb-4 sm:mb-0 sm:mr-6"
                />
                <div>
                  <h1 className="text-2xl font-bold text-neutral-900">
                    {profile.first_name} {profile.last_name}
                  </h1>
                  <p className="text-neutral-500 mt-1">
                    {profile.position ? `${profile.position}` : 'Client'}
                    {profile.company_name ? ` chez ${profile.company_name}` : ''}
                  </p>
                  <div className="flex flex-wrap gap-4 mt-3">
                    {profile.email && (
                      <div className="flex items-center text-neutral-600">
                        <Mail className="h-4 w-4 mr-1" />
                        <span>{profile.email}</span>
                      </div>
                    )}
                    {profile.phone && (
                      <div className="flex items-center text-neutral-600">
                        <Phone className="h-4 w-4 mr-1" />
                        <span>{profile.phone}</span>
                      </div>
                    )}
                    {profile.city && profile.country && (
                      <div className="flex items-center text-neutral-600">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span>{profile.city}, {profile.country}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* About section */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">À propos</h2>
            </div>
            <div className="p-6">
              {profile.bio ? (
                <p className="text-neutral-700 whitespace-pre-line">{profile.bio}</p>
              ) : (
                <p className="text-neutral-500 italic">Aucune biographie renseignée</p>
              )}
            </div>
          </div>

          {/* Company information */}
          {(profile.company_name || profile.industry) && (
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <h2 className="text-lg font-semibold text-neutral-900">Entreprise</h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {profile.company_name && (
                    <div>
                      <h3 className="text-sm font-medium text-neutral-500 mb-1">Nom de l'entreprise</h3>
                      <p className="text-neutral-900">{profile.company_name}</p>
                    </div>
                  )}
                  {profile.industry && (
                    <div>
                      <h3 className="text-sm font-medium text-neutral-500 mb-1">Secteur d'activité</h3>
                      <p className="text-neutral-900">{profile.industry}</p>
                    </div>
                  )}
                  {profile.company_size && (
                    <div>
                      <h3 className="text-sm font-medium text-neutral-500 mb-1">Taille de l'entreprise</h3>
                      <p className="text-neutral-900">{profile.company_size}</p>
                    </div>
                  )}
                  {profile.position && (
                    <div>
                      <h3 className="text-sm font-medium text-neutral-500 mb-1">Poste</h3>
                      <p className="text-neutral-900">{profile.position}</p>
                    </div>
                  )}
                  {profile.website && (
                    <div className="md:col-span-2">
                      <h3 className="text-sm font-medium text-neutral-500 mb-1">Site web</h3>
                      <a
                        href={profile.website.startsWith('http') ? profile.website : `https://${profile.website}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary-600 hover:underline"
                      >
                        {profile.website}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Contact information */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">Coordonnées</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Email</h3>
                  <p className="text-neutral-900">{profile.email}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Téléphone</h3>
                  <p className="text-neutral-900">{profile.phone || 'Non spécifié'}</p>
                </div>
                <div className="md:col-span-2">
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Adresse</h3>
                  <p className="text-neutral-900">
                    {profile.address ? (
                      <>
                        {profile.address}
                        <br />
                        {profile.city && profile.country ? `${profile.city}, ${profile.country}` : ''}
                      </>
                    ) : (
                      'Non spécifiée'
                    )}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Profile completion */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">Complétion du profil</h2>
            </div>
            <div className="p-6">
              <div className="mb-2 flex justify-between items-center">
                <span className="text-sm font-medium text-neutral-700">
                  {profile.completion_percentage}% complété
                </span>
                <span className="text-xs text-neutral-500">
                  {profile.completion_percentage < 100 ? 'À compléter' : 'Complété'}
                </span>
              </div>
              <div className="w-full bg-neutral-200 rounded-full h-2.5">
                <div
                  className="bg-primary-600 h-2.5 rounded-full"
                  style={{ width: `${profile.completion_percentage}%` }}
                ></div>
              </div>
              {profile.completion_percentage < 100 && (
                <Button
                  variant="outline"
                  fullWidth
                  className="mt-4"
                  onClick={handleEditProfile}
                  style={{ color: 'black' }}
                >
                  Compléter mon profil
                </Button>
              )}
            </div>
          </div>

          {/* Account information */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">Informations du compte</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Date de naissance</h3>
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-neutral-400 mr-2" />
                    <p className="text-neutral-900">{formatDate(profile.birth_date || '')}</p>
                  </div>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-neutral-500 mb-1">Membre depuis</h3>
                  <div className="flex items-center">
                    <User className="h-5 w-5 text-neutral-400 mr-2" />
                    <p className="text-neutral-900">{formatDate(profile.created_at)}</p>
                  </div>
                </div>
              </div>
              <div className="mt-6 pt-6 border-t border-neutral-200">
                <Button
                  variant="outline"
                  fullWidth
                  leftIcon={<Settings className="h-4 w-4" />}
                  onClick={() => navigate('/dashboard/settings')}
                  style={{ color: 'black' }}
                >
                  Paramètres du compte
                </Button>
              </div>
            </div>
          </div>

          {/* Social links */}
          {profile.social_links && Object.values(profile.social_links).some(link => link) && (
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-neutral-200">
                <h2 className="text-lg font-semibold text-neutral-900">Réseaux sociaux</h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  {profile.social_links.linkedin && (
                    <a
                      href={profile.social_links.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-neutral-700 hover:text-primary-600"
                    >
                      <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                      </svg>
                      LinkedIn
                    </a>
                  )}
                  {profile.social_links.twitter && (
                    <a
                      href={profile.social_links.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-neutral-700 hover:text-primary-600"
                    >
                      <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                      </svg>
                      Twitter
                    </a>
                  )}
                  {profile.social_links.facebook && (
                    <a
                      href={profile.social_links.facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-neutral-700 hover:text-primary-600"
                    >
                      <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385h-3.047v-3.47h3.047v-2.642c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953h-1.514c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385c5.738-.9 10.126-5.864 10.126-11.854z"/>
                      </svg>
                      Facebook
                    </a>
                  )}
                  {profile.social_links.instagram && (
                    <a
                      href={profile.social_links.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center text-neutral-700 hover:text-primary-600"
                    >
                      <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                      </svg>
                      Instagram
                    </a>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default ClientProfilePage;
