import React from 'react';
import Badge from '../ui/Badge';

interface SkillsSectionProps {
  skills?: string[];
  languages?: string[];
  experience?: number;
  hourlyRate?: string;
}

const SkillsSection: React.FC<SkillsSectionProps> = ({
  skills = [],
  languages = [],
  experience,
  hourlyRate,
}) => {
  // Mock data if real data is not provided
  const mockSkills = ['Animation 3D', 'Modélisation 3D', 'Blender', 'Texturing', 'Rigging', 'Rendu 3D'];
  const mockLanguages = ['Français', 'Anglais', 'Espagnol'];
  
  const displaySkills = skills.length > 0 ? skills : mockSkills;
  const displayLanguages = languages.length > 0 ? languages : mockLanguages;

  return (
    <div className="bg-white rounded-lg shadow-sm border border-neutral-200 overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-semibold mb-6">Compétences et informations</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left column */}
          <div>
            {/* Skills */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Compétences</h3>
              <div className="flex flex-wrap gap-2">
                {displaySkills.map((skill, index) => (
                  <Badge key={index} variant="primary" size="md">
                    {skill}
                  </Badge>
                ))}
              </div>
            </div>
            
            {/* Languages */}
            <div>
              <h3 className="text-lg font-medium mb-3">Langues</h3>
              <div className="flex flex-wrap gap-2">
                {displayLanguages.map((language, index) => (
                  <Badge key={index} variant="secondary" size="md">
                    {language}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
          
          {/* Right column */}
          <div>
            {/* Experience */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-2">Expérience</h3>
              <p className="text-neutral-700">
                {experience ? `${experience} ans d'expérience` : 'Plus de 5 ans d\'expérience'}
              </p>
            </div>
            
            {/* Hourly Rate */}
            <div>
              <h3 className="text-lg font-medium mb-2">Taux horaire</h3>
              <p className="text-neutral-700 font-semibold">
                {hourlyRate || '50€'} / heure
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SkillsSection;
