export type ProfileType = 'independent' | 'company' | 'individual';

export interface ProfileFormData {
  // Personal Data
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  profileType: ProfileType;
  
  // KYC & Certification
  idDocument?: File;
  identity_document_number:string,
  // selfie?: File;
  // certifications: string[];
  
  // Experience & Portfolio
  experience: number;
  portfolio : string;
  // experiences: {
  //   title: string;
  //   company: string;
  //   startDate: string;
  //   endDate: string;
  //   description: string;
  // }[];
  // portfolio: string[];
  
  // Education
  education: string;
  degree : string;

  // education: {
  //   degree: string;
  //   school: string;
  //   year: string;
  // }[];
  
  // Skills
  skills: string[];
  // skills: {
  //   name: string;
  //   level: number;
  // }[];
  
  // Languages
  languages: string[];
  // languages: {
  //   language: string;
  //   level: 'A1' | 'A2' | 'B1' | 'B2' | 'C1' | 'C2';
  // }[];
  
  // Availability
  availability: string;
  availability_details: string;
  estimated_response_time: string;
  // availability: {
  //   startDate: string;
  //   workingHours: number;
  // };
  
  // Services
  services: string[];
  workingHours: number;
  // services: {
  //   title: string;
  //   description: string;
  //   rate: number;
  //   rateUnit: 'hour' | 'day' | 'project';
  // }[];
}