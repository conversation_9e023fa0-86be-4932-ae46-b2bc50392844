import React, { useState, useEffect, useRef } from "react";
import SearchBar from "./SearchBar";
import { API_BASE_URL } from "./../config";
import { motion, AnimatePresence } from "framer-motion";

interface HeroProps {
  onSearch: (query: string, type: string) => void;
}

interface HeroImage {
  id: number;
  title: string;
  description: string;
  alt_text: string;
  image_url: string;
  thumbnail_url: string;
  position: number;
  is_active: boolean;
}

const Hero: React.FC<HeroProps> = ({ onSearch }) => {
  const [selectedType, setSelectedType] = useState("Services");
  const [searchValue, setSearchValue] = useState("");
  const [isLargeScreen, setIsLargeScreen] = useState(false);

  // 👉 Gestion des images Hero depuis API
  const [heroImages, setHeroImages] = useState<HeroImage[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  const getImageHero = (imagePath: string | undefined, defaultImage: string = "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") => {
        if (!imagePath) return defaultImage;
          console.log("Liens image : ",imagePath)  
          if (imagePath.startsWith('http')) return imagePath;  
          if (imagePath.startsWith('/')) return `${API_BASE_URL}/storage${imagePath}`;
          return `${API_BASE_URL}/storage/${imagePath}`;
        };

  useEffect(() => {
    const checkScreenSize = () => {
      setIsLargeScreen(window.innerWidth >= 1025);
    };
    
    // Vérifier la taille initiale
    checkScreenSize();
    
    // Écouter les changements de taille
    window.addEventListener('resize', checkScreenSize);
    
    // Nettoyer l'écouteur d'événement
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const onSearchRef = useRef(onSearch);
  useEffect(() => {
    onSearchRef.current = onSearch;
  }, [onSearch]);

  useEffect(() => {
    const id = setTimeout(() => {
      onSearchRef.current(searchValue, selectedType);
    }, 350);
    return () => clearTimeout(id);
  }, [searchValue, selectedType]);

  const handleTypeClick = (type: string) => {
    setSelectedType(type);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.trim() === "") {
      setSearchValue("");
      onSearch("", selectedType);
    } else {
      setSearchValue(value);
      onSearch(value, selectedType);
    }
  };

   // 👉 Récupération des images à l'ouverture
  useEffect(() => {
    const fetchHeroImages = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/hero-images`);
        const result = await response.json();

        if (result?.data && Array.isArray(result.data) && result.data.length > 0) {
          // tri par position croissante
          const sorted = result.data.sort(
            (a: HeroImage, b: HeroImage) => a.position - b.position
          );
          setHeroImages(sorted);
        } else {
          setHeroImages([]); // pas d'images
        }
      } catch (error) {
        console.error("Erreur API Hero Images :", error);
        setHeroImages([]); // en cas d'erreur on met tableau vide
      }
    };

    fetchHeroImages();
  }, []);


  // 👉 Gestion du slideshow (rotation toutes les 1s)
  useEffect(() => {
    if (heroImages.length > 1) {
      const interval = setInterval(() => {
        setCurrentIndex((prev) => (prev + 1) % heroImages.length);
      }, 3000); // 1 seconde
      return () => clearInterval(interval);
    }
  }, [heroImages]);

  // 👉 Déterminer quelle image afficher
  const getDisplayedImage = () => {
    if (heroImages.length === 0) {
      return {
        id:1,
        image_url:
          "https://hi-3d.com/wp-content/uploads/2025/08/ependes-salle-sport-double-polyvalente-4-pavillon-du-rialet-visu-2-1024x855.jpg",
        alt_text: "3D Art",
      };
    }
    if (heroImages.length === 1) {
      return heroImages[0];
    }
    return heroImages[currentIndex];
  };

  const displayedImage = getDisplayedImage();

  return (
    <div className="bg-white w-full flex items-stretch">
      <div className={`w-full  px-[10px] md:px-[40px] mx-auto grid ${isLargeScreen ? 'grid-cols-2' : 'grid-cols-1'} gap-10 items-end`}>
        {/* Left column - content */}
        <div className="flex flex-col justify-end text-left h-full w-full">
          <h1
            className="text-3xl md:text-5xl font-normal mb-6"
            style={{
              fontSize: "48px",
              fontFamily: "'Inter', sans-serif",
              fontWeight: 600,
              lineHeight: "1em",
              letterSpacing: "-3px",
              color: "#0D0C22",
            }}
          >
            Connect your vision
            <br />
            with top 3D artists
          </h1>

          <p
            className="text-base md:text-lg mb-10 font-normal"
            style={{
              fontSize: "18px",
              fontFamily: "'Inter', sans-serif",
              fontWeight: 400,
              lineHeight: "28px",
              color: "#0D0C22",
            }}
          >
            Explore work from the most talented and accomplished 3D artists
            ready to
            <br />
            take on your next project.
          </p>

          <div className="flex items-center space-x-4 mb-6">
            <button
              type="button"
              className={`border-none rounded-full py-2 px-6 font-sans font-medium text-sm h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm
                ${
                  selectedType === "Services"
                    ? "bg-black text-white hover:bg-[#F5F5F5] hover:text-gray-500"
                    : "bg-[#F5F5F5] text-gray-500 hover:bg-black hover:text-white"
                }`}
              style={{ fontFamily: "Arial, sans-serif", fontSize: "16px" }}
              onClick={() => handleTypeClick("Services")}
            >
              Services
            </button>
            <button
              type="button"
              className={`border-none rounded-full py-2 px-6 font-sans font-medium text-sm h-10 leading-5 cursor-pointer transition-colors duration-200 shadow-sm
                ${
                  selectedType === "3D Artiste"
                    ? "bg-black text-white hover:bg-[#F5F5F5] hover:text-gray-500"
                    : "bg-[#F5F5F5] text-gray-500 hover:bg-black hover:text-white"
                }`}
              style={{ fontFamily: "Arial, sans-serif", fontSize: "16px" }}
              onClick={() => handleTypeClick("3D Artiste")}
            >
              Artistes 3D
            </button>
          </div>

          {/* Barre de recherche responsive */}
          <div className="w-full">
            <SearchBar
              width={"100%"}
              height={60}
              iconSize={50}
              value={searchValue}
              onChange={handleSearchChange}
            />
          </div>
        </div>

        {/* Right column - image (visible seulement à partir de 1025px) */}
       

        {isLargeScreen && (
          <div className="flex justify-end w-full h-[400px] lg:h-[500px] relative overflow-hidden rounded-lg">
            <AnimatePresence mode="wait">
              <motion.img
                key={displayedImage.id}
                src={displayedImage.image_url}
                alt={displayedImage.alt_text}
                className="absolute top-0 left-0 w-full h-full object-cover rounded-lg"
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.8, ease: "easeInOut" }}
              />
            </AnimatePresence>
          </div>
        )}
      </div>
    </div>
  );
};

export default Hero;