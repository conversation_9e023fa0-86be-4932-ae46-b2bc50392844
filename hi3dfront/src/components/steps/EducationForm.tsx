import React from 'react';
import { Plus, X } from 'lucide-react';
import type { ProfileFormData } from '../types';

interface EducationFormProps {
  data: ProfileFormData;
  onChange: (data: Partial<ProfileFormData>) => void;
}

export default function EducationForm({ data, onChange }: EducationFormProps) {
  return (
    <div className="space-y-8">
      <div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <h3 className="text-lg font-semibold mb-4">Votre diplôme le plus élevé</h3>
            <input
              type="text"
              placeholder="Diplôme"
              value={data.degree || ''}  // Assurez-vous que `degree` est une chaîne ou un champ vide
              onChange={(e) => onChange({ degree: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="grid grid-cols-1 gap-4">
            <h3 className="text-lg font-semibold mb-4">Votre Etude</h3>
            <input
              type="text"
              placeholder="Etude"
              value={data.education || ''}  // Assurez-vous que `education` est une chaîne ou un champ vide
              onChange={(e) => onChange({ education: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
