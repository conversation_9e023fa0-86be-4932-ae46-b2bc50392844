import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';
import type { ProfileFormData } from '../types';

interface EducationFormProps {
  data: ProfileFormData;
  onChange: (data: Partial<ProfileFormData>) => void;
}

export default function EducationForm({ data, onChange }: EducationFormProps) {
  const [newEducation, setNewEducation] = useState({
    degree: '',
    school: '',
    year: '',
  });

  // const handleAddEducation = () => {
  //   if (newEducation.degree && newEducation.school) {
  //     onChange({
  //       education: [...data.education, newEducation],
  //     });
  //     setNewEducation({
  //       degree: '',
  //       school: '',
  //       year: '',
  //     });
  //   }
  // };

  // const handleRemoveEducation = (index: number) => {
  //   onChange({
  //     education: data.education.filter((_, i) => i !== index),
  //   });
  // };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Formation</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <input
              type="text"
              placeholder="Diplôme"
              value={newEducation.degree}
              onChange={(e) => setNewEducation({ ...newEducation, degree: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <input
              type="text"
              placeholder="École"
              value={newEducation.school}
              onChange={(e) => setNewEducation({ ...newEducation, school: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <input
              type="text"
              placeholder="Année"
              value={newEducation.year}
              onChange={(e) => setNewEducation({ ...newEducation, year: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          {/* <button
            onClick={handleAddEducation}
            className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center justify-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Ajouter la formation
          </button> */}
        </div>

        {/* <div className="mt-4 space-y-4">
          {data.education.map((edu, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-lg relative">
              <button
                onClick={() => handleRemoveEducation(index)}
                className="absolute top-2 right-2 p-1 text-gray-500 hover:text-red-500"
              >
                <X className="w-4 h-4" />
              </button>
              <h4 className="font-semibold">{edu.degree}</h4>
              <p className="text-gray-600">{edu.school}</p>
              <p className="text-sm text-gray-500">{edu.year}</p>
            </div>
          ))}
        </div> */}
      </div>
    </div>
  );
}