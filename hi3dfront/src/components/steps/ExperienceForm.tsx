import React, { useState } from 'react';
import { Plus, X, Link as LinkIcon } from 'lucide-react';
import type { ProfileFormData } from '../types';

interface ExperienceFormProps {
  data: ProfileFormData;
  onChange: (data: Partial<ProfileFormData>) => void;
}

export default function ExperienceForm({ data, onChange }: ExperienceFormProps) {
  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-semibold mb-4">Nombre d'années de votre expériences professionnelles</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <input
              type="number"
              placeholder="Année d'expérience professionnelle"
              value={data.experience || ''} // Si `experience` peut être nul, on utilise une chaîne vide
              onChange={(e) => onChange({ experience: parseInt(e.target.value) || 0 })} // Convertir en nombre
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="0"
              step="1"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold mb-4">Liens vers votre portfolio</h3>
        <div className="space-y-4">
          <div className="flex gap-2">
            <input
              type="url"
              placeholder="Lien vers votre projet"
              value={data.portfolio || ''} // Si `portfolio` peut être null, on utilise une chaîne vide
              onChange={(e) => onChange({ portfolio: e.target.value })} // Mise à jour de la valeur du lien
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
