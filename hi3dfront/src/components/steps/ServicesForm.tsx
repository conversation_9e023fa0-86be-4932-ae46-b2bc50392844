import React, { useState } from 'react';
import { Plus, X } from 'lucide-react';
import type { ProfileFormData } from '../types';

interface ServicesFormProps {
  data: ProfileFormData;
  onChange: (data: Partial<ProfileFormData>) => void;
}

const RATE_UNITS = ['hour', 'day', 'project'] as const;

export default function ServicesForm({ data, onChange }: ServicesFormProps) {
  const [newservice, setNewservice] = useState<string>(''); // État pour la compétence actuelle
    
      // Fonction pour ajouter la compétence au tableau
      const handleAddservice = () => {
        if (newservice.trim() !== '') {
          const updatedservices = [...data.services, newservice.trim()];
          onChange({ services: updatedservices });
          setNewservice(''); // Réinitialiser l'entrée après ajout
        }
      };
    
      // Fonction pour gérer la suppression d'une compétence
      const handleRemoveservice = (serviceToRemove: string) => {
        const updatedservices = data.services.filter(service => service !== serviceToRemove);
        onChange({ services: updatedservices });
      };
    
      // Fonction pour gérer la saisie de texte
      const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setNewservice(e.target.value);
      };
    
      // Fonction pour ajouter une compétence quand "Entrée" est pressé
      const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Enter') {
          handleAddservice();
        }
      };

  return (
    <div className="space-y-8">
      <div>
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <h3 className="text-lg font-semibold mb-4">Services</h3>
            <input
              type="text"
              placeholder="Service"
              value={newservice}
              onChange={handleInputChange}
              onKeyDown={handleKeyDown}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Affichage des compétences ajoutées sous forme de tags */}
          <div className="flex gap-2 flex-wrap mt-4">
            {data.services.map((service, index) => (
              <div key={index} className="flex items-center gap-2 bg-gray-200 rounded-lg px-3 py-1">
                <span>{service}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveservice(service)}
                  className="text-red-500"
                >
                  <X size={14} />
                </button>
              </div>
            ))}
          </div>

          {/* Bouton pour ajouter la compétence manuellement */}
          <div className="mt-4">
            <button
              type="button"
              onClick={handleAddservice}
              className="px-4 py-2 bg-blue-500 text-white rounded-md"
            >
              Ajouter le service
            </button>
          </div>

          <div className="grid grid-cols-1 gap-4">
            <h3 className="text-lg font-semibold mb-4">Tarif horaire</h3>
            <input
              type="text"
              placeholder="Tarif horaire"
              value={data.workingHours || ''} // Si `experience` peut être nul, on utilise une chaîne vide
              onChange={(e) => onChange({ workingHours: parseInt(e.target.value) || 0 })} // Convertir en nombre
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              min="0"
              step="1"
            />
          </div>

        </div>
      </div>
    </div>
  );
}