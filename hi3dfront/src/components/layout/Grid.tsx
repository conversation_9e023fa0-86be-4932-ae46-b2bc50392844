import React, { ReactNode } from 'react';

export interface GridProps {
  children: ReactNode;
  className?: string;
  cols?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
  mdCols?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
  lgCols?: 1 | 2 | 3 | 4 | 5 | 6 | 12;
  gap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12;
  rowGap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12;
  colGap?: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 8 | 10 | 12;
}

const Grid: React.FC<GridProps> = ({
  children,
  className = '',
  cols = 1,
  mdCols,
  lgCols,
  gap,
  rowGap,
  colGap,
}) => {
  // Grid columns classes
  const colsClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4',
    5: 'grid-cols-5',
    6: 'grid-cols-6',
    12: 'grid-cols-12',
  };
  
  // Medium breakpoint columns
  const mdColsClasses = mdCols ? {
    1: 'md:grid-cols-1',
    2: 'md:grid-cols-2',
    3: 'md:grid-cols-3',
    4: 'md:grid-cols-4',
    5: 'md:grid-cols-5',
    6: 'md:grid-cols-6',
    12: 'md:grid-cols-12',
  } : {};
  
  // Large breakpoint columns
  const lgColsClasses = lgCols ? {
    1: 'lg:grid-cols-1',
    2: 'lg:grid-cols-2',
    3: 'lg:grid-cols-3',
    4: 'lg:grid-cols-4',
    5: 'lg:grid-cols-5',
    6: 'lg:grid-cols-6',
    12: 'lg:grid-cols-12',
  } : {};
  
  // Gap classes
  const gapClasses = gap !== undefined ? {
    0: 'gap-0',
    1: 'gap-1',
    2: 'gap-2',
    3: 'gap-3',
    4: 'gap-4',
    5: 'gap-5',
    6: 'gap-6',
    8: 'gap-8',
    10: 'gap-10',
    12: 'gap-12',
  } : {};
  
  // Row gap classes
  const rowGapClasses = rowGap !== undefined ? {
    0: 'gap-y-0',
    1: 'gap-y-1',
    2: 'gap-y-2',
    3: 'gap-y-3',
    4: 'gap-y-4',
    5: 'gap-y-5',
    6: 'gap-y-6',
    8: 'gap-y-8',
    10: 'gap-y-10',
    12: 'gap-y-12',
  } : {};
  
  // Column gap classes
  const colGapClasses = colGap !== undefined ? {
    0: 'gap-x-0',
    1: 'gap-x-1',
    2: 'gap-x-2',
    3: 'gap-x-3',
    4: 'gap-x-4',
    5: 'gap-x-5',
    6: 'gap-x-6',
    8: 'gap-x-8',
    10: 'gap-x-10',
    12: 'gap-x-12',
  } : {};
  
  return (
    <div 
      className={`grid ${colsClasses[cols]} ${mdCols ? mdColsClasses[mdCols] : ''} ${lgCols ? lgColsClasses[lgCols] : ''} ${gap !== undefined ? gapClasses[gap] : ''} ${rowGap !== undefined ? rowGapClasses[rowGap] : ''} ${colGap !== undefined ? colGapClasses[colGap] : ''} ${className}`}
    >
      {children}
    </div>
  );
};

export default Grid;
