import React, { ReactNode } from 'react';
import Header from '../Header';
import Footer from '../Footer';

interface LayoutProps {
  children: ReactNode;
  hideHeader?: boolean;
  hideFooter?: boolean;
  className?: string;
}

/**
 * Composant Layout pour une structure cohérente des pages
 * avec une meilleure accessibilité
 */
const Layout: React.FC<LayoutProps> = ({
  children,
  hideHeader = false,
  hideFooter = false,
  className = ''
}) => {
  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      {!hideHeader && <Header />}

      <main id="main-content" className="flex-grow">
        {children}
      </main>

      {!hideFooter && <Footer />}
    </div>
  );
};

// Optimiser le composant avec React.memo pour éviter les re-rendus inutiles
export default React.memo(Layout);
