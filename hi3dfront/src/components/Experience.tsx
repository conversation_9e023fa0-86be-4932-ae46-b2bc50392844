import React, { useState } from "react";
import { API_BASE_URL } from '../config';
import { useProfile } from "./ProfileContext"; 

const Experience = () => {
  // Récupérer les données du localStorage
  const { profile, setProfile } = useProfile();
  const storedProfile = JSON.parse(localStorage.getItem("userProfile") || "{}");
  const initialExperience = storedProfile?.profile_data?.experience || 0;
  const initialPortfolio = storedProfile?.profile_data?.portfolio_url || "";

  const [experience, setExperience] = useState<number>(initialExperience);
  const [portfolioUrl, setPortfolioUrl] = useState<string>(initialPortfolio);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  // Mise à jour des champs
  const handleExperienceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setExperience(Number(e.target.value));
  };

  const handlePortfolioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPortfolioUrl(e.target.value);
  };

  // Envoi des données à l'API
  const handleSubmit = async () => {
    setLoading(true);
    setMessage("");

    const token = localStorage.getItem("token");

    try {
      const response = await fetch(
        `${API_BASE_URL}/api/profile/completion/experience`,
        {
          method: "PUT",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ experience, portfolio_url: portfolioUrl }),
        }
      );

      const data = await response.json();

      if (response.ok) {
        setMessage("✅ Expérience mise à jour avec succès !");
        
        // Mettre à jour le localStorage
        const updatedProfile = {
          ...storedProfile,
          profile_data: {
            ...storedProfile.profile_data,
            experience: experience,
            portfolio_url: portfolioUrl,
          },
        };

        const updateJiab = {
          ...profile,
          profile_data: {
            ...storedProfile.profile_data,
            experience: experience,
            portfolio_url: portfolioUrl,
          },
        };

        localStorage.setItem("userProfile", JSON.stringify(updatedProfile));
        setProfile(updateJiab);
      } else {
        setMessage("❌ Erreur : " + (data.message || "Impossible d'enregistrer l'expérience."));
      }
    } catch (error) {
      setMessage("❌ Erreur réseau, veuillez réessayer.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      <h2 className="text-sm font-bold text-gray-700 mb-4">EXPÉRIENCE PROFESSIONNELLE</h2>

      {message && (
        <div className={`mb-4 p-2 text-white rounded ${message.startsWith("✅") ? "bg-green-500" : "bg-red-500"}`}>
          {message}
        </div>
      )}

      {/* Nombre d'années d'expérience */}
      <div>
        <h3 className="text-lg font-semibold mb-4">Nombre d'années d'expérience</h3>
        <div className="space-y-4">
          <input
            type="number"
            placeholder="Année d'expérience professionnelle"
            value={experience}
            onChange={handleExperienceChange}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            min="0"
            step="1"
          />
        </div>
      </div>

      {/* Lien vers le portfolio */}
      <div className="mt-6">
        <h3 className="text-lg font-semibold mb-4">Lien vers votre portfolio</h3>
        <input
          type="url"
          placeholder="Lien vers votre projet"
          value={portfolioUrl}
          onChange={handlePortfolioChange}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      </div>

      {/* Bouton pour envoyer les données */}
      <div className="mt-6">
        <button
          onClick={handleSubmit}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition"
          disabled={loading}
        >
          {loading ? "Envoi en cours..." : "Enregistrer mon expérience"}
        </button>
      </div>
    </div>
  );
};

export default Experience;
