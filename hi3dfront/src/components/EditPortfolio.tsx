import React, { useState, useEffect } from "react";
import Header from "./Header";
import Footer from "./Footer";
import HeroProfile from "./HeroProfile";
import { API_BASE_URL } from "../config";
import { useNavigate,useParams, useLocation } from "react-router-dom";
import { profileService } from "../services/profileService";
import type { ServiceOffer, ServiceOfferFormData } from "./services/types";
import SwitchServiceWorkAbout from "./SwitchServiceWorkAbout";
import GalleryServicePro from "./GalleryServicePro";
import GalleryWorkPro from "./GalleryWorkPro";
import AboutPro from "./AboutPro";

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  is_professional: boolean;
}

// Interface pour les éléments du portfolio
interface PortfolioItem {
  id?: number;
  path?: string; 
  name?: string;
  type?: string;
  created_at?: string;
  // description?: string;
}

interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  phone: string;
  email?: string;
  address: string;
  city: string;
  country: string;
  skills: string[] | null;
  languages: string[] | null;
  availability_status: string | null;
  services_offered: string[];
  hourly_rate: string;
  completion_percentage: number;
  created_at: string;
  updated_at: string;
  avatar?: string;
  cover_photo?: string;
  profile_picture_path?: string;
  rating?: number;
  review_count?: number;
  bio?: string;
  title?: string;
  portfolio?: PortfolioItem[];
  user: User;
  likes_count?: number;
  views_count?: number;
}

interface Achievement {
  id: number;
  freelance_profile_id: number;
  title: string;
  organization: string;
  date_obtained: string;
  description: string;
  file_path: string | null;
  achievement_url: string | null;
  created_at: string;
  updated_at: string;
}


const EditPortfolio: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [services, setService] = useState<any[]>([]);
  const [servicesFix, setServicesFix] = useState<ServiceOffer[]>([]);
  const [bio, setBio] = useState<string | null>(null);
  const [avatar, setAvatar] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"services" | "work" | "about">("services");

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [projects, setProject] = useState<any | []>([]);
  const [professional, setProfessional] = useState<FreelanceProfile | null>(null);
  const [offers, setOffers] = useState([]);

  const { id } = useParams<{ id: string }>();
  const [idPro, setIdPro] = useState<any | null>(null);
  const [profiles, setProfiles] = useState<any | null>(null);

  const token = localStorage.getItem("token");
  const user = JSON.parse(localStorage.getItem("user") || "{}");

  const isPro = user.is_professional;

  // Fonction de changement d'onglet
  const handleTabChange = (tab: "services" | "work" | "about") => {
    setActiveTab(tab);
  };

  const getImageService = (imagePath: string | undefined, defaultImage: string = "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") => {
        if (!imagePath) return defaultImage;
          console.log("Liens image : ",imagePath)  
          if (imagePath.startsWith('http')) return imagePath;  
          if (imagePath.startsWith('/')) return `${API_BASE_URL}/storage${imagePath}`;
          return `${API_BASE_URL}/storage/${imagePath}`;
        };

  const recordProfessionalView = async (id: any) => {
      try {
        setLoading(true);

        const response = await fetch(`${API_BASE_URL}/api/professionals/${id}/view`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        });

        const data = await response.json();
        return data;
      } catch (error) {
        console.error("Erreur lors de l'enregistrement de la vue :", error);
        return null;
      } finally {
        setLoading(false);
      }
    };

  useEffect(() => {
    recordProfessionalView(id);
    fetchProfessional();
    fetchClientPendingOffers();
    fetchAchievements();
    // fetchServiceOffert();
  }, [id]);

  useEffect(() => {
    if(professional?.user_id != null){
      fetchServiceOffert();
    }
    
  }, [professional?.user_id]);

  const fetchServiceOffert = async () => {
        try {
          setLoading(true);
  
           const response = await fetch(`${API_BASE_URL}/api/professionals/${professional?.user_id}/service-offers`);
  
            if (!response.ok) {
              throw new Error('Erreur lors de la récupération des réalisations');
            }
        
            const data = await response.json();
        
            console.log("data :", data);
        
            if (Array.isArray(data)) {
              const formatted = data.map((item) => ({
                id: item.id,
                title: item.title,
                description: item.description || 'Projet réalisé avec passion et expertise technique.',
                // image_url: item.files[0].path 
                //   ? `${API_BASE_URL}/storage/${item.files[0].path}`
                //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
                
                image_url : Array.isArray(item.files) && item.files.length > 0
                  ? getImageService(item.files[0].path)//`${API_BASE_URL}/storage/${item.files[0].path}`
                  : item.image?getImageService(item.image):'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',     
                file_urls: Array.isArray(item.files)
                  ? item.files.map((file: any) => getImageService(file.path))//`${API_BASE_URL}/storage/${file.path}`)
                  : [],
                category: item.categories ? item.categories.join(" - ") : "",
                client_name: item.execution_time,
                professional_name: item.user.first_name+' '+item.user.last_name, // Si tu n'as pas cette info dans l'API
                professional_id: item.user.professional_details.id,
                date_create : item.created_at,
                price : item.price,
                user_id : item.user.id,
                avatar: item.user.professional_details.avatar? getImageUrl(String(item.user.professional_details.avatar)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
              }));
        
              console.log("Data Formater :", formatted);
              setService(formatted);
            } else {
              setService([]);
            }
        } catch (error) {
          console.error("Erreur lors de la récupération des réalisations:", error);
          setError("Impossible de charger les réalisations. Veuillez réessayer plus tard.");
        }finally{
        setLoading(false);
      }
      };

  const fetchAchievements = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_BASE_URL}/api/professionals/${id}/achievements`);
        
        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des réalisations');
        }
        
        const data = await response.json();
        
        if (data.success && data.achievements) {
          setAchievements(data.achievements);

          const formatted = data.achievements.map((proj:any) => ({
            id: proj.id,
            title: proj.title,
            description: proj.description || 'Projet réalisé avec passion et expertise technique.',
            image_url : proj.cover_photo
            ? getImageService(proj.cover_photo)//`${API_BASE_URL}/storage/${proj.cover_photo}`
            : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            file_urls : Array.isArray(proj.gallery_photos)
            ? proj.gallery_photos.map((file: any) => getImageService(file.path))//`${API_BASE_URL}/storage/${file.path}`)
            : [],
            category: proj.category,
            client_name: '',
            date_create: proj.created_at,
            professional_name: proj.professional.first_name+' '+proj.professional.last_name, // Si tu n'as pas cette info dans l'API
            professional_id: proj.professional_profile_id || 1,
            user_id : proj.professional.user_id,
            avatar: proj.professional.avatar? getImageUrl(String(proj.professional.avatar)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          }));

        console.log("Data Formater :", formatted);
        setProject(formatted);
        } else {
          setAchievements([]);
          setProject([]);
        }
      } catch (error) {
        console.error("Erreur lors de la récupération des réalisations:", error);
        setError("Impossible de charger les réalisations. Veuillez réessayer plus tard.");
        
      }finally{
        setLoading(false);
      }
    };

    const fetchProfessional = async () => {
        try {
          setLoading(true);
          // Essayer d'abord l'endpoint professionals
          let response = await fetch(`${API_BASE_URL}/api/professionals/${id}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json',
            },
          });
  
          // Si l'endpoint professionals échoue, essayer l'endpoint users
          if (!response.ok) {
            console.log('Endpoint professionals a échoué, essai de l\'endpoint users');
            response = await fetch(`${API_BASE_URL}/api/users/${id}`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json',
              },
            });
          }
  
          if (!response.ok) {
            throw new Error('Impossible de récupérer les détails du professionnel');
          }
  
          const data = await response.json();
          console.log('Données du professionnel récupérées:', data);
  
          // Traiter les données selon le format
          if (data.professional) {
            // Traiter les skills qui peuvent être une chaîne JSON ou un tableau
            let skills = [];
            if (data.professional.skills) {
              if (Array.isArray(data.professional.skills)) {
                skills = data.professional.skills;
              } else if (typeof data.professional.skills === 'string') {
                try {
                  skills = JSON.parse(data.professional.skills);
                } catch (e) {
                  skills = [data.professional.skills]; // Si ce n'est pas un JSON valide, le traiter comme une chaîne simple
                }
              }
            }
  
            // Traiter le portfolio qui peut être une chaîne JSON ou un tableau
            let portfolio = [];
            if (data.professional.portfolio) {
              if (Array.isArray(data.professional.portfolio)) {
                portfolio = data.professional.portfolio;
              } else if (typeof data.professional.portfolio === 'string') {
                try {
                  portfolio = JSON.parse(data.professional.portfolio);
                } catch (e) {
                  portfolio = []; // Si ce n'est pas un JSON valide, utiliser un tableau vide
                }
              }
            }
  
            // Mettre à jour les données du professionnel
            setProfessional({
              ...data.professional,
              skills: skills,
              portfolio: portfolio.length > 0 ? portfolio : [
                { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
              ],
              likes_count: data.professional.likes_count,
              views_count: data.professional.views_count,
            });
          } else if (data.user && data.profile_data) {
            // Traiter le portfolio qui peut être une chaîne JSON ou un tableau
            let portfolio = [];
            if (data.profile_data.portfolio) {
              if (Array.isArray(data.profile_data.portfolio)) {
                portfolio = data.profile_data.portfolio;
              } else if (typeof data.profile_data.portfolio === 'string') {
                try {
                  portfolio = JSON.parse(data.profile_data.portfolio);
                } catch (e) {
                  portfolio = []; // Si ce n'est pas un JSON valide, utiliser un tableau vide
                }
              }
            }
  
            setProfessional({
              ...data.profile_data,
              user: data.user,
              portfolio: portfolio.length > 0 ? portfolio : [
                { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
              ],
              likes_count: data.user.likes_count || 0,
              views_count: data.user.views_count || 0,
            });
          } else if (data.user) {
            setProfessional({
              id: data.user.id,
              user_id: data.user.id,
              first_name: data.user.first_name,
              last_name: data.user.last_name,
              phone: '',
              address: '',
              city: data.user.city || 'Paris',
              country: data.user.country || 'France',
              skills: data.user.skills || ['Animation 3D', 'Modélisation 3D', 'Rigging'],
              languages: ['Français', 'Anglais'],
              availability_status: data.user.availability_status || 'available',
              services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
              hourly_rate: data.user.hourly_rate || '45',
              completion_percentage: data.completion_percentage || 100,
              created_at: data.user.created_at,
              updated_at: data.user.updated_at,
              avatar: data.user.avatar,
              profile_picture_path: data.user.profile_picture_path,
              rating: data.user.rating || 4.8,
              review_count: data.user.review_count || 27,
              bio: data.user.bio || 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
              title: data.user.title || 'Artiste 3D',
              portfolio: data.user.portfolio || [
                { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
              ],
              likes_count: data.user.likes_count || 0,
              views_count: data.user.views_count || 0,
              user: data.user,
            });
          } else {
            // Utiliser des données de démonstration
            setProfessional({
              id: parseInt(id || '1'),
              user_id: parseInt(id || '1'),
              first_name: 'Thomas',
              last_name: 'Martin',
              phone: '+33 6 12 34 56 78',
              address: '123 Rue de la Création',
              city: 'Paris',
              country: 'France',
              skills: ['Animation 3D', 'Modélisation 3D', 'Rigging', 'Texturing', 'Rendu 3D'],
              languages: ['Français', 'Anglais'],
              availability_status: 'available',
              services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
              hourly_rate: '45',
              completion_percentage: 100,
              created_at: '2023-01-01',
              updated_at: '2023-01-01',
              avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
              profile_picture_path: 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
              rating: 4.8,
              review_count: 27,
              portfolio: [
                { name: 'Projet 3D 1', path: 'https://picsum.photos/seed/101/300/200', type: 'Modélisation 3D pour un jeu vidéo' },
                { name: 'Animation 3D', path: 'https://picsum.photos/seed/102/300/200', type: 'Animation de personnage' },
                { name: 'Rendu architectural', path: 'https://picsum.photos/seed/103/300/200', type: 'Visualisation architecturale' },
              ],
              likes_count: 0,
              views_count: 0,
              bio: 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
              title: 'Artiste 3D',
              user: {
                id: parseInt(id || '1'),
                first_name: 'Thomas',
                last_name: 'Martin',
                email: '<EMAIL>',
                is_professional: true,
              },
            });
          }
        } catch (err) {
          console.error('Erreur lors de la récupération du professionnel:', err);
          if (err instanceof Error) {
            setError(err.message);
          } else {
            setError('Une erreur inconnue est survenue');
          }
  
          // Utiliser des données de démonstration en cas d'erreur
          setProfessional({
            id: parseInt(id || '1'),
            user_id: parseInt(id || '1'),
            first_name: 'Thomas',
            last_name: 'Martin',
            phone: '+33 6 12 34 56 78',
            address: '123 Rue de la Création',
            city: 'Paris',
            country: 'France',
            skills: ['Animation 3D', 'Modélisation 3D', 'Rigging', 'Texturing', 'Rendu 3D'],
            languages: ['Français', 'Anglais'],
            availability_status: 'available',
            services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
            hourly_rate: '45',
            completion_percentage: 100,
            created_at: '2023-01-01',
            updated_at: '2023-01-01',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
            profile_picture_path: 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
            rating: 4.8,
            review_count: 27,
            portfolio: [
              { name: 'Projet 3D 1', path: 'https://picsum.photos/seed/101/300/200', type: 'Modélisation 3D pour un jeu vidéo' },
              { name: 'Animation 3D', path: 'https://picsum.photos/seed/102/300/200', type: 'Animation de personnage' },
              { name: 'Rendu architectural', path: 'https://picsum.photos/seed/103/300/200', type: 'Visualisation architecturale' },
            ],
            likes_count: 0,
            views_count: 0,
            bio: 'Artiste 3D passionné avec plus de 5 ans d\'expérience dans la création de modèles 3D et d\'animations pour des jeux vidéo et des films.',
            title: 'Artiste 3D',
            user: {
              id: parseInt(id || '1'),
              first_name: 'Thomas',
              last_name: 'Martin',
              email: '<EMAIL>',
              is_professional: true,
            },
          });
        } finally {
          setLoading(false);
        }
      };
  
      const fetchClientPendingOffers = async () => {
        if (!token) return;
        setLoading(true);
        try {
          const response = await fetch(`${API_BASE_URL}/api/client/open-offers/pending`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Accept': 'application/json',
            },
          });
  
          if (!response.ok) {
            throw new Error(`Erreur ${response.status}`);
          }
  
          const data = await response.json();
          console.log("Mes Offre : ", data);
          setOffers(data.offers);
          return data.offers;
        } catch (error) {
          console.error('Erreur lors de la récupération des offres client :', error);
          return [];
        }finally{
          setLoading(false);
        }
      };

  useEffect(() => {
    if (location.state && location.state.activeTab) {
      setActiveTab(location.state.activeTab);
    }
  }, [location.state]);

  const getImageUrl = (
    imagePath: string | undefined,
    defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg"
  ) => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith("http")) return imagePath;
    if (imagePath.startsWith("/")) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  if (loading) {
      return (
        <div className="min-h-screen bg-white">
          <Header />
          <div className="flex justify-center items-center h-96">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
          <Footer />
        </div>
      );
    }

   if (!professional) {
      return (
        <div className="min-h-screen bg-white">
          <Header />
          <div className="flex justify-center items-center h-96">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
          <Footer />
        </div>
      );
    }

  return (
    <>
      <Header />
      {(id && professional) && (
        <>
        <HeroProfile pro_detail={professional} isPro = {false}/>
        {/* <HeroProfile /> */}
        <SwitchServiceWorkAbout
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />

        {activeTab === "services" && <GalleryServicePro items={services}/>}
        {activeTab === "work" && <GalleryWorkPro items = {projects}/>}
        {activeTab === "about" && <AboutPro pro_detail={professional}/>}
        </>
      )}
    
      <Footer />
    </>
  );
};

export default EditPortfolio;