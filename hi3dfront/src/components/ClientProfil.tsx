import React, { useState, useEffect } from "react";
import Header from "./Header";
import Footer from "./Footer";
import HeroClient from "./HeroClient";
import { API_BASE_URL } from "../config";
import { useNavigate, useLocation } from "react-router-dom";
import { profileService } from "../services/profileService";
import { ProfileData } from '../services/profileService';

const ClientProfil: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const [loading, setLoading] = useState<boolean>(true);
  const [profiles, setProfile] = useState<any | null>(null);
  const [error, setError] = useState<string | null>(null);

  const token = localStorage.getItem("token");
  const user = JSON.parse(localStorage.getItem("user") || "{}");

  useEffect(() => {
    const fetchProfile = async () => {
      setLoading(true);
      try {
        // Utiliser le service API réel pour récupérer les données du profil
        const data = await profileService.getProfile();
        setProfile(data.profile);
        setError(null);
        console.log('Profil récupéré avec succès:', data.profile);
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError('Impossible de charger le profil. Veuillez réessayer plus tard.');
      } finally {
        setLoading(false);
      }
    };

    fetchProfile();
  }, [token]);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Non spécifié';

    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

   if (loading) {
      return (
        <div className="min-h-screen bg-white">
          <Header />
          <div className="flex justify-center items-center h-96">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
          <Footer />
        </div>
      );
    }

  

  return (
    <>
    
      <Header />

      {/* {(professional) && ( */}
        <>
        <HeroClient profile={profiles}/>
        <div className="w-full px-[10px] md:px-[40px] mx-auto mt-20 py-2">
          <div className="flex space-x-4">
            

            {/* Bouton About */}
            <button
              type="button"
              // onClick={() => onTabChange('about')}
              className="bg-transparent rounded-full py-2 px-8 font-sans font-medium text-base cursor-pointer transition-colors duration-200 shadow-sm"
              style={{
                fontFamily: "'Inter', sans-serif",
                backgroundColor: "rgb(0, 110, 255)",
                fontWeight: 400,
                color: "white",
                fontSize: "16px",
              }}
              // onMouseOver={(e) => {
              //   if (activeTab !== 'about') {
              //     e.currentTarget.style.backgroundColor = "rgb(0, 110, 255)";
              //     e.currentTarget.style.color = "white";
              //   }
              // }}
              // onMouseOut={(e) => {
              //   if (activeTab !== 'about') {
              //     e.currentTarget.style.backgroundColor = "#f6f7f8";
              //     e.currentTarget.style.color = "rgb(72, 72, 72)";
              //   }
              // }}
            >
              About
            </button>
          </div>
        </div>
        
        <div className="w-full">
        <div className="w-full px-[10px] md:px-[40px] mx-auto">
          <h1
            className="mb-8 mt-8"
            style={{
              fontSize: "28px",
              fontFamily: "'Inter', sans-serif",
              fontWeight: 500,
              lineHeight: "1em",
              color: "#0D0C22",
            }}
          >
            Biography 
          </h1>

          <div className="w-full flex flex-col md:flex-row gap-8">
            {/* Colonne de gauche - Contenu principal (70%) */}
            <div className="w-full md:w-[70%] mdx:w-[60%] bg-white rounded-lg">
              <p
                className="mb-2 text-left"
                style={{
                  fontSize: "17px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
                  lineHeight: "1.4em",
                  color: "#0D0C22",
                }}
              >
                {profiles?.bio || "Aucune biographie disponible."}
              </p>

              <h2
                className="mb-2 text-left"
                style={{
                  fontSize: "17px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
                  color: "#0D0C22",
                }}
              >
                Company name : 
              </h2>
              <div
                className="flex flex-col gap-3 mb-3"
                style={{
                  fontSize: "17px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
                  color: "#0D0C22",
                }}
              >
              <div  className="flex items-center text-left">
                <span className="text-black-500 mr-3">✦</span>
                <span
                  style={{
                    fontSize: "17px",
                    fontFamily: "'Inter', sans-serif",
                    fontWeight: 400,
                    color: "#0D0C22",
                  }}
                >
                  {profiles?.company_name||"COMPANY NAME"}
                </span>
              </div>
                
              </div>

              <h2
                className="mb-2 text-left"
                style={{
                  fontSize: "17px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
                  color: "#0D0C22",
                }}
              >
                Industry sector : 
              </h2>
              <div
                className="flex flex-col gap-3 mb-3"
                style={{
                  fontSize: "17px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
                  color: "#0D0C22",
                }}
              >
              <div  className="flex items-center text-left">
                <span className="text-black-500 mr-3">✦</span>
                <span
                  style={{
                    fontSize: "17px",
                    fontFamily: "'Inter', sans-serif",
                    fontWeight: 400,
                    color: "#0D0C22",
                  }}
                >
                  {profiles?.industry||"Industry sector"}
                </span>
              </div>
                
              </div>

              <h2
                className="mb-2 text-left"
                style={{
                  fontSize: "17px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
                  color: "#0D0C22",
                }}
              >
                Company size : 
              </h2>
              <div
                className="flex flex-col gap-3 mb-3"
                style={{
                  fontSize: "17px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
                  color: "#0D0C22",
                }}
              >
              <div  className="flex items-center text-left">
                <span className="text-black-500 mr-3">✦</span>
                <span
                  style={{
                    fontSize: "17px",
                    fontFamily: "'Inter', sans-serif",
                    fontWeight: 400,
                    color: "#0D0C22",
                  }}
                >
                  {profiles?.company_size||"Company size"}
                </span>
              </div>
                
              </div>
              
              
            </div>

            {/* Colonne de droite - Sidebar (30%) */}
            <div className="w-full md:w-[30%] mdx:w-[40%]">
              <div className="bg-[#f6f7f8] p-6 rounded-lg sticky top-6">
                <div className="flex flex-col gap-4">
                  {/* Localisation - England */}
                  <div className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="ionicon h-5 w-5 text-gray-600 mr-2"
                      viewBox="0 0 512 512"
                    >
                      <circle cx="256" cy="192" r="32"></circle>
                      <path d="M256 32c-88.22 0-160 68.65-160 153 0 40.17 18.31 93.59 54.42 158.78 29 52.34 62.55 99.67 80 123.22a31.75 31.75 0 0051.22 0c17.42-23.55 51-70.88 80-123.22C397.69 278.61 416 225.19 416 185c0-84.35-71.78-153-160-153zm0 224a64 64 0 1164-64 64.07 64.07 0 01-64 64z"></path>
                    </svg>
                    <span className="text-gray-800 font-normal text-base">
                      {profiles?.address} {profiles?.city} {profiles?.country}
                    </span>
                  </div>

                  {/* Badge PRO et texte Pro account alignés */}
                  <div className="flex items-center gap-2">
                    <span className="bg-black text-white text-xs font-medium px-2 py-1 rounded-full">
                    Email 
                    </span>
                    <span className="text-gray-800 font-normal text-base">
                    {profiles?.email}
                    </span>
                    
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="bg-black text-white text-xs font-medium px-2 py-1 rounded-full">
                    Phone 
                    </span>
                    <span className="text-gray-800 font-normal text-base">
                    {profiles?.phone || 'Non spécifié'}
                    </span>
                  </div>
                </div>
                
              </div>
            </div>
          </div>
        </div>
      </div>
        </>
      {/* )} */}

      <Footer />
    </>
  );
};

export default ClientProfil;