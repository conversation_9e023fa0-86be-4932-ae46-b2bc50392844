// import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
// import { faCheckCircle, faTimesCircle, faPlaneDeparture, faQuestionCircle } from "@fortawesome/free-solid-svg-icons";
// import PropTypes from "prop-types";
// import React from "react";

// const AvailabilityStatus = ({ status,time }: { status: string,time:string }) => {
//   const getStatusInfo = (status: string) => {
//     switch (status) {
//       case "available":
//         return { text: "Disponible", icon: faCheckCircle, color: "text-green-500" };
//       case "busy":
//         return { text: "Occupé", icon: faTimesCircle, color: "text-red-500" };
//       case "unavailable":
//           return { text: "Non disponible", icon: faTimesCircle, color: "text-red-500" };
//       case "vacation":
//         return { text: "En Vacance", icon: faPlaneDeparture, color: "text-yellow-500" };
//       default:
//         return { text: "Aucun status", icon: faQuestionCircle, color: "text-gray-500" };
//     }
//   };

//   const { text, icon, color } = getStatusInfo(status);

//   return (
//     <p className="text-gray-600 mt-2 flex items-center gap-2">
//       <span className="font-bold">Disponibilité :</span>
//       <FontAwesomeIcon icon={icon} className={color} /> {text}
//     </p>
//   );
// };

// AvailabilityStatus.propTypes = {
//   status: PropTypes.string.isRequired,
// };

// export default AvailabilityStatus;


import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faCheckCircle, faTimesCircle, faPlaneDeparture, faQuestionCircle } from "@fortawesome/free-solid-svg-icons";
import PropTypes from "prop-types";
import React from "react";

const AvailabilityStatus = ({ status, time }: { status: string, time?: string }) => {
  const getStatusInfo = (status: string) => {
    switch (status) {
      case "available":
        return { text: "Disponible", icon: faCheckCircle, color: "text-green-500" };
      case "busy":
        return { text: "Occupé", icon: faTimesCircle, color: "text-red-500" };
      case "unavailable":
        return { text: "Non disponible", icon: faTimesCircle, color: "text-red-500" };
      case "vacation":
        return { text: "En Vacance", icon: faPlaneDeparture, color: "text-yellow-500" };
      default:
        return { text: "Aucun status", icon: faQuestionCircle, color: "text-gray-500" };
    }
  };

  // Convertir le temps en format "JJ-MM-AAAA HH:MM"
  const formatDateTime = (isoString: string) => {
    if (!isoString) return null;
    const date = new Date(isoString);
    return date.toLocaleString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    }).replace(",", ""); // Enlever la virgule entre la date et l'heure
  };

  const { text, icon, color } = getStatusInfo(status);
  const formattedTime = time ? formatDateTime(time) : null;

  return (
    <>
    <p className="text-gray-600 mt-2 flex items-center gap-2">
      <span className="font-bold">Disponibilité :</span>
      <FontAwesomeIcon icon={icon} className={color} /> {text}
    </p>
    <p>
      {status === "available" && <span className="text-green-500 font-semibold"> Répond rapidement </span>}
      {status === "unavailable" && formattedTime && (
        <span className="text-red-500 font-semibold"> Délai de réponse : {formattedTime} </span>
      )}
    </p>
    </>
  );
};

AvailabilityStatus.propTypes = {
  status: PropTypes.string.isRequired,
  time: PropTypes.string,
};

export default AvailabilityStatus;

