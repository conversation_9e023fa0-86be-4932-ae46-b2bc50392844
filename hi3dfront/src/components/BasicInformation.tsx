import React, { useState } from "react";
import { API_BASE_URL } from '../config';
import { useProfile } from "./ProfileContext";


const BasicInformation: React.FC = () => {

  const { profile, setProfile } = useProfile();
  const storedProfile = JSON.parse(localStorage.getItem('userProfile') || '{}');
  const profileData = storedProfile?.profile_data || {};
  const user = JSON.parse(localStorage.getItem('user') || '{}'); // Correction ici

  const [formData, setFormData] = useState({
    firstName: profileData.first_name || '',
    lastName: profileData.last_name || '',
    email: user.email || '', // Utiliser user.email
    phone: profileData.phone || '',
    address: profileData.address || '',
    city: profileData.city || '',
    country: profileData.country || '',
    profileType: storedProfile && storedProfile.profile_type === "freelance" ? "Indépendant" : "Entreprise",
    profileImage: "https://images.unsplash.com/photo-1522075469751-3a6694fb2f61?q=80&w=2080&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D", // Image statique par défaut
  });

  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setFormData({ ...formData, profileImage: URL.createObjectURL(file) });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setMessage("");

    const token = localStorage.getItem("token");
    const requestData = {
      first_name: formData.firstName,
      last_name: formData.lastName,
      company_name: formData.profileType === "Entreprise" ? formData.firstName + " Corp" : "",
      company_size: "Medium",
      industry: "Technology",
      phone: formData.phone,
      address: formData.address,
      city: formData.city,
      country: formData.country,
    };

    console.log('Donnée envoyer :', JSON.stringify(requestData));

    try {
      const response = await fetch(`${API_BASE_URL}/api/profile/completion/personal`, {
        method: "PUT",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();

      console.log('Reponse :', data);
      if (response.ok) {
        const updatedProfile = {
          ...storedProfile,
          profile_data: {
            ...storedProfile.profile_data,
            first_name: formData.firstName,
            last_name: formData.lastName,
            phone: formData.phone,
            address: formData.address,
            city: formData.city,
            country: formData.country,
          },
        };

        const updateJiab = {
          ...profile,
          profile_data: {
            ...storedProfile.profile_data,
            first_name: formData.firstName,
            last_name: formData.lastName,
            phone: formData.phone,
            address: formData.address,
            city: formData.city,
            country: formData.country,
          },
        };

        localStorage.setItem("userProfile", JSON.stringify(updatedProfile));
        setMessage("✅ Modification effectuée avec succès !");
        setProfile(updateJiab);
        // Rafraîchir la page après une modification réussie
        // window.location.reload();
      } else {
        setMessage("❌ Erreur : " + (data.message || "Une erreur est survenue."));
      }
    } catch (error) {
      setMessage("❌ Erreur réseau, veuillez réessayer.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white px-4 sm:px-6 lg:px-8">
      <h2 className="text-xl font-bold mb-4">Données personnelles</h2>

      {/* Section Image de Profil */}
      <div className="flex items-center space-x-4 mb-6">
        <div className="w-24 h-24 rounded-full border overflow-hidden">
          <img
            src={formData.profileImage}
            alt="Profile"
            className="w-full h-full object-cover"
          />
        </div>
        <label className="text-blue-600 cursor-pointer">
          📤 Remplacer
          <input
            type="file"
            accept="image/*"
            className="hidden"
            onChange={handleImageChange}
          />
        </label>
      </div>

      {message && (
        <div className={`mb-4 p-2 text-white rounded ${message.startsWith("✅") ? "bg-green-500" : "bg-red-500"}`}>
          {message}
        </div>
      )}
      {/* Formulaire */}
      <form className="grid grid-cols-2 gap-4">
        <div>
          <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">Prénom</label>
          <input
            type="text"
            name="firstName"
            id="firstName"
            value={formData.firstName}
            onChange={handleChange}
            className="w-full border-gray-300 rounded-md p-2 shadow-sm focus:ring-blue-500 focus:border-blue-500"
            aria-label="Prénom"
          />
        </div>

        <div>
          <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">Nom</label>
          <input
            type="text"
            name="lastName"
            id="lastName"
            value={formData.lastName}
            onChange={handleChange}
            className="w-full border-gray-300 rounded-md p-2 shadow-sm focus:ring-blue-500 focus:border-blue-500"
            aria-label="Nom"
          />
        </div>

        <div className="col-span-2">
          <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email</label>
          <input
            type="email"
            name="email"
            id="email"
            value={formData.email}
            readOnly
            // onChange={handleChange}
            className="w-full border-gray-300 rounded-md p-2 shadow-sm focus:ring-blue-500 focus:border-blue-500"
            aria-label="Email"
          />
        </div>

        <div className="col-span-2">
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700">Téléphone</label>
          <input
            type="text"
            name="phone"
            id="phone"
            value={formData.phone}
            onChange={handleChange}
            className="w-full border-gray-300 rounded-md p-2 shadow-sm focus:ring-blue-500 focus:border-blue-500"
            aria-label="Téléphone"
          />
        </div>

        <div className="col-span-2">
          <label htmlFor="address" className="block text-sm font-medium text-gray-700">Adresse</label>
          <input
            type="text"
            name="address"
            id="address"
            value={formData.address}
            onChange={handleChange}
            className="w-full border-gray-300 rounded-md p-2 shadow-sm focus:ring-blue-500 focus:border-blue-500"
            aria-label="Adresse"
          />
        </div>

        <div>
          <label htmlFor="city" className="block text-sm font-medium text-gray-700">Ville</label>
          <input
            type="text"
            name="city"
            id="city"
            value={formData.city}
            onChange={handleChange}
            className="w-full border-gray-300 rounded-md p-2 shadow-sm focus:ring-blue-500 focus:border-blue-500"
            aria-label="Ville"
          />
        </div>

        <div>
          <label htmlFor="country" className="block text-sm font-medium text-gray-700">Pays</label>
          <input
            type="text"
            name="country"
            id="country"
            value={formData.country}
            onChange={handleChange}
            className="w-full border-gray-300 rounded-md p-2 shadow-sm focus:ring-blue-500 focus:border-blue-500"
            aria-label="Pays"
          />
        </div>

        <div className="col-span-2">
          <label className="block text-sm font-medium text-gray-700">Type de profil</label>
          <select
            name="profileType"
            value={formData.profileType}
            disabled
            // onChange={handleChange}
            className="w-full border-gray-300 rounded-md p-2 shadow-sm focus:ring-blue-500 focus:border-blue-500"
            aria-label="Type de profil"
          >
            <option>Indépendant</option>
            <option>Entreprise</option>
            <option>Particulier</option>
          </select>
        </div>

        <div className="mb-6">
          <button
            type="button"
            onClick={handleSubmit}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition"
            disabled={loading}
          >
            {loading ? "Envoi en cours..." : "Modifier mes informations personnelles"}

          </button>
        </div>

      </form>
    </div>
  );
};

export default BasicInformation;
