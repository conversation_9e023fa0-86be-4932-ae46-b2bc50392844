"use client";
import React, { useEffect, useMemo, useState } from "react";
import Modal from "./Modal";
// adapte si besoin les chemins ci-dessous selon ton projet :
import SkillPicker, { SkillsByCategory } from "./SkillPicker";
import LocationSelector from "./../components/ui/LocationSelector";
// idem : adapte selon ton projet
import { API_BASE_URL } from "./../config";

type ProfileSlice = {
  bio: string;
  skills: string[];
  languages: string[];
  address: string;
  city: string;
  country: string;
};

// Liste des compétences par catégorie (ta liste)
const skillsByCategory: SkillsByCategory = {
  modeling: [
    "Blender",
    "Maya",
    "3ds Max",
    "ZBrush",
    "Substance Painter",
    "Hard Surface Modeling",
    "Organic Modeling",
  ],
  animation: [
    "Animation de personnages",
    "Animation d'objets",
    "Motion Capture",
    "Rigging",
    "Facial Animation",
  ],
  architectural: [
    "SketchUp",
    "Revit",
    "ArchiCAD",
    "Lumion",
    "V-Ray",
    "Rendu architectural",
    "Modélisation BIM",
  ],
  product: [
    "Fusion 360",
    "SolidWorks",
    "Rhino 3D",
    "KeyShot",
    "Prototypage 3D",
    "Design industriel",
  ],
  character: [
    "Character Design",
    "Character Modeling",
    "Character Rigging",
    "Facial Rigging",
    "Sculpting",
  ],
  environment: [
    "Environment Design",
    "Landscape Modeling",
    "Terrain Generation",
    "World Building",
    "Level Design",
  ],
  vr_ar: ["Unity", "Unreal Engine", "WebXR", "A-Frame", "ARKit", "ARCore", "Oculus SDK"],
  game_art: [
    "Game Asset Creation",
    "Low Poly Modeling",
    "Texture Baking",
    "UV Mapping",
    "PBR Texturing",
  ],
};

const PREDEFINED_LANGUAGES = [
  "Français",
  "Anglais",
  "Espagnol",
  "Allemand",
  "Italien",
  "Portugais",
  "Russe",
  "Chinois",
  "Japonais",
  "Arabe",
];

export default function EditBioModal({
  open,
  onClose,
  onSaved,
  title = "Edit your Bio & Skills",
}: {
  open: boolean;
  onClose: () => void;
  onSaved?: (p: ProfileSlice) => void;
  title?: string;
}) {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [bio, setBio] = useState("");
  const [skills, setSkills] = useState<string[]>([]);
  const [languages, setLanguages] = useState<string[]>([]);
  const [address, setAddress] = useState("");
  const [city, setCity] = useState("");
  const [country, setCountry] = useState("");

  // Charger le profil à l’ouverture
  useEffect(() => {
    if (!open) return;
    (async () => {
      setLoading(true);
      setError(null);
      setSuccess(null);
      try {
        const token = localStorage.getItem("token");
        if (!token) throw new Error("Unauthenticated");
        const res = await fetch(`${API_BASE_URL}/api/profile`, {
          headers: { Authorization: `Bearer ${token}` },
        });
        if (!res.ok) throw new Error("Failed to fetch profile");
        const data = await res.json();

        const p = data?.profile || {};
        setBio(p.bio ?? "");
        setSkills(Array.isArray(p.skills) ? p.skills : []);
        setLanguages(Array.isArray(p.languages) ? p.languages : []);
        setAddress(p.address ?? "");
        setCity(p.city ?? "");
        setCountry(p.country ?? "");
      } catch (e: any) {
        setError(e?.message || "Could not fetch your profile data");
      } finally {
        setLoading(false);
      }
    })();
  }, [open]);

  const toggleLanguage = (l: string) =>
    setLanguages((curr) => (curr.includes(l) ? curr.filter((x) => x !== l) : [...curr, l]));

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);
    setSuccess(null);
    try {
      const token = localStorage.getItem("token");
      if (!token) throw new Error("Unauthenticated");

      const payload: ProfileSlice = {
        bio,
        skills,
        languages,
        address,
        city,
        country,
      };

      const res = await fetch(`${API_BASE_URL}/api/profile`, {
        method: "PUT",
        headers: { Authorization: `Bearer ${token}`, "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const err = await res.json().catch(() => ({}));
        throw new Error(err.message || "Failed to update profile");
      }
      setSuccess("Profile updated successfully!");
      onSaved?.(payload);
      // onClose(); // décommente si tu veux fermer après save
    } catch (e: any) {
      setError(e?.message || "Profile update failed");
    } finally {
      setSaving(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose} title={title} maxWidthClass="max-w-3xl">
      {loading ? (
        <div className="flex items-center justify-center h-40">
          <div className="animate-spin h-8 w-8 border-2 border-blue-600 border-t-transparent rounded-full" />
        </div>
      ) : (
        <form onSubmit={onSubmit} className="mt-1">
          {/* zone scrollable */}
          <div className="max-h-[70vh] overflow-y-auto pr-2 space-y-8">
            {/* Alerts */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}
            {success && (
              <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                {success}
              </div>
            )}

            {/* Bio */}
            <section className="space-y-2">
              <label htmlFor="bio" className="block text-sm font-medium text-neutral-800">
                Biographie / À propos de moi
              </label>
              <textarea
                id="bio"
                rows={6}
                className="w-full rounded-2xl border border-gray-300 bg-gray-50 px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-gray-400"
                value={bio}
                onChange={(e) => setBio(e.target.value)}
                placeholder="Parle de toi, de ton expérience et de ton expertise…"
              />
            </section>

            {/* Compétences */}
            <section className="space-y-3">
              <h3 className="text-sm font-medium text-neutral-800">Compétences</h3>
              <SkillPicker
                categories={skillsByCategory}
                value={skills}
                onChange={setSkills}
                allowCustom={false}
              />
            </section>

            {/* Langues */}
            <section className="space-y-3">
              <h3 className="text-sm font-medium text-neutral-800">Langues</h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
                {PREDEFINED_LANGUAGES.map((lang) => {
                  const isSel = languages.includes(lang);
                  return (
                    <button
                      key={lang}
                      type="button"
                      onClick={() => toggleLanguage(lang)}
                      className={`flex items-center justify-between px-3 py-2 rounded-lg border text-sm ${
                        isSel
                          ? "bg-blue-50 border-blue-200 text-blue-700"
                          : "bg-white border-neutral-200 hover:bg-neutral-50"
                      }`}
                    >
                      {lang}
                      {isSel && <span>✓</span>}
                    </button>
                  );
                })}
              </div>
            </section>

            {/* Adresse */}
            <section className="space-y-3">
              <h3 className="text-sm font-medium text-neutral-800">Adresse</h3>

              {/* Utilise ton composant si disponible */}
              <LocationSelector
                country={country}
                city={city}
                address={address}
                onCountryChange={setCountry}
                onCityChange={setCity}
                onAddressChange={setAddress}
                required
              />

              {/* Si tu n'as pas LocationSelector, remplace par ces 3 inputs :
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Pays</label>
                  <input
                    type="text"
                    className="w-full rounded-2xl border border-gray-300 bg-white px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
                    value={country}
                    onChange={(e) => setCountry(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Ville</label>
                  <input
                    type="text"
                    className="w-full rounded-2xl border border-gray-300 bg-white px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
                    value={city}
                    onChange={(e) => setCity(e.target.value)}
                  />
                </div>
                <div className="space-y-2 sm:col-span-2">
                  <label className="block text-sm font-medium">Adresse</label>
                  <input
                    type="text"
                    className="w-full rounded-2xl border border-gray-300 bg-white px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                  />
                </div>
              </div>
              */}
            </section>
          </div>

          {/* Footer sticky */}
          <div className="pt-5 mt-6 border-t border-neutral-200 flex gap-3 justify-end">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 rounded-xl border border-neutral-300 bg-white hover:bg-neutral-50"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={saving}
              className="px-5 py-2 rounded-xl bg-[#0D63F3] text-white font-semibold disabled:opacity-70"
            >
              {saving ? "Enregistrement…" : "Enregistrer"}
            </button>
          </div>
        </form>
      )}
    </Modal>
  );
}
