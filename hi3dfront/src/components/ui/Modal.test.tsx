import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Modal from './Modal';

describe('Modal Component', () => {
  // Sauvegarder l'état initial du overflow
  const originalOverflow = document.body.style.overflow;

  beforeEach(() => {
    // Réinitialiser le style du body avant chaque test
    document.body.style.overflow = '';
  });

  afterAll(() => {
    // Restaurer le style original après tous les tests
    document.body.style.overflow = originalOverflow || '';
  });

  // Test de rendu de base
  test('renders when isOpen is true', () => {
    render(
      <Modal isOpen={true} onClose={() => {}} title="Test Modal">
        Modal content
      </Modal>
    );

    expect(screen.getByText('Test Modal')).toBeInTheDocument();
    expect(screen.getByText('Modal content')).toBeInTheDocument();

    // Vérifier les attributs ARIA
    const dialog = screen.getByRole('dialog');
    expect(dialog).toHaveAttribute('aria-modal', 'true');
    expect(dialog).toHaveAttribute('aria-labelledby', 'modal-title');
  });

  // Test quand isOpen est false
  test('does not render when isOpen is false', () => {
    render(
      <Modal isOpen={false} onClose={() => {}} title="Test Modal">
        Modal content
      </Modal>
    );

    expect(screen.queryByText('Test Modal')).not.toBeInTheDocument();
    expect(screen.queryByText('Modal content')).not.toBeInTheDocument();
  });

  // Test du bouton de fermeture
  test('calls onClose when close button is clicked', () => {
    const handleClose = jest.fn();
    render(
      <Modal isOpen={true} onClose={handleClose} title="Test Modal">
        Modal content
      </Modal>
    );

    const closeButton = screen.getByRole('button', { name: /fermer/i });
    fireEvent.click(closeButton);

    expect(handleClose).toHaveBeenCalledTimes(1);
  });

  // Test de la fermeture en cliquant à l'extérieur
  test('calls onClose when clicking outside the modal', () => {
    const handleClose = jest.fn();
    render(
      <Modal isOpen={true} onClose={handleClose} title="Test Modal" closeOnClickOutside={true}>
        Modal content
      </Modal>
    );

    // Cliquer sur l'arrière-plan (le premier élément avec le rôle dialog)
    const backdrop = screen.getByRole('dialog');
    fireEvent.click(backdrop);

    expect(handleClose).toHaveBeenCalledTimes(1);
  });

  // Test pour ne pas fermer en cliquant à l'extérieur quand closeOnClickOutside est false
  test('does not call onClose when clicking outside if closeOnClickOutside is false', () => {
    const handleClose = jest.fn();
    render(
      <Modal isOpen={true} onClose={handleClose} title="Test Modal" closeOnClickOutside={false}>
        Modal content
      </Modal>
    );

    const backdrop = screen.getByRole('dialog');
    fireEvent.click(backdrop);

    expect(handleClose).not.toHaveBeenCalled();
  });

  // Test avec un pied de page
  test('renders with footer content', () => {
    render(
      <Modal
        isOpen={true}
        onClose={() => {}}
        title="Test Modal"
        footer={<button>Save</button>}
      >
        Modal content
      </Modal>
    );

    expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument();
  });

  // Test des différentes tailles
  test('renders with different sizes', () => {
    const { rerender } = render(
      <Modal isOpen={true} onClose={() => {}} title="Small Modal" size="sm">
        Small modal content
      </Modal>
    );

    expect(screen.getByRole('document')).toHaveClass('max-w-sm');

    rerender(
      <Modal isOpen={true} onClose={() => {}} title="Large Modal" size="lg">
        Large modal content
      </Modal>
    );

    expect(screen.getByRole('document')).toHaveClass('max-w-lg');
  });

  // Test du verrouillage du défilement du body
  test('disables body scroll when open', () => {
    render(
      <Modal isOpen={true} onClose={() => {}} title="Test Modal">
        Modal content
      </Modal>
    );

    expect(document.body.style.overflow).toBe('hidden');
  });

  // Test du rétablissement du défilement du body
  test('restores body scroll when closed', () => {
    const { rerender } = render(
      <Modal isOpen={true} onClose={() => {}} title="Test Modal">
        Modal content
      </Modal>
    );

    expect(document.body.style.overflow).toBe('hidden');

    rerender(
      <Modal isOpen={false} onClose={() => {}} title="Test Modal">
        Modal content
      </Modal>
    );

    expect(document.body.style.overflow).toBe('');
  });

  // Test avec des classes personnalisées
  test('applies custom className', () => {
    render(
      <Modal isOpen={true} onClose={() => {}} title="Test Modal" className="custom-modal">
        Modal content
      </Modal>
    );

    expect(screen.getByRole('document')).toHaveClass('custom-modal');
  });
});
