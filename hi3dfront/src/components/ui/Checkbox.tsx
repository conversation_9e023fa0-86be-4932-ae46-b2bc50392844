import React, { InputHTMLAttributes, forwardRef, ReactNode } from 'react';

export interface CheckboxProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: ReactNode;
  error?: string;
  helperText?: string;
}

const Checkbox = forwardRef<HTMLInputElement, CheckboxProps>(
  ({ label, error, helperText, className = '', ...props }, ref) => {
    return (
      <div className="mb-4">
        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              ref={ref}
              type="checkbox"
              className={`
                h-4 w-4 text-primary-600 border-neutral-300 rounded
                focus:ring-primary-500 focus:outline-none
                disabled:opacity-50 disabled:cursor-not-allowed
                ${error ? 'border-red-500' : ''}
                ${className}
              `}
              {...props}
            />
          </div>

          {label && (
            <div className="ml-3 text-sm">
              <label
                htmlFor={props.id}
                className={`font-medium ${error ? 'text-red-500' : 'text-neutral-700'} ${props.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {label}
              </label>
            </div>
          )}
        </div>

        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}

        {helperText && !error && (
          <p className="mt-1 text-sm text-neutral-500">{helperText}</p>
        )}
      </div>
    );
  }
);

Checkbox.displayName = 'Checkbox';

export default Checkbox;
