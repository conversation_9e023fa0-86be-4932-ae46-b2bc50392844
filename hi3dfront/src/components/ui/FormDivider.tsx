import React from 'react';

interface FormDividerProps {
  text?: string;
  className?: string;
}

const FormDivider: React.FC<FormDividerProps> = ({ 
  text, 
  className = '' 
}) => {
  if (!text) {
    return (
      <div className={`relative my-6 ${className}`}>
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-neutral-300"></div>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`relative my-6 ${className}`}>
      <div className="absolute inset-0 flex items-center">
        <div className="w-full border-t border-neutral-300"></div>
      </div>
      <div className="relative flex justify-center">
        <span className="px-4 bg-white text-sm text-neutral-500">
          {text}
        </span>
      </div>
    </div>
  );
};

export default FormDivider;
