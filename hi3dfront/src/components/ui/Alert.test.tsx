import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Alert from './Alert';

describe('Alert Component', () => {
  // Test de rendu de base
  test('renders correctly with required props', () => {
    render(<Alert type="info">This is an info alert</Alert>);
    
    // Vérifier que le contenu est affiché
    expect(screen.getByText('This is an info alert')).toBeInTheDocument();
    
    // Vérifier que l'alerte a le rôle approprié
    expect(screen.getByRole('alert')).toBeInTheDocument();
  });

  // Test des différents types d'alerte
  test('renders with different types', () => {
    const { rerender } = render(<Alert type="success">Success Alert</Alert>);
    expect(screen.getByRole('alert')).toHaveClass('bg-green-50');
    
    rerender(<Alert type="error">Error <PERSON>ert</Alert>);
    expect(screen.getByRole('alert')).toHaveClass('bg-red-50');
    
    rerender(<Alert type="warning">Warning Alert</Alert>);
    expect(screen.getByRole('alert')).toHaveClass('bg-yellow-50');
    
    rerender(<Alert type="info">Info Alert</Alert>);
    expect(screen.getByRole('alert')).toHaveClass('bg-blue-50');
  });

  // Test avec un titre
  test('renders with a title', () => {
    render(
      <Alert type="info" title="Important Information">
        This is an important message
      </Alert>
    );
    
    expect(screen.getByText('Important Information')).toBeInTheDocument();
    expect(screen.getByText('This is an important message')).toBeInTheDocument();
  });

  // Test du bouton de fermeture
  test('calls onClose when close button is clicked', () => {
    const handleClose = jest.fn();
    render(
      <Alert type="info" onClose={handleClose}>
        Closable alert
      </Alert>
    );
    
    // Vérifier que le bouton de fermeture est présent
    const closeButton = screen.getByRole('button', { name: /fermer/i });
    expect(closeButton).toBeInTheDocument();
    
    // Cliquer sur le bouton de fermeture
    fireEvent.click(closeButton);
    expect(handleClose).toHaveBeenCalledTimes(1);
  });

  // Test sans bouton de fermeture
  test('does not render close button when onClose is not provided', () => {
    render(<Alert type="info">Alert without close button</Alert>);
    
    // Vérifier que le bouton de fermeture n'est pas présent
    const closeButton = screen.queryByRole('button', { name: /fermer/i });
    expect(closeButton).not.toBeInTheDocument();
  });

  // Test avec une icône personnalisée
  test('renders with custom icon', () => {
    render(
      <Alert 
        type="info" 
        icon={<span data-testid="custom-icon">🔔</span>}
      >
        Alert with custom icon
      </Alert>
    );
    
    expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
  });

  // Test des attributs d'accessibilité
  test('has correct accessibility attributes', () => {
    render(<Alert type="error">Error message</Alert>);
    
    // Les alertes d'erreur doivent avoir aria-live="assertive"
    const alert = screen.getByRole('alert');
    expect(alert).toHaveAttribute('aria-live', 'assertive');
    
    // Tester avec un autre type
    const { rerender } = render(<Alert type="info">Info message</Alert>);
    expect(screen.getByRole('alert')).toHaveAttribute('aria-live', 'polite');
  });

  // Test avec des classes personnalisées
  test('applies custom className', () => {
    render(
      <Alert type="info" className="custom-class">
        Alert with custom class
      </Alert>
    );
    
    expect(screen.getByRole('alert')).toHaveClass('custom-class');
  });
});
