import React, { useState } from 'react';
import { API_BASE_URL } from './../config';
import Avatar from './ui/Avatar';
import Modal from './ui/Modal';
import Button from './ui/Button';
import { Search, X } from "lucide-react";
import { useNavigate } from 'react-router-dom';

import MessageContent_mobile_new from './MessageContent_mobile_new';

type MessageSidebarProps = {
  offerDetail?: any;
  setSelectedApplication?: any;
  onSelectProfessional?: (id: number) => void;
};

const MessageSidebar: React.FC<MessageSidebarProps> = ({ offerDetail, setSelectedApplication, onSelectProfessional }) => {
  type ApplicationStatus = "all" | "invited" | "accepted";

  const [activeFilter, setActiveFilter] = useState<ApplicationStatus>("all");
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [professionals, setProfessionals] = useState<any[]>([]);
  const [loadingProfessionals, setLoadingProfessionals] = useState(false);
  const [selectedProfessionalId, setSelectedProfessionalId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [inviteError, setInviteError] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [inviting, setInviting] = useState(false);
  const [selectedMobileApplication, setSelectedMobileApplication] = useState<any>(null);
  const navigate = useNavigate();

  const [showProfessionalInfo, setShowProfessionalInfo] = useState(false);

  const token = localStorage.getItem('token');
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const isClient = currentUser.role === 'client' || !currentUser.is_professional;

  // Données statiques des professionnels (fallback)
  const staticProfessionals = [
    {
      id: 1,
      user_id: 1,
      first_name: 'Jack and Moris',
      last_name: 'Render',
      avatar: 'https://randomuser.me/api/portraits/men/41.jpg',
      title: 'Architecture visualisation',
      specialty: '3D Artist'
    },
    {
      id: 2,
      user_id: 2,
      first_name: 'The 3d',
      last_name: 'Boss',
      avatar: 'https://randomuser.me/api/portraits/men/22.jpg',
      title: 'Architecture visualisation',
      specialty: '3D Designer'
    },
    {
      id: 3,
      user_id: 3,
      first_name: 'Sofie',
      last_name: 'Render',
      avatar: 'https://randomuser.me/api/portraits/women/45.jpg',
      title: 'Architecture visualisation',
      specialty: '3D Animator'
    },
    {
      id: 4,
      user_id: 4,
      first_name: 'Maris 3d',
      last_name: 'magik',
      avatar: 'https://randomuser.me/api/portraits/women/32.jpg',
      title: 'Architecture visualisation',
      specialty: '3D Modeler'
    },
    {
      id: 5,
      user_id: 5,
      first_name: 'Alex',
      last_name: 'Design',
      avatar: 'https://randomuser.me/api/portraits/men/23.jpg',
      title: 'Game Development',
      specialty: 'UI/UX Designer'
    }
  ];

  // Données statiques pour les applications (fallback)
  const staticApplications = [
    {
      id: 1,
      created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      status: "invited",
      proposal: "Je suis intéressé par votre projet de création d'environnement 3D pour jeu mobile.",
      freelance_profile: {
        user_id: 1,
        first_name: "Jean",
        last_name: "Dupont",
        avatar: "https://randomuser.me/api/portraits/men/41.jpg"
      }
    },
    {
      id: 2,
      created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      status: "accepted",
      proposal: "J'ai une grande expérience dans la modélisation 3D pour jeux mobiles.",
      freelance_profile: {
        user_id: 2,
        first_name: "Marie",
        last_name: "Martin",
        avatar: "https://randomuser.me/api/portraits/women/22.jpg"
      }
    }
  ];

  const handleOpenInviteModal = async () => {
    console.log("Ouverture de la modal d'invitation");
    setInviteError(null);
    setSelectedProfessionalId(null);
    setSearchQuery('');
    setShowInviteModal(true);
    await fetchProfessionals();
  };

  const handleCloseInviteModal = () => {
    if (inviting) return; // éviter la fermeture pendant l'envoi
    setShowInviteModal(false);
  };

  const fetchProfessionals = async () => {
    if (!token) return;

    setLoadingProfessionals(true);
    try {
      const response = await fetch(`${API_BASE_URL}/api/professionals`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des professionnels');
      }

      const data = await response.json();
      console.log('Professionnels récupérés:', data);

      let professionalsList = [];
      if (data.users) {
        professionalsList = data.users;
      } else if (data.professionals) {
        professionalsList = data.professionals;
      } else if (Array.isArray(data)) {
        professionalsList = data;
      } else {
        professionalsList = [];
      }

      const normalizedProfessionals = professionalsList.map((pro: any) => ({
        ...pro,
        user_id: pro.user_id || pro.id,
      }));
      setProfessionals(normalizedProfessionals);
    } catch (err) {
      console.error('Erreur:', err);
      setInviteError('Impossible de récupérer la liste des professionnels');
      setProfessionals(staticProfessionals);
    } finally {
      setLoadingProfessionals(false);
    }
  };

  const filteredProfessionals = professionals.filter(pro => {
    const fullName = `${pro.first_name || ''} ${pro.last_name || ''}`.toLowerCase();
    return fullName.includes(searchQuery.toLowerCase());
  });

  const handleInviteProfessional = async () => {
    if (!token || !offerDetail.id || !selectedProfessionalId) {
      setInviteError('Veuillez sélectionner un professionnel');
      return;
    }

    try {
      setInviting(true);
      setInviteError(null);
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${offerDetail.id}/invite`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ professional_id: selectedProfessionalId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erreur lors de l\'invitation du professionnel');
      }

      const data = await response.json();
      console.log('Réponse de l\'invitation:', data);

      setShowInviteModal(false);
      setSuccessMessage('Invitation envoyée avec succès au professionnel');
      window.location.reload();
    } catch (err) {
      console.error('Erreur:', err);
      setInviteError(err instanceof Error ? err.message : 'Erreur lors de l\'invitation du professionnel');
    } finally {
      setInviting(false);
    }
  };

  const statusLabels: Record<ApplicationStatus, string> = {
    all: "All",
    invited: "Invited",
    accepted: "Approved",
  };

  const getUrlProlfil = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  function formatRelativeDate(dateString: string) {
    const date = new Date(dateString);
    const diffMs = Date.now() - date.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);
    if (diffMinutes < 1) return "À l'instant";
    if (diffMinutes < 60) return `Il y a ${diffMinutes} minute${diffMinutes > 1 ? "s" : ""}`;
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `Il y a ${diffHours} heure${diffHours > 1 ? "s" : ""}`;
    const diffDays = Math.floor(diffHours / 24);
    return `Il y a ${diffDays} jour${diffDays > 1 ? "s" : ""}`;
  }

  function truncateText(text: string, maxLength: number) {
    return text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
  }

  const getFilteredApplications = () => {
    if (!offerDetail?.applications) return staticApplications;
    if (activeFilter === "all") return offerDetail?.applications;
    return offerDetail?.applications.filter((app: any) => app?.status === activeFilter);
  };

  const countByStatus = (status: any) => {
    if (!offerDetail?.applications) return staticApplications.length;
    if (status === "all") return offerDetail.applications.length;
    return offerDetail?.applications.filter((app: any) => app?.status === status).length;
  };

  const filteredApplications = getFilteredApplications();

  return (
    <>
      <div className="p-0 m-0 ml-0 pl-0 h-full flex flex-col" style={{ height: '100%' }}>
        {successMessage && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4">
            {successMessage}
          </div>
        )}

        <div className="flex gap-4 mb-6 border-b pb-3 border-black-200 ml-0 pl-0">
          {Object.entries(statusLabels).map(([statusKey, label]) => {
            const key = statusKey as ApplicationStatus;
            return (
              <button
                key={key}
                className={`py-2 px-4 text-sm font-semibold ml-0 pl-0 ${activeFilter === key ? "text-black font-bold border-b-2 border-black" : "text-gray-400"
                  }`}
                onClick={() => setActiveFilter(key)}
                style={{
                  fontSize: "14px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
                }}
              >
                {label} ({countByStatus(key)})
              </button>
            );
          })}
        </div>

        <div className="flex-1 overflow-y-auto pr-2" style={{ minHeight: 'calc(100% - 320px)' }}>
          {filteredApplications.length > 0 ? (
            <div className="mb-6 ml-0 pl-0">
              <div className="space-y-1 ml-0 pl-0">
                {filteredApplications.map((application: any) => {
                  const freelance = application.freelance_profile;
                  return (
                    <div
                      key={application.id}
                      className={`${selectedProfessionalId === freelance.user_id ? "bg-primary-50 border border-primary-200 shadow-sm" : "bg-[#F5F5F5] hover:bg-gray-200"} p-4 transition-colors duration-200 md:cursor-pointer`}
                      style={{
                        borderRadius: "18px",
                      }}
                      onClick={() => {
                        // Ne s'exécute que sur desktop (écrans >= 768px)
                        if (window.innerWidth >= 768) {
                          setSelectedApplication && setSelectedApplication(application);
                          onSelectProfessional && onSelectProfessional(freelance.user_id);
                        }
                      }}
                    >
                      <div className="flex items-center">
                        <img
                          src={getUrlProlfil(freelance.avatar)}
                          alt={`${freelance.first_name} ${freelance.last_name}`}
                          className="w-12 h-12 rounded-full object-cover mr-4"
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <span className="text-[#1a1a1a]"
                              style={{
                                fontSize: "13px",
                                fontFamily: "'Inter', sans-serif",
                                fontWeight: 700,
                              }}
                            >
                              {freelance.first_name} {freelance.last_name}
                            </span>
                          </div>
                          <div className="text-gray-600 mt-1"
                            style={{
                              fontSize: "11px",
                              fontFamily: "'Inter', sans-serif",
                              fontWeight: 600,
                            }}
                          >
                            {freelance?.title?.length > 30
                              ? freelance?.title?.slice(0, 30) + "..."
                              : freelance?.title}
                          </div>
                          {freelance?.skills && freelance?.skills.length > 0 && (
                            <div
                              className="text-xs text-gray-500 mt-1"
                              style={{
                                fontSize: "11px",
                                fontFamily: "'Inter', sans-serif",
                                fontWeight: 600,
                              }}
                            >
                              {freelance?.skills.join(", ").length > 30
                                ? freelance?.skills.join(", ").slice(0, 30) + "..."
                                : freelance?.skills.join(", ")}
                            </div>
                          )}
                        </div>
                        <button
                          className="h-[40px] w-[82px] bg-white py-2 rounded-full flex items-center justify-center hover:bg-gray-50 border border-gray-200 md:hidden hover:text-gray-300 transition-colors"
                          style={{
                            fontFamily: "'Inter', sans-serif",
                            fontWeight: "400",
                            fontSize: "13px",
                            color: "#ffffff",
                            background: "#000000"
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedMobileApplication(application);
                          }}
                        >
                          Open
                        </button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

          ) : (
            <div className="text-center text-gray-400 text-sm py-12 ml-0 pl-0">
              <div className="w-24 h-24 mx-auto mb-4 flex items-center justify-center bg-gray-100 rounded-full text-4xl opacity-50">
                📭
              </div>
              <p>Aucune candidature trouvée pour le filtre <strong>{statusLabels[activeFilter]}</strong></p>
              <p className="text-xs mt-2">Essayez un autre filtre ou invitez de nouveaux artistes !</p>
            </div>
          )}
        </div>

        <button
          className="w-full py-3 px-4 rounded-full bg-white border border-gray-300 font-semibold cursor-pointer ml-0 pl-0 mt-4"
          onClick={handleOpenInviteModal}
        >
          Invite a new artiste
        </button>
      </div>

      {/* Modal pour l'affichage mobile */}
      {selectedMobileApplication && (
        <div className="fixed inset-0 bg-white z-50 md:hidden flex flex-col">
          {/* En-tête avec informations du professionnel */}
          <div className="bg-[#dadada] p-4 rounded-[20px]">
            <div className="flex items-center">
              {/* SVG avant la photo de profil */}
              <button
                onClick={() => setSelectedMobileApplication(null)}
                className="mr-3"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="26"
                  height="26"
                  viewBox="0 0 11 11"
                  fill="none"
                >
                  <path
                    d="M6.62412 2.59863L3.87939 5.34336L6.62412 8.08808"
                    stroke="black"
                    strokeWidth="0.914908"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  ></path>
                </svg>
              </button>

              <img
                src={getUrlProlfil(selectedMobileApplication.freelance_profile.avatar)}
                alt={`${selectedMobileApplication.freelance_profile.first_name} ${selectedMobileApplication.freelance_profile.last_name}`}
                className="w-12 h-12 rounded-full object-cover mr-4"
              />
              <div className="flex-1">
                <h3
                  className="text-lg font-medium"
                  style={{ fontFamily: "'Inter', sans-serif", fontSize: '16px', color: '#000000' }}
                >
                  {selectedMobileApplication.freelance_profile.first_name} {selectedMobileApplication.freelance_profile.last_name}
                </h3>
                <p className="text-gray-600 text-sm">{selectedMobileApplication.freelance_profile.title}</p>
              </div>
              {/* Bouton pour afficher/masquer les informations détaillées */}
              <button
                className="text-xl font-bold"
                onClick={() => setShowProfessionalInfo(!showProfessionalInfo)}
              >
                ...
              </button>
            </div>
          </div>

          {/* Nouveau bloc d'informations professionnelles (caché par défaut) */}
          {showProfessionalInfo && (
            <div className="space-y-4 p-4 bg-[#dadada] rounded-[20px]" style={{ marginTop: "4px" }}>
              <div
                onClick={() => navigate(`/professionals/${selectedMobileApplication?.freelance_profile?.user_id}`)}
                className="flex items-center"
              >
                <img
                  src={getUrlProlfil(selectedMobileApplication.freelance_profile.avatar)}
                  alt={`${selectedMobileApplication.freelance_profile.first_name} ${selectedMobileApplication.freelance_profile.last_name}`}
                  className="w-28 h-28 rounded-full object-cover mr-3"
                  onError={(e) => {
                    e.currentTarget.onerror = null;
                    e.currentTarget.src = 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
                  }}
                />
                <div>
                  <p className="text-xs mt-10"
                    style={{
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: "300",
                      color: "#000000"
                    }}
                  >Visite portfolio</p>
                  <h4 className="text-sm font-semibold"
                    style={{
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: "400",
                      fontSize: "16px",
                      color: "#000000"
                    }}
                  >{selectedMobileApplication.freelance_profile.first_name} {selectedMobileApplication.freelance_profile.last_name}</h4>
                  <p
                    style={{
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: "400",
                      fontSize: "16px",
                      color: "#000000"
                    }}
                  >{selectedMobileApplication.freelance_profile.title || 'Architecture visualisation'}</p>
                </div>
              </div>

              <div className="flex items-center text-sm text-gray-600">
                <span className="mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="ionicon" viewBox="0 0 512 512" width="16" height="20">
                    <circle cx="256" cy="192" r="32"></circle>
                    <path d="M256 32c-88.22 0-160 68.65-160 153 0 40.17 18.31 93.59 54.42 158.78 29 52.34 62.55 99.67 80 123.22a31.75 31.75 0 0051.22 0c17.42-23.55 51-70.88 80-123.22C397.69 278.61 416 225.19 416 185c0-84.35-71.78-153-160-153zm0 224a64 64 0 1164-64 64.07 64.07 0 01-64 64z"></path>
                  </svg>
                </span>
                <span
                  style={{
                    fontFamily: "'Inter', sans-serif",
                    fontWeight: "400",
                    fontSize: "14px",
                    color: "#000000"
                  }}
                >{selectedMobileApplication.freelance_profile.city}, {selectedMobileApplication.freelance_profile.country}</span>
              </div>

              <div className="flex items-center">
                <span
                  className="bg-[#000000] text-white rounded-full px-1.5 py-0.5 inline-block mr-3"
                  style={{
                    fontSize: "8px",
                    fontFamily: "'Inter', sans-serif",
                    fontWeight: 300,
                  }}
                >
                  PRO
                </span>
                <span
                  style={{
                    fontFamily: "'Inter', sans-serif",
                    fontWeight: "400",
                    fontSize: "14px",
                    color: "#000000"
                  }}
                >Pro acount</span>
              </div>

              {/* Section Langues avec SVG flottant à droite */}
              <div className="flex items-center justify-between">
                <div className="flex items-center text-sm">
                  <span className="mr-2 text-xl text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" className="ionicon" viewBox="0 0 512 512" width="16" height="20">
                      <path fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="32" d="M48 112h288M192 64v48M272 448l96-224 96 224M301.5 384h133M281.3 112S257 206 199 277 80 384 80 384"></path>
                      <path d="M256 336s-35-27-72-75-56-85-56-85" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="32"></path>
                    </svg>
                  </span>
                  <ul className="flex space-x-2 list-none uppercase">
                    {selectedMobileApplication.freelance_profile.languages?.map((language: string, index: number) => (
                      <li key={index}
                        style={{
                          fontFamily: "'Inter', sans-serif",
                          fontWeight: "400",
                          fontSize: "14px",
                          color: "#000000"
                        }}
                      >{language}</li>
                    ))}
                  </ul>
                </div>

                {/* SVG flottant à droite pour masquer les informations */}
                <button
                  onClick={() => setShowProfessionalInfo(false)}
                  className="ml-2"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="29"
                    height="29"
                    viewBox="0 0 29 29"
                    fill="none"
                  >
                    <g clipPath="url(#clip0_201_99288)">
                      <path
                        d="M4.40454 17.6777C4.20532 17.8652 4.08813 18.1347 4.08813 18.4394C4.08813 19.0488 4.54517 19.5058 5.15454 19.5058C5.45922 19.5058 5.74047 19.4004 5.91625 19.2011L14.8928 10.0371H13.6389L22.6154 19.2011C22.8029 19.4004 23.0842 19.5058 23.3772 19.5058C23.9865 19.5058 24.4436 19.0488 24.4436 18.4394C24.4436 18.1347 24.3264 17.8652 24.1272 17.6777L15.0569 8.39648C14.8576 8.17383 14.5647 8.04492 14.2717 8.04492C13.967 8.04492 13.6858 8.17383 13.4749 8.39648L4.40454 17.6777Z"
                        fill="black"
                        fillOpacity="0.85"
                      ></path>
                    </g>
                    <defs>
                      <clipPath id="clip0_201_99288">
                        <rect width="20.3555" height="12.4102" fill="white" transform="translate(4.08813 8.04492)"></rect>
                      </clipPath>
                    </defs>
                  </svg>
                </button>
              </div>
            </div>
          )}

          {/* Bouton Approve the offer */}
          <div className="px-4 border-b border-gray-200" style={{ marginTop: "4px" }}>
            <button
              className="h-[40px] w-full bg-black py-2 rounded-full flex items-center justify-center hover:bg-gray-900 text-white"
              style={{ fontFamily: "'Inter', sans-serif", fontWeight: '400', fontSize: '14px' }}
            >
              Approve the offer
            </button>
          </div>

          {/* Intégration du composant MessageContent mobile */}
          <div className="flex-1 w-full">
            <MessageContent_mobile_new
              offerId={offerDetail?.id}
              offerTitle={offerDetail?.title}
              clientId={offerDetail?.user?.id}
              clientName={`${offerDetail?.user?.first_name || ''} ${offerDetail?.user?.last_name || ''}`.trim() || "Client"}
              clientAvatar={offerDetail?.user?.avatar}
              professionalId={selectedMobileApplication?.freelance_profile?.user_id}
              professionalName={`${selectedMobileApplication?.freelance_profile?.first_name || ''} ${selectedMobileApplication?.freelance_profile?.last_name || ''}`.trim() || "Professional"}
              professionalAvatar={selectedMobileApplication?.freelance_profile?.avatar}
              isClient={true}
              selectedFiles={[]}
              setSelectedFiles={() => { }}
            />
          </div>
        </div>
      )}

      <Modal
        isOpen={showInviteModal}
        onClose={handleCloseInviteModal}
        title="Invite a professional"
      >
        <div className="p-4">
          {inviteError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
              {inviteError}
            </div>
          )}

          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search a professional..."
                className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                disabled={inviting}
              />
            </div>
          </div>

          <div className="max-h-60 overflow-y-auto mb-4">
            {loadingProfessionals ? (
              <div className="flex justify-center items-center h-20">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary-600"></div>
              </div>
            ) : filteredProfessionals.length === 0 ? (
              <div className="text-center py-4 text-neutral-500">
                No professional found.
              </div>
            ) : (
              <div className="space-y-2">
                {filteredProfessionals.map((professional) => (
                  <div
                    key={professional.user_id}
                    className={`flex items-center p-3 rounded-lg cursor-pointer ${selectedProfessionalId === professional.user_id
                      ? "bg-primary-50 border border-primary-200"
                      : "hover:bg-neutral-50 border border-transparent"
                      }`}
                    onClick={() => !inviting && setSelectedProfessionalId(professional.user_id)}
                  >
                    <Avatar
                      src={getUrlProlfil(professional.avatar)}
                      fallback={
                        (professional.first_name?.[0] || "") + (professional.last_name?.[0] || "")
                      }
                      size="md"
                      className="mr-3"
                    />
                    <div>
                      <h4 className="font-medium text-neutral-900">
                        {professional.first_name} {professional.last_name}
                      </h4>
                      {professional.title && (
                        <p className="text-sm text-neutral-500">{professional.title}</p>
                      )}
                    </div>

                  </div>
                ))}

              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={handleCloseInviteModal} disabled={inviting}>
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleInviteProfessional}
              disabled={!selectedProfessionalId || loadingProfessionals || inviting}
            >
              {inviting ? 'Sending...' : 'Invite'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default MessageSidebar;