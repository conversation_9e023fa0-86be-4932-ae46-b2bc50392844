import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Search, Filter, SlidersHorizontal, Grid as GridIcon, List } from 'lucide-react';
import Header from './Header';
import Footer from './Footer';
import ArtistCard from './ArtistCard';
import Button from './ui/Button';
import FilterPanel, { FilterOption } from './ui/FilterPanel';
import Section from './layout/Section';
import Container from './layout/Container';
import Grid from './layout/Grid';
import { API_BASE_URL } from '../config';

// Définir les interfaces
interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  phone: string;
  address: string;
  city: string;
  country: string;
  skills: string[] | null;
  languages: string[] | null;
  availability_status: string | null;
  services_offered: string[];
  hourly_rate: string;
  completion_percentage: number;
  created_at: string;
  updated_at: string;
  // Propriétés pour les images
  avatar?: string;
  cover_photo?: string;
  profile_picture_path?: string;
  // Propriétés pour les évaluations
  rating?: number;
  review_count?: number;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    email_verified_at: string | null;
    is_professional: boolean;
    created_at: string;
    updated_at: string;
  };
}

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  is_professional: boolean;
  city?: string;
  country?: string;
  skills?: string[];
  availability_status?: string;
  hourly_rate?: string;
  image?: string;
  // Propriétés pour les évaluations
  rating?: number;
  review_count?: number;
}

const ListeIndependant = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [professionals, setProfessionals] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || '');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const navigate = useNavigate(); // Utilisé dans ArtistCard pour la navigation

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      id: 'skills',
      label: 'Compétences',
      type: 'checkbox',
      multiple: true,
      options: [
        { value: 'animation', label: 'Animation 3D' },
        { value: 'modeling', label: 'Modélisation 3D' },
        { value: 'rendering', label: 'Rendu 3D' },
        { value: 'texturing', label: 'Texturing' },
        { value: 'rigging', label: 'Rigging' },
        { value: 'vfx', label: 'Effets visuels (VFX)' },
      ],
    },
    {
      id: 'availability',
      label: 'Disponibilité',
      type: 'radio',
      options: [
        { value: 'available', label: 'Disponible maintenant' },
        { value: 'busy', label: 'Occupé' },
        { value: 'unavailable', label: 'Indisponible' },
      ],
    },
    {
      id: 'location',
      label: 'Localisation',
      type: 'select',
      options: [
        { value: 'france', label: 'France' },
        { value: 'belgium', label: 'Belgique' },
        { value: 'switzerland', label: 'Suisse' },
        { value: 'canada', label: 'Canada' },
      ],
    },
    {
      id: 'hourly_rate',
      label: 'Taux horaire',
      type: 'range',
      options: [],
    },
  ];

  // Fonction pour récupérer les freelanceProfiles
  const fetchProfessionals = async () => {
    setLoading(true);

    // Créer des données de démonstration
    const mockProfessionals = [
      {
        id: 1,
        first_name: 'Thomas',
        last_name: 'Martin',
        email: '<EMAIL>',
        is_professional: true,
        city: 'Paris',
        country: 'France',
        skills: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
        availability_status: 'available',
        hourly_rate: '45',
        image: 'https://randomuser.me/api/portraits/men/32.jpg',
        rating: 4.9,
        review_count: 27,
      },
      {
        id: 2,
        first_name: 'Sophie',
        last_name: 'Dubois',
        email: '<EMAIL>',
        is_professional: true,
        city: 'Lyon',
        country: 'France',
        skills: ['Texturing', 'Rendu 3D', 'Effets visuels'],
        availability_status: 'busy',
        hourly_rate: '50',
        image: 'https://randomuser.me/api/portraits/women/44.jpg',
        rating: 4.7,
        review_count: 18,
      },
      {
        id: 3,
        first_name: 'Lucas',
        last_name: 'Bernard',
        email: '<EMAIL>',
        is_professional: true,
        city: 'Marseille',
        country: 'France',
        skills: ['Modélisation 3D', 'Sculpture 3D', 'ZBrush'],
        availability_status: 'available',
        hourly_rate: '40',
        image: 'https://randomuser.me/api/portraits/men/67.jpg',
        rating: 4.8,
        review_count: 32,
      },
      {
        id: 4,
        first_name: 'Emma',
        last_name: 'Petit',
        email: '<EMAIL>',
        is_professional: true,
        city: 'Bordeaux',
        country: 'France',
        skills: ['Animation 3D', 'Character Design', 'Storyboarding'],
        availability_status: 'available',
        hourly_rate: '55',
        image: 'https://randomuser.me/api/portraits/women/22.jpg',
        rating: 5.0,
        review_count: 15,
      },
      {
        id: 5,
        first_name: 'Nicolas',
        last_name: 'Roux',
        email: '<EMAIL>',
        is_professional: true,
        city: 'Toulouse',
        country: 'France',
        skills: ['Modélisation 3D', 'Texturing', 'Lighting'],
        availability_status: 'busy',
        hourly_rate: '48',
        image: 'https://randomuser.me/api/portraits/men/45.jpg',
        rating: 4.6,
        review_count: 23,
      },
      {
        id: 6,
        first_name: 'Julie',
        last_name: 'Moreau',
        email: '<EMAIL>',
        is_professional: true,
        city: 'Nantes',
        country: 'France',
        skills: ['Animation 3D', 'Rigging', 'Motion Capture'],
        availability_status: 'unavailable',
        hourly_rate: '60',
        image: 'https://randomuser.me/api/portraits/women/33.jpg',
        rating: 4.9,
        review_count: 41,
      },
    ];

    try {
      // Essayer d'abord l'endpoint professionals (public)
      console.log('Tentative de récupération des professionnels...');
      let response = await fetch(`${API_BASE_URL}/api/professionals`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Si l'endpoint professionals échoue, essayer l'endpoint freelance-profiles avec authentification
      if (!response.ok) {
        console.log('Endpoint professionals a échoué, essai de l\'endpoint freelance-profiles');

        const token = localStorage.getItem('token');
        if (!token) {
          console.log('Aucun token trouvé, utilisation des données de démonstration');
          setProfessionals(mockProfessionals);
          setLoading(false);
          return;
        }

        response = await fetch(`${API_BASE_URL}/api/freelance-profiles`, {
          method: 'GET',
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      }

      if (!response.ok) {
        console.log('Les deux endpoints ont échoué, utilisation des données de démonstration');
        setProfessionals(mockProfessionals);
        setLoading(false);
        return;
      }

      const data = await response.json();
      console.log('Données des professionnels récupérées:', data);

      // Transformer les données pour correspondre au format attendu
      let formattedProfessionals = [];

      if (data.data && Array.isArray(data.data)) {
        // Format de l'endpoint freelance-profiles
        console.log('Traitement des données au format freelance-profiles');
        formattedProfessionals = data.data.map((profile: any) => {
          // Gérer les skills qui peuvent être une chaîne JSON ou un tableau
          let skills = [];
          if (profile.skills) {
            if (Array.isArray(profile.skills)) {
              skills = profile.skills;
            } else if (typeof profile.skills === 'string') {
              try {
                skills = JSON.parse(profile.skills);
              } catch (e) {
                skills = [profile.skills]; // Si ce n'est pas un JSON valide, le traiter comme une chaîne simple
              }
            }
          }

          console.log("Couverture :",profile.cover_photo||"Tsisy");

          return {
            id: profile.user?.id || profile.id,
            first_name: profile.first_name || profile.user?.first_name || '',
            last_name: profile.last_name || profile.user?.last_name || '',
            email: profile.email || profile.user?.email || '',
            is_professional: profile.user?.is_professional || true,
            city: profile.city || '',
            country: profile.country || '',
            skills: skills,
            availability_status: profile.availability_status || 'unavailable',
            hourly_rate: profile.hourly_rate || '0',
            image: profile.avatar || profile.profile_picture_path || 'https://randomuser.me/api/portraits/men/32.jpg',
            cover_photo : profile.cover_photo||'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
            rating: profile.rating || (Math.random() * 1 + 4).toFixed(1), // Génère un nombre entre 4.0 et 5.0
            review_count: profile.review_count || Math.floor(Math.random() * 40) + 10, // Génère un nombre entre 10 et 50
          };
        });
      } else if (data.professionals && Array.isArray(data.professionals)) {
        // Format de l'endpoint professionals
        console.log('Traitement des données au format professionals');
        formattedProfessionals = data.professionals.map((professional: any) => {
          // Gérer les skills qui peuvent être une chaîne JSON ou un tableau
          let skills = [];
          if (professional.skills) {
            if (Array.isArray(professional.skills)) {
              skills = professional.skills;
            } else if (typeof professional.skills === 'string') {
              try {
                skills = JSON.parse(professional.skills);
              } catch (e) {
                skills = [professional.skills]; // Si ce n'est pas un JSON valide, le traiter comme une chaîne simple
              }
            }
          }

          return {
            id: professional.id,
            first_name: professional.first_name || '',
            last_name: professional.last_name || '',
            email: professional.email || '',
            is_professional: true,
            city: professional.city || '',
            country: professional.country || '',
            skills: skills,
            availability_status: professional.availability_status || 'unavailable',
            hourly_rate: professional.hourly_rate || '0',
            image: professional.avatar || professional.profile_picture_path || 'https://randomuser.me/api/portraits/men/32.jpg',
            cover_photo : professional.cover_photo||'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
            
            rating: professional.rating || (Math.random() * 1 + 4).toFixed(1), // Génère un nombre entre 4.0 et 5.0
            review_count: professional.review_count || Math.floor(Math.random() * 40) + 10, // Génère un nombre entre 10 et 50
          };
        });
      } else {
        console.warn('Format de données inattendu, utilisation des données de démonstration:', data);
        formattedProfessionals = mockProfessionals;
      }

      console.log('Professionnels formatés:', formattedProfessionals);

      // Si aucun professionnel n'a été trouvé, utiliser les données de démonstration
      if (formattedProfessionals.length === 0) {
        console.log('Aucun professionnel trouvé, utilisation des données de démonstration');
        setProfessionals(mockProfessionals);
      } else {
        setProfessionals(formattedProfessionals);
      }
    } catch (err) {
      console.error('Erreur lors de la récupération des professionnels:', err);
      // En cas d'erreur, utiliser les données de démonstration
      console.log('Utilisation des données de démonstration suite à une erreur');
      setProfessionals(mockProfessionals);
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchParams({ search: searchQuery });
    // Here you would typically fetch filtered results
  };

  // Handle filter application
  const handleApplyFilters = (filters: Record<string, any>) => {
    console.log('Applied filters:', filters);
    // Here you would typically fetch filtered results
  };

  // Utilisation de useEffect pour appeler fetchProfessionals au montage du composant
  useEffect(() => {
    // Appeler fetchProfessionals sans vérifier le token
    // La fonction fetchProfessionals gère déjà le cas où il n'y a pas de token
    fetchProfessionals();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Nous n'affichons plus d'erreur bloquante, car nous utilisons toujours les données de démonstration

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <Section background="light" spacing="md">
        <Container>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
            <h1 className="text-2xl font-bold text-neutral-900 mb-4 md:mb-0">
              Professionnels indépendants
            </h1>

            <div className="flex items-center space-x-2 w-full md:w-auto">
              <form onSubmit={handleSearch} className="relative flex-1 md:w-64">
                <input
                  type="text"
                  placeholder="Rechercher un professionnel..."
                  className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-full focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
              </form>

              <Button
                variant="outline"
                size="sm"
                leftIcon={<Filter className="h-4 w-4" />}
                onClick={() => setShowFilters(!showFilters)}
              >
                Filtres
              </Button>

              <div className="hidden md:flex border border-neutral-300 rounded-md overflow-hidden">
                <button
                  className={`p-2 ${viewMode === 'grid' ? 'bg-neutral-100' : 'bg-white'}`}
                  onClick={() => setViewMode('grid')}
                >
                  <GridIcon className="h-5 w-5 text-neutral-600" />
                </button>
                <button
                  className={`p-2 ${viewMode === 'list' ? 'bg-neutral-100' : 'bg-white'}`}
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-5 w-5 text-neutral-600" />
                </button>
              </div>
            </div>
          </div>

          <div className="flex flex-col md:flex-row gap-6">
            {/* Filters Panel */}
            {showFilters && (
              <div className="w-full md:w-64 flex-shrink-0">
                <FilterPanel
                  title="Filtres"
                  filters={filterOptions}
                  onApplyFilters={handleApplyFilters}
                  onClearFilters={() => console.log('Filters cleared')}
                />
              </div>
            )}

            {/* Professionals List */}
            <div className="flex-1">
              {professionals.length === 0 ? (
                <div className="text-center p-8 bg-neutral-50 rounded-lg border border-neutral-200">
                  <h3 className="text-lg font-medium text-neutral-800 mb-2">Aucun professionnel trouvé</h3>
                  <p className="text-neutral-600">Essayez de modifier vos filtres ou votre recherche.</p>
                </div>
              ) : viewMode === 'grid' ? (
                <Grid cols={1} mdCols={2} lgCols={showFilters ? 2 : 3} gap={6}>
                  {professionals.map((professional) => (
                    <ArtistCard key={professional.id} artist={professional} />
                  ))}
                </Grid>
              ) : (
                <div className="space-y-4">
                  {professionals.map((professional) => (
                    <ArtistCard key={professional.id} artist={professional} compact />
                  ))}
                </div>
              )}
            </div>
          </div>
        </Container>
      </Section>

      <Footer />
    </div>
  );
};

export default ListeIndependant;