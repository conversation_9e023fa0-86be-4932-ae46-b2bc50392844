import React from 'react';
import Badge from './ui/Badge';


type BriefProps = {
  offer?: any;
};

const Brief:React.FC<BriefProps>  = ({offer})=> {
  // Formater la date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending':
                return <span className="px-3 py-1 rounded-full text-white bg-yellow-500">En attente</span>;
            case 'accepted':
                return <span className="px-3 py-1 rounded-full text-white bg-green-500">Accepté</span>;
            case 'rejected':
                return <span className="px-3 py-1 rounded-full text-white bg-red-500">Refusé</span>;
            case 'invited':
                return <span className="px-3 py-1 rounded-full text-white bg-blue-500">Invité</span>;
            default:
                return <span className="px-3 py-1 rounded-full text-white bg-gray-500">Inconnu</span>;
        }
    };

  // Calculer les jours restants
  const getDaysRemaining = (dateString: string) => {
    const deadline = new Date(dateString);
    const now = new Date();
    const diffTime = deadline.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return 'Délai dépassé';
    } else if (diffDays === 0) {
      return 'Dernier jour';
    } else {
      return `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`;
    }
  };

  return (
    <div style={{ background: 'rgb(250 245 255 / var(--tw-bg-opacity, 1))', borderRadius: 16, border: '1px solid #eee', padding: 32, marginBottom: 124, boxShadow: '0 2px 8px rgba(60,30,90,0.03)' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 16 }}>
        <div className="flex items-center mb-0">
          <span className="flex items-center justify-center mr-3" style={{ width: 36, height: 36, borderRadius: '50%', background: '#a259cf1a' }}>
            <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" stroke="#a259cf" strokeWidth="2" fill="none"/><path d="M8 10h8M8 14h5" stroke="#a259cf" strokeWidth="2" strokeLinecap="round"/></svg>
          </span>
          <span className="font-bold text-lg text-[#222] mr-2">{offer.title || 'Project Request'}</span>
        </div>
        <div style={{ fontSize: 15, color: '#222', fontWeight: 500, marginLeft: 12, minWidth: 160, textAlign: 'right' }}>
          <span style={{ fontSize: 13, color: '#888', fontWeight: 400 }}>Date : </span>{formatDate(offer.created_at)}
        </div>
      </div>
      <div style={{background: '#fff', borderRadius: 12, padding: 28}}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: 24 }}>
          <div style={{ fontWeight: 600, fontSize: 15, color: '#222' }}>Statut du projet</div>
          <Badge
            color={
              offer.status === 'pending' ? 'warning' :
              offer.status === 'open' ? 'info' :
              offer.status === 'in_progress' ? 'success' :
              offer.status === 'completed' ? 'success' :
              'neutral'
            }
          >
            {offer.status === 'pending' ? 'En attente' :
             offer.status === 'open' ? 'Ouvert' :
             offer.status === 'in_progress' ? 'En cours' :
             offer.status === 'completed' ? 'Terminé' :
             offer.status === 'closed' ? 'Clôturé' :
             offer.status === 'invited' ? 'Invitation' :
             'Inconnu'}
          </Badge>
        </div>
        <div className="mb-6">
          <div className="font-semibold mb-2" style={{ fontWeight: 600, marginBottom: 8 }}>Description</div>
          <div className="text-[13px] text-[#222]" style={{ fontSize: 13, color: '#222' }}
            dangerouslySetInnerHTML={{ __html: offer.description }}
          />
        </div>
        <div style={{ marginBottom: 18 }}>
          <div className="font-semibold mb-2" style={{ fontWeight: 600, marginBottom: 8 }}>Client</div>
          <div className="text-[13px] text-[#222]" style={{ fontSize: 13, color: '#222', marginBottom: 2 }}>{(offer.client?.name || 'Client')}</div>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: 24 }}>
          <div style={{ fontWeight: 600, fontSize: 15, color: '#222' }}>Date de livraison : <span style={{ fontWeight: 400, color: '#222' }}>{formatDate(offer.deadline)}</span></div>
          <div style={{ fontWeight: 600, fontSize: 15, color: '#222' }}>Budget : <span style={{ fontWeight: 400, color: '#222' }}>{offer.budget}</span></div>
        </div>
      </div>
    </div>
  );
};

export default Brief; 