import React, { useState } from "react";
import EditBioModal from "./EditBioModal";

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  is_professional: boolean;
}

// Interface pour les éléments du portfolio
interface PortfolioItem {
  id?: number;
  path?: string;
  name?: string;
  type?: string;
  created_at?: string;
  // description?: string;
}

interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  phone: string;
  email?: string;
  address: string;
  city: string;
  country: string;
  skills: string[] | null;
  languages: string[] | null;
  availability_status: string | null;
  services_offered: string[];
  hourly_rate: string;
  completion_percentage: number;
  created_at: string;
  updated_at: string;
  avatar?: string;
  cover_photo?: string;
  profile_picture_path?: string;
  rating?: number;
  review_count?: number;
  bio?: string;
  title?: string;
  portfolio?: PortfolioItem[];
  user: User;
  likes_count?: number;
  views_count?: number;
}

interface AboutProps {
  pro_detail: FreelanceProfile;
  isPro? :boolean
}
const AboutPro: React.FC<AboutProps> = ({ pro_detail,isPro }) => {
  const [showEditBioModal, setShowEditBioModal] = useState(false);
  return (
    <>
    <div className="w-full">
      <div className="w-full px-[10px] md:px-[40px] mx-auto">
      { isPro===true && (
      <button
          type="button"
          onClick={() => setShowEditBioModal(true)}
          className="bg-transparent rounded-full py-2 px-8 font-sans font-medium text-base cursor-pointer transition-colors duration-200 shadow-sm"
          style={{
            fontFamily: "'Inter', sans-serif",
            backgroundColor: "rgb(0, 110, 255)",
            fontWeight: 400,
            color: "white",
            fontSize: "16px",
          }}
          // onMouseOver={(e) => {
          //   if (activeTab !== 'work') {
          //     e.currentTarget.style.backgroundColor = "rgb(0, 110, 255)";
          //     e.currentTarget.style.color = "white";
          //   }
          // }}
          // onMouseOut={(e) => {
          //     e.currentTarget.style.backgroundColor = "#f6f7f8";
          //     e.currentTarget.style.color = "rgb(72, 72, 72)";
          // }}
        >
          Edit
        </button>
      )}
        <h1
          className="mb-8 mt-8"
          style={{
            fontSize: "28px",
            fontFamily: "'Inter', sans-serif",
            fontWeight: 500,
            lineHeight: "1em",
            color: "#0D0C22",
          }}
        >
          Biography 
        </h1>

        <div className="w-full flex flex-col md:flex-row gap-8">
          {/* Colonne de gauche - Contenu principal (70%) */}
          <div className="w-full md:w-[70%] mdx:w-[60%] bg-white rounded-lg">
            <p
              className="mb-2 text-left"
              style={{
                fontSize: "17px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 400,
                lineHeight: "1.4em",
                color: "#0D0C22",
              }}
            >
              {pro_detail?.bio || "Aucune biographie disponible."}
              {/* With 13+ years of experience as a UI/UX Designer specializing in
              web and mobile app design, I've worked with a variety of clients
              to build great products from early concept to delightful and
              user-friendly design. I support the design process from concept,
              through sketching on the paper, wireframing (sometimes I skip this
              step) to stunning visuals wrapped up in a detailed prototype. */}
            </p>

            <h2
              className="mb-2 text-left"
              style={{
                fontSize: "17px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 400,
                color: "#0D0C22",
              }}
            >
              I am an Expert in:
            </h2>
            <div
              className="flex flex-col gap-3 mb-3"
              style={{
                fontSize: "17px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 400,
                color: "#0D0C22",
              }}
            >
              {pro_detail?.skills?.map((skill, index) => (
                <div key={index} className="flex items-center text-left">
                  <span className="text-black-500 mr-3">✦</span>
                  <span
                    style={{
                      fontSize: "17px",
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: 400,
                      color: "#0D0C22",
                    }}
                  >
                    {skill}
                  </span>
                </div>
              ))}
              {/* {[
                "User Interface Design",
                "Web Design",
                "Mobile App Design",
                "Landing Page Design",
                "Dashboard Design",
                "Data Visualization",
                "Responsive Design",
                "UI Interaction",
                "UI Prototyping",
              ].map((skill, index) => (
                <div key={index} className="flex items-center text-left">
                  <span className="text-black-500 mr-3">✦</span>
                  <span
                    style={{
                      fontSize: "17px",
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: 400,
                      color: "#0D0C22",
                    }}
                  >
                    {skill}
                  </span>
                </div>
              ))} */}
            </div>

            <p
              className="text-left"
              style={{
                fontSize: "17px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 400,
                color: "#0D0C22",
              }}
            >
              Feel free to get in touch.
            </p>

            {/* Section Compétences */}
            <div className="mt-8">
              {/* <h2 className="text-xl font-semibold mb-4 text-left">Skills</h2> */}
              <h2 className="text-xl font-semibold mb-4 text-left">
                Languages
              </h2>
              <div className="flex flex-wrap gap-2">
                {pro_detail?.languages?.map((language, index) => (
                  <span
                    key={index}
                    className="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded text-left"
                  >
                    {language}
                  </span>
                ))}
                {/* {pro_detail?.skills?.map((skill, index) => (
                  <span
                    key={index}
                    className="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded text-left"
                  >
                    {skill}
                  </span>
                ))} */}
                {/* {[
                  "3d visualisation",
                  "Architecture",
                  "3dmox",
                  "Corona render",
                  "Visits virtualite",
                ].map((skill, index) => (
                  <span
                    key={index}
                    className="bg-gray-100 text-gray-800 text-sm px-3 py-1 rounded text-left"
                  >
                    {skill}
                  </span>
                ))} */}
              </div>
            </div>

            {/* Section Statistiques */}
            <div className="mt-8 pt-6 border-t border-black">
              <div className="flex items-center space-x-8">
                <div className="text-left">
                  <div
                    style={{
                      fontSize: "17px",
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: 400,
                      color: "#0D0C22",
                    }}
                  >
                    {pro_detail.review_count || 0} pin
                  </div>
                </div>
                <div className="text-left">
                  <div
                    style={{
                      fontSize: "17px",
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: 400,
                      color: "#0D0C22",
                    }}
                  >
                    {pro_detail.likes_count || 0} likes
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Colonne de droite - Sidebar (30%) */}
          <div className="w-full md:w-[30%] mdx:w-[40%]">
            <div className="bg-[#f6f7f8] p-6 rounded-lg sticky top-6">
              <div className="flex flex-col gap-4">
                {/* Localisation - England */}
                <div className="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="ionicon h-5 w-5 text-gray-600 mr-2"
                    viewBox="0 0 512 512"
                  >
                    <circle cx="256" cy="192" r="32"></circle>
                    <path d="M256 32c-88.22 0-160 68.65-160 153 0 40.17 18.31 93.59 54.42 158.78 29 52.34 62.55 99.67 80 123.22a31.75 31.75 0 0051.22 0c17.42-23.55 51-70.88 80-123.22C397.69 278.61 416 225.19 416 185c0-84.35-71.78-153-160-153zm0 224a64 64 0 1164-64 64.07 64.07 0 01-64 64z"></path>
                  </svg>
                  <span className="text-gray-800 font-normal text-base">
                    {pro_detail.address} {pro_detail.city} {pro_detail.country}
                  </span>
                </div>

                {/* Badge PRO et texte Pro account alignés */}
                <div className="flex items-center gap-2">
                  <span className="bg-black text-white text-xs font-medium px-2 py-1 rounded-full">
                    PRO
                  </span>
                  <span className="text-gray-800 font-normal text-base">
                    Pro account
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <EditBioModal
        open={showEditBioModal}
        onClose={() => setShowEditBioModal(false)}
        onSaved={(p) => {
          // rafraîchir l’écran si besoin
          window.location.reload();
          console.log("bio saved", p);
        }}
      />
    </>
  );
};

export default AboutPro;
