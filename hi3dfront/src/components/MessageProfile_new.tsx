import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { API_BASE_URL } from './../config';

const MessageProfile = ({ application, offerDetail }: { application?: any; offerDetail?: any }) => {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const token = localStorage.getItem('token');
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const isClient = currentUser.role === 'client' || !currentUser.is_professional;

  const getUrlProlfil = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  const currentApp = application?.application ?? application;

  useEffect(() => {
    console.log("Application nettoyée :", currentApp);
  }, [currentApp]);

  const hasAlreadyApplied = () => {
    return offerDetail?.applications?.some((application: any) => (
      application.freelance_profile?.user?.id === currentUser.id &&
      application.status === 'accepted'
    ));
  };

  const hasInvited = () => {
    return offerDetail?.applications?.some((application: any) => (
      application.freelance_profile?.user?.id === currentUser.id &&
      application.status === 'invited'
    ));
  };

  const hasAlreadyRefused = () => {
    return offerDetail?.applications?.some((application: any) => (
      application.freelance_profile?.user?.id === currentUser.id &&
      application.status === 'rejected'
    ));
  };

  const hasAlreadyPostule = () => {
    return offerDetail?.applications?.some((application: any) => (
      application.freelance_profile?.user?.id === currentUser.id &&
      application.status === 'pending'
    ));
  };

  const handleDeclineOffer = async () => {
    if (!token || !offerDetail || !offerDetail.applications) {
      setError("Données insuffisantes pour refuser l'offre. Il faut être invité pour pouvoir refuser. Merci");
      return;
    }

    const application = offerDetail.applications.find((app: any) =>
      app.freelance_profile?.user?.id === currentUser.id
    );

    if (!application) {
      setError("Aucune candidature ou invitation trouvée pour vous. Il faut être invité pour pouvoir refuser. Merci");
      return;
    }

    const applicationId = application.id;

    try {
      const response = await fetch(`${API_BASE_URL}/api/offer-applications/${applicationId}/decline`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error("Erreur lors du refus de l'offre");
      }

      setSuccessMessage("Vous avez indiqué que vous n'êtes pas disponible pour cette offre.");
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (err) {
      console.error('Erreur:', err);
      setError("Impossible de refuser l'offre. Veuillez réessayer plus tard.");
    }
  };

  const handleAcceptOffer = async () => {
    if (!token || !offerDetail || !offerDetail.applications) {
      setError("Données insuffisantes pour accepter l'offre. Il faut être invité pour pouvoir accépté sinon vous pouvez postuler. Merci");
      return;
    }

    const application = offerDetail.applications.find((app: any) =>
      app.freelance_profile?.user?.id === currentUser.id
    );

    if (!application) {
      setError("Aucune candidature ou invitation trouvée pour vous. Il faut être invité pour pouvoir accépté sinon vous pouvez postuler. Merci");
      return;
    }

    const applicationId = application.id;

    try {
      const response = await fetch(`${API_BASE_URL}/api/offer-applications/${applicationId}/accept`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorBody = await response.json();
        console.error('Erreur API:', errorBody);
        throw new Error(errorBody.message || "Erreur lors de l'acceptation de l'offre");
      }

      alert("L'offre accepter avec succès");
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (err) {
      console.error('Erreur:', err);
      setError("Impossible d'accepter l'offre. Veuillez réessayer plus tard.");
    }
  };

  const handleApplyToProject = async (projectId: number) => {
    const token = localStorage.getItem("token");
    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${projectId}/apply`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Une erreur est survenue');
      }
      alert(`Vous avez marqué l'offre comme intéressé.`);
      setTimeout(() => {
        window.location.reload();
      }, 2000);
    } catch (err) {
      alert(err instanceof Error ? err.message : "Une erreur est survenue.");
    }
  };

  const handleSelectProfessional = async (applicationId: number) => {
    if (!token || !offerDetail.id) return;
    console.log("Contenu : ",JSON.stringify({ application_id: applicationId }))
    try {
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${offerDetail.id}/assign`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ application_id: applicationId }),
      });

      if (!response.ok) {
        console.log("Retour : ",response)
        throw new Error('Erreur lors de la sélection du professionnel');
      }
      // Afficher un message de succès
      setSuccessMessage('Vous avez sélectionné ce professionnel pour votre projet. Il en sera informé.');
      window.location.reload();
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de sélectionner le professionnel. Veuillez réessayer plus tard.');
    }
  };

  const handleAcceptApplication = async (applicationId: number) => {
    const token = localStorage.getItem("token");
    try {
        const response = await fetch(`${API_BASE_URL}/api/offer-applications/${applicationId}/status`, {
            method: 'PATCH',
            headers: {
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: 'accepted' }),
        });
        if (!response.ok) throw new Error("Erreur lors de l'acceptation");
        window.location.reload();
    } catch (error) {
        alert("Échec de l'acceptation de la candidature.");
    }
};

  // ----------
  // UI: Design calqué sur MessageProfile.tsx
  // ----------

  // Vue côté professionnel: on affiche le profil du client propriétaire de l'offre
  if (isClient === false && offerDetail) {
    const client = offerDetail.user || {};
    console.log("Details offre : ", offerDetail)
    const fullName = `${client.client_profile.first_name || ''} ${client.client_profile.last_name || ''}`.trim() || 'Client';
    return (
      <div className="space-y-4 p-3 bg-[#F5F5F5]" style={{ height: '100%', borderRadius: 14, border: '1px solid #e5e5e5' }}>
        <div onClick={() => navigate(`/professionals/${client.id}`)} className="flex items-center cursor-pointer ">
          <img
            src={getUrlProlfil(client.client_profile.avatar)}
            alt={fullName}
            className="w-28 h-28 rounded-full object-cover mr-3"
            onError={(e) => {
              (e.currentTarget as HTMLImageElement).onerror = null;
              (e.currentTarget as HTMLImageElement).src = 'https://randomuser.me/api/portraits/men/32.jpg';
            }}
          />
          <div>
            <h4
              className="text-sm font-semibold"
              style={{
                fontFamily: "'Inter', sans-serif",
                fontWeight: '500',
                fontSize: '16px',
                color: '#000000'
              }}
            >
              {fullName}
            </h4>
            <p
              style={{
                fontFamily: "'Inter', sans-serif",
                fontWeight: '500',
                fontSize: '16px',
                color: '#000000'
              }}
            >
              {/* {offerDetail.title || 'Project owner'} */}
              {offerDetail?.title
                ? offerDetail.title.length > 30
                ? offerDetail.title.slice(0, 30) + "..."
                : offerDetail.title
                : "Project owner"}
            </p>
          </div>
        </div>

        <div className="flex items-center text-sm text-gray-600">
          <span className="mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" className="ionicon" viewBox="0 0 512 512" width="16" height="20">
              <circle cx="256" cy="192" r="32"></circle>
              <path d="M256 32c-88.22 0-160 68.65-160 153 0 40.17 18.31 93.59 54.42 158.78 29 52.34 62.55 99.67 80 123.22a31.75 31.75 0 0051.22 0c17.42-23.55 51-70.88 80-123.22C397.69 278.61 416 225.19 416 185c0-84.35-71.78-153-160-153zm0 224a64 64 0 1164-64 64.07 64.07 0 01-64 64z"></path>
            </svg>
          </span>
          <span
            style={{
              fontFamily: "'Inter', sans-serif",
              fontWeight: '400',
              fontSize: '14px',
              color: '#000000'
            }}
          >
            {client.client_profile.city || ''}, {client.client_profile.country || ''}
          </span>
        </div>

        <div className="flex items-center">
          <span
            className="bg-[#000000] text-white rounded-full px-1.5 py-0.5 inline-block mr-3"
            style={{ fontSize: '8px', fontFamily: "'Inter', sans-serif", fontWeight: 300 }}
          >
            CUSTOMER
          </span>
          <span style={{ fontFamily: "'Inter', sans-serif", fontWeight: '400', fontSize: '14px', color: '#000000' }}>
            Customer account
          </span>
        </div>

        {/* Actions (reprend la logique existante) */}
        {hasAlreadyApplied() ? (
          <p className="text-green-700 font-semibold">Vous êtes déjà intéressé par cette offre.</p>
        ) : hasAlreadyPostule() ? (
          <p className="text-yellow-600 font-medium bg-yellow-100 px-3 py-2 rounded-md shadow-sm w-fit">
            ⏳ Votre demande est en attente d'acceptation du client.
          </p>
        ) : hasAlreadyRefused() ? (
          <p className="text-red-700 font-semibold bg-red-100 px-3 py-2 rounded-md shadow-sm w-fit">
            ❌ Vous avez déjà refusé cette offre.
          </p>
        ) : hasInvited() ? (
          <>
            <button
              className="h-[50px] w-full bg-white py-2 rounded-[14px] flex items-center justify-center hover:bg-gray-50 border border-gray-200"
              style={{ fontFamily: "'Inter', sans-serif", fontWeight: '400', fontSize: '14px', color: '#000000' }}
              onClick={handleDeclineOffer}
            >
              Pas disponible
            </button>
            <button
              className="h-[50px] w-full bg-black py-2 rounded-[14px] flex items-center justify-center hover:bg-gray-900 text-white"
              style={{ fontFamily: "'Inter', sans-serif", fontWeight: '400', fontSize: '14px' }}
              onClick={handleAcceptOffer}
            >
              Interessé
            </button>
          </>
        ) : (
          <button
            className="h-[50px] w-full bg-black py-2 rounded-[14px] flex items-center justify-center hover:bg-gray-900 text-white"
            style={{ fontFamily: "'Inter', sans-serif", fontWeight: '400', fontSize: '14px' }}
            onClick={() => handleApplyToProject(Number(offerDetail.id))}
          >
            Postuler
          </button>
        )}
      </div>
    );
  }

  // Vue côté client: on affiche le profil du freelance sélectionné
  if (!currentApp || !currentApp.freelance_profile) {
    return (
      <div className="space-y-4 p-3 bg-[#F5F5F5]" style={{ height: '65%', borderRadius: 14, border: '1px solid #e5e5e5' }}>
        <div className="flex items-center text-gray-400 justify-center" style={{ height: 180 }}>
          <div className="w-24 h-24 mb-4 flex items-center justify-center bg-gray-100 rounded-full text-4xl opacity-50">👤</div>
        </div>
        <p className="text-sm text-center">No profile selected</p>
        <p className="text-xs text-center">Click on a candidate to see their profile</p>
      </div>
    );
  }

  const freelance = currentApp.freelance_profile;
  const freelanceName = `${freelance?.first_name || ''} ${freelance?.last_name || ''}`.trim() || 'Professional';

  return (
    <div className="space-y-4 p-3 bg-[#F5F5F5]" style={{ height: '65%', borderRadius: 14, border: '1px solid #e5e5e5' }}>
      <div onClick={() => navigate(`/professionals/${freelance.id}`)} className="flex items-center cursor-pointer ">
        <img
          src={getUrlProlfil(freelance.avatar)}
          alt={freelanceName}
          className="w-28 h-28 rounded-full object-cover mr-3"
        />
        <div>
          <h4
            className="text-sm font-semibold"
            style={{ fontFamily: "'Inter', sans-serif", fontWeight: '500', fontSize: '16px', color: '#000000' }}
          >
            {freelanceName}
          </h4>
          <p
            style={{ fontFamily: "'Inter', sans-serif", fontWeight: '500', fontSize: '16px', color: '#000000' }}
          >
            {freelance?.title
                ? freelance.title.length > 30
                ? freelance.title.slice(0, 30) + "..."
                : freelance.title
                : "Architecture visualisation"}
          </p>
        </div>
      </div>

      <div className="flex items-center text-sm text-gray-600">
        <span className="mr-3">
          <svg xmlns="http://www.w3.org/2000/svg" className="ionicon" viewBox="0 0 512 512" width="16" height="20">
            <circle cx="256" cy="192" r="32"></circle>
            <path d="M256 32c-88.22 0-160 68.65-160 153 0 40.17 18.31 93.59 54.42 158.78 29 52.34 62.55 99.67 80 123.22a31.75 31.75 0 0051.22 0c17.42-23.55 51-70.88 80-123.22C397.69 278.61 416 225.19 416 185c0-84.35-71.78-153-160-153zm0 224a64 64 0 1164-64 64.07 64.07 0 01-64 64z"></path>
          </svg>
        </span>
        <span style={{ fontFamily: "'Inter', sans-serif", fontWeight: '400', fontSize: '14px', color: '#000000' }}>
          {freelance?.city || 'Unknown city'}, {freelance?.country || 'Unknown country'}
        </span>
      </div>

      <div className="flex items-center">
        <span
          className="bg-[#000000] text-white rounded-full px-1.5 py-0.5 inline-block mr-3"
          style={{ fontSize: '8px', fontFamily: "'Inter', sans-serif", fontWeight: 300 }}
        >
          PRO
        </span>
        <span style={{ fontFamily: "'Inter', sans-serif", fontWeight: '400', fontSize: '14px', color: '#000000' }}>
          Pro account
        </span>
      </div>

      {Array.isArray(freelance?.languages) && freelance.languages.length > 0 && (
        <div className="flex items-center text-sm">
          <span className="mr-2 text-xl text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="ionicon" viewBox="0 0 512 512" width="16" height="20">
              <path fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="32" d="M48 112h288M192 64v48M272 448l96-224 96 224M301.5 384h133M281.3 112S257 206 199 277 80 384 80 384"></path>
              <path d="M256 336s-35-27-72-75-56-85-56-85" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="32"></path>
            </svg>
          </span>
          <ul className="flex space-x-2 list-none uppercase">
            {freelance.languages.map((language: string, index: number) => (
              <li key={index} style={{ fontFamily: "'Inter', sans-serif", fontWeight: '400', fontSize: '14px', color: '#000000' }}>
                {language}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Actions côté client, selon le statut de la candidature */}
      {currentApp?.status === 'pending' && offerDetail?.status === 'open' && (
        <button
          className="h-[50px] w-full bg-black py-2 rounded-[24px] flex items-center justify-center hover:bg-gray-900 text-white"
          style={{ fontFamily: "'Inter', sans-serif", fontWeight: '400', fontSize: '14px' }}
          onClick={() => handleAcceptApplication(currentApp.id)}
        >
          Accept demande
        </button>
      )}

      {currentApp?.status === 'accepted' && offerDetail?.status === 'open' && (
        <button
          className="h-[50px] w-full bg-black py-2 rounded-[24px] flex items-center justify-center hover:bg-gray-900 text-white"
          style={{ fontFamily: "'Inter', sans-serif", fontWeight: '400', fontSize: '14px' }}
          onClick={() => handleSelectProfessional(currentApp.id)}
        >
          Approve the offer
        </button>
      )}

      <button
        className="h-[50px] w-full bg-white py-2 rounded-[24px] flex items-center justify-center hover:bg-gray-50 border border-gray-200"
        style={{ fontFamily: "'Inter', sans-serif", fontWeight: '400', fontSize: '14px', color: '#000000' }}
        onClick={() => navigate(`/professionals/${freelance.id}`)}
      >
        View profil
      </button>

      {error && (
        <div className="text-red-600 text-sm">{error}</div>
      )}
      {successMessage && (
        <div className="text-green-600 text-sm">{successMessage}</div>
      )}
    </div>
  );
};

export default MessageProfile;
