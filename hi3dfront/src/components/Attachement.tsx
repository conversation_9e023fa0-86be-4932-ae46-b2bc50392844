import React, { useEffect, useState } from 'react';
import { API_BASE_URL } from './../config';

const Attachement = () => {
  const [files, setFiles] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const token = localStorage.getItem('token');

  useEffect(() => {
    const fetchFiles = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/files`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        const data = await response.json();
        // console.log("Listes des attachements==========",data);
        setFiles(data.data?.files || []); 
      } catch (err) {
        setFiles([]);
      } finally {
        setLoading(false);
      }
    };
    fetchFiles();
  }, [token]);

  if (loading) return <div>Chargement...</div>;

  return (
    <div style={{ width: '100%' }}>
      <div style={{ display: 'flex', fontWeight: 600, background: '#faf7f3', borderRadius: 12, padding: '18px 24px', marginBottom: 12 }}>
        <span style={{ flex: 2 }}>Nom du fichier</span>
        <span style={{ flex: 1 }}>Date d'ajout</span>
        <span style={{ flex: 1 }}>Type</span>
        <span style={{ flex: 1 }}></span>
      </div>
      {files.length === 0 && <div style={{ padding: 24, color: '#888' }}>Aucun fichier trouvé.</div>}
      {files.map(file => (
        <div key={file.id} style={{ display: 'flex', alignItems: 'center', background: '#faf7f3', borderRadius: 12, padding: '18px 24px', marginBottom: 12 }}>
          <span style={{ flex: 2 }}>{file.original_name}</span>
          <span style={{ flex: 1 }}>{file.created_at ? new Date(file.created_at).toLocaleDateString() : ''}</span>
          <span style={{ flex: 1 }}>{file.mime_type || file.extension}</span>
          <span style={{ flex: 1, display: 'flex', justifyContent: 'flex-end', gap: 8 }}>
            <a href={`/api/files/${file.id}/download`} target="_blank" rel="noopener noreferrer">
              <button style={{ padding: '8px 18px', borderRadius: 20, background: '#fff', border: '1px solid #eee', fontWeight: 600, cursor: 'pointer' }}>Download</button>
            </a>
          </span>
        </div>
      ))}
    </div>
  );
};

export default Attachement; 