import ServiceOffersList from './services/ServiceOffersList';
import ServiceOfferForm from './services/ServiceOfferForm';
import ServiceOfferDetails from './services/ServiceOfferDetails';
import type { ServiceOffer, ServiceOfferFormData } from './services/types';
import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { Plus } from 'lucide-react';
import Button from './ui/Button';
import Alert from './ui/Alert';
import { API_BASE_URL } from '../config';

type GalleryItem = {
  id: number;
  title: string;
  author: string;
  authorAvatar: string;
  isPro?: boolean;
  likes: number;
  views: string;
  image: string;
};

type Props = {
  items: GalleryItem[];
  service: ServiceOffer[];
};

const MyServiceOffer: React.FC<Props> = ({ items, service }) => {
  const navigate = useNavigate();
    const location = useLocation();
    const { id } = useParams<{ id: string }>();
    const [services, setServices] = useState<ServiceOffer[]>([]);
    const [selectedService, setSelectedService] = useState<ServiceOffer | null>(null);
    const [showForm, setShowForm] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [formError, setFormError] = useState<string | null>(null);
    const [formSuccess, setFormSuccess] = useState<string | null>(null);
    const [formLoading, setFormLoading] = useState<boolean>(false);
    const token = localStorage.getItem('token');
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [serviceToDelete, setServiceToDelete] = useState<number | null>(null);
  
  
    // Vérifier le statut professionnel de l'utilisateur avec plus de détails
    console.log("User object from localStorage:", user);
    console.log("User is_professional:", user.is_professional);
    console.log("User is_professional type:", typeof user.is_professional);
  
    // Vérifier si l'utilisateur est un professionnel (avec vérification plus robuste)
    const isProfessional = user && (
      user.is_professional === true ||
      user.is_professional === 1 ||
      user.is_professional === "1" ||
      user.is_professional === "true"
    );
    console.log("isProfessional:", isProfessional);
  
    const isEditMode = !!selectedService;
  
    // Récupérer le paramètre user de l'URL
    const searchParams = new URLSearchParams(location.search);
    const userIdParam = searchParams.get('user');

    const handleCreateService = () => {
        setSelectedService(null);
        setShowForm(true);
      };
    
      // Gérer la modification d'un service existant
      const handleEditService = (service: ServiceOffer) => {
        setSelectedService(service);
        setShowForm(true);
      };

      // Gérer le clic sur un service
        const handleServiceClick = (serviceId: number) => {
          navigate(`/dashboard/services/${serviceId}`);
        };

        const handleSelectService = (id: number) => {
          const found = service.find(service => service.id === id);
          if (found) {
            setSelectedService(found);
          }
        };
      
        // const handleSelectService = (service: ServiceOffer) => {

        //   setSelectedService(service);
        // }
      
        // Gérer le partage d'un service
        const handleShareService = (serviceId: number) => {
          const url = `${window.location.origin}/service/${serviceId}`;
          navigator.clipboard.writeText(url);
          alert('Lien copié dans le presse-papier');
        };

  const handleDeleteService = async (serviceId: number) => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/service-offers/${serviceId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression du service');
      }

      // Mettre à jour la liste des services
      setServices(services.filter(service => service.id !== serviceId));

      // Si le service supprimé était sélectionné, désélectionner
      if (selectedService && selectedService.id === serviceId) {
        setSelectedService(null);
        // Rediriger vers la page des services du professionnel connecté avec le paramètre user
        // Utiliser window.location.href pour forcer un rechargement complet
        const timestamp = new Date().getTime();
        window.location.href = `/dashboard/services?user=${user.id}&t=${timestamp}`;
      }

      setFormSuccess('Service supprimé avec succès');
    } catch (err) {
      console.error('Erreur:', err);
      setFormError('Impossible de supprimer le service');
    }
  };
  
    const handleFormSubmit = async (formData: ServiceOfferFormData) => {
      setFormLoading(true);
      setFormError(null);
      setFormSuccess(null);
  
      try {
        const payload = {
          title: formData.title,
          description: formData.description,
          price: typeof formData.price === 'string' ? parseFloat(formData.price) : formData.price,
          execution_time: formData.execution_time,
          concepts: formData.concepts,
          revisions: formData.revisions,
          categories: formData.categories,
          is_private: formData.is_private,
          status: formData.status,
        };
  
        let requestBody;
        let headers: Record<string, string> = {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        };
  
        if (formData.images && formData.images.length > 0) {
          const formDataObj = new FormData();
  
          formData.images.forEach((file: File) => {
            formDataObj.append('files[]', file);
          });
  
          Object.entries(payload).forEach(([key, value]) => {
            if (key === 'categories') {
              // Envoyer chaque catégorie individuellement
              if (Array.isArray(value)) {
                value.forEach((category, index) => {
                  formDataObj.append(`categories[${index}]`, category);
                });
              }
            } else if (key === 'is_private') {
              formDataObj.append(key, value ? '1' : '0');
            } else {
              formDataObj.append(key, String(value));
            }
          });
  
          requestBody = formDataObj;
        } else {
          // Pour les requêtes sans fichiers, s'assurer que les catégories sont bien un tableau
          if (Array.isArray(payload.categories)) {
            payload.categories = payload.categories;
          } else if (typeof payload.categories === 'string') {
            payload.categories = [payload.categories];
          } else {
            payload.categories = [];
          }
          requestBody = JSON.stringify(payload);
          headers['Content-Type'] = 'application/json';
        }
  
        const endpoint = isEditMode
          ? `${API_BASE_URL}/api/service-offers/${selectedService?.id}`
          : `${API_BASE_URL}/api/service-offers`;
  
        const response = await fetch(endpoint, {
          method: isEditMode ? 'POST' : 'POST',
          headers,
          body: requestBody,
        });
  
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || `Erreur lors de ${isEditMode ? 'la modification' : 'la création'} du service`);
        }
  
        const data = await response.json();
        
        if (isEditMode) {
          setServices(services.map(service =>
            service.id === selectedService?.id ? {
              ...service,
              ...payload,
              updated_at: new Date().toISOString()
            } : service
          ));
          setFormSuccess('Service mis à jour avec succès');
          // Forcer l'actualisation de la page avec window.location.href
          navigate('/edit-portfolio', { state: { activeTab: 'offers' } });
        } else {
          const newService: ServiceOffer = {
            ...payload,
            price_unit: formData.price_unit,
            id: data.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            user: {
              id: user.id,
              first_name: user.first_name,
              last_name: user.last_name,
              profile_picture_path: user.profile_picture_path,
            },
            files: data.files || [],
            imageUrl: data.files && data.files.length > 0
              ? `${API_BASE_URL}/storage/${data.files[0].path}`
              : undefined,
            file_urls: Array.isArray(data.files)
              ? data.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
              : [],
            likes: 0,
            views: 0,
          };
          setServices([...services, newService]);
          setFormSuccess('Service créé avec succès');
          // Rediriger vers la page des services avec le paramètre user
          navigate('/edit-portfolio', { state: { activeTab: 'offers' } });
        }
  
        setShowForm(false);
      } catch (err) {
        console.error('Erreur:', err);
        setFormError(err instanceof Error ? err.message : `Impossible de ${isEditMode ? 'modifier' : 'créer'} le service`);
      } finally {
        setFormLoading(false);
      }
    };
  
    // Gérer l'annulation du formulaire
    const handleFormCancel = () => {
      setShowForm(false);
      if (!isEditMode) {
        setSelectedService(null);
      }
    };

  return (
    <div className="w-full">
      <>
       {/* Afficher les erreurs */}
      {(error || formError) && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => {
            setError(null);
            setFormError(null);
          }}
          className="mb-6"
        >
          {error || formError}
        </Alert>
      )}

      {/* Afficher les succès */}
      {formSuccess && (
        <Alert
          type="success"
          title="Succès"
          onClose={() => setFormSuccess(null)}
          className="mb-6"
        >
          {formSuccess}
        </Alert>
      )}

      {/* Afficher le formulaire, les détails ou la liste */}
      {showForm ? (
        <ServiceOfferForm
          initialData={isEditMode ? selectedService : undefined}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
          isLoading={formLoading}
        />
      ) : selectedService ? (
        <ServiceOfferDetails
          service={selectedService}
          onEdit={handleEditService}
          onDelete={handleDeleteService}
          onShare={handleShareService}
        />
      ) : (
        <div className="w-full grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 sm:gap-8">
        {items.map(item => (
        <div
          key={item.id}
          className="group w-full max-w-[315px] bg-white rounded-lg overflow-hidden relative shadow-none flex flex-col"
          onClick={() => handleSelectService(item.id)}
        >
          {/* Boutons Modifier/Supprimer en haut à droite, visibles au survol */}
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 flex space-x-2">
            <button
              onClick={e => {
                e.stopPropagation();
                // Ouvre le formulaire d'édition pour ce service
                const found = service.find(s => s.id === item.id);
                if (found) handleEditService(found);
              }}
              className="bg-white rounded-full p-2 shadow hover:bg-blue-100"
              title="Modifier ce service"
            >
              <svg width="20" height="20" fill="none" stroke="#2980b9" strokeWidth="2" viewBox="0 0 24 24">
                <path d="M15.232 5.232l3.536 3.536M9 13l6-6 3 3-6 6H9v-3z"/>
                <path d="M16 7l1.5-1.5a2.121 2.121 0 1 1 3 3L19 10"/>
              </svg>
            </button>
            <button
              onClick={e => {
                e.stopPropagation();
                setServiceToDelete(item.id);
                setShowDeleteModal(true);
              }}
              className="bg-white rounded-full p-2 shadow hover:bg-red-100"
              title="Supprimer ce service"
            >
              <svg width="20" height="20" fill="none" stroke="#e74c3c" strokeWidth="2" viewBox="0 0 24 24">
                <path d="M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"/>
              </svg>
            </button>
          </div>
          <div
            style={{
              backgroundImage: `url(${item.image})`,
            }}
            className="bg-[#2d241b] bg-cover bg-center w-full h-[220px] rounded-lg"
            aria-label={item.title}
            role="img"
            onClick={() => handleSelectService(item.id)}
          />
          <div
            className="w-full flex justify-between items-center px-0 pt-[18px] pb-2.5 min-h-[60px] text-[12px]" style={{ fontFamily: 'Arial, sans-serif' }}
          >
            <div className="flex items-center gap-2.5">
              <img
                src={item.authorAvatar}
                alt={item.author}
                className="w-8 h-8 rounded-full object-cover border-2 border-[#eee]"
              />
              <span className="font-semibold truncate max-w-[90px]">{item.author}</span>
              {item.isPro && (
                <span className="bg-[#f3f3f3] text-[#888] font-bold rounded px-2 py-0.5 ml-1">PRO</span>
              )}
            </div>
            <div className="flex items-center gap-4 text-[#888]">
              <span className="flex items-center gap-1">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="#b3b3b3" className="mr-0.5"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41 0.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>
                {item.likes}
              </span>
              <span className="flex items-center gap-1">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="#b3b3b3" className="mr-0.5"><path d="M12 5c-7 0-10 7-10 7s3 7 10 7 10-7 10-7-3-7-10-7zm0 12c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8a3 3 0 100 6 3 3 0 000-6z"/></svg>
                {item.views}
              </span>
            </div>
          </div>
        </div>
      ))}
      {/* Add work  card */}

      <div
        className="w-full max-w-[315px] bg-white rounded-lg overflow-hidden relative shadow-none flex flex-col border-2 border-dashed border-[#222] min-h-[220px] items-center justify-center cursor-pointer"
        onClick={() => navigate('/dashboard/services/create')}
        tabIndex={0}
        role="button"
        aria-label="Add work"
        onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') navigate('/dashboard/services/create'); }}
      >
        <div style={{ fontSize: 32, marginBottom: 8, color: '#222' }}>+</div>
        <div style={{ fontSize: 18, color: '#222' }}>Add Offer</div>
      </div>
      {/* <div className="w-[90vw] max-w-[315px] bg-white rounded-lg overflow-hidden m-auto relative shadow-none flex flex-col border-2 border-dashed border-[#222] min-h-[220px] items-center justify-center cursor-pointer">
        <div style={{ fontSize: 32, marginBottom: 8, color: '#222' }}>+</div>
        <div style={{ fontSize: 18, color: '#222' }}>Add work</div>
      </div> */}
        </div>
      )}

      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl">
            <h3 className="text-lg font-bold">Confirmer la suppression</h3>
            <p className="my-4">Êtes-vous sûr de vouloir supprimer ce service ? Cette action est irréversible.</p>
            <div className="flex justify-end space-x-4">
              <Button variant="outline" onClick={() => setShowDeleteModal(false)}>Annuler</Button>
              <Button variant="danger" onClick={() => {
                if (serviceToDelete) handleDeleteService(serviceToDelete);
                setShowDeleteModal(false);
                setServiceToDelete(null);
              }}>Supprimer</Button>
            </div>
          </div>
        </div>
      )}
      </>
      
    </div>
  );
};

export default MyServiceOffer; 