import React, { useRef, useState } from "react";
import ModalFilter from "./ModalFilter";
import { CATEGORIES } from "./categories";

type Props = {
  onCategorySelect: (category: string) => void;
  selectedCategory: string;
};

const CategoryList: React.FC<Props> = ({
  onCategorySelect,
  selectedCategory,
}) => {
  const [showFilters, setShowFilters] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const categories: string[] = CATEGORIES;

  // ARROWS: state & helpers
  const [canScrollLeftDesktop, setCanScrollLeftDesktop] = useState(false);
  const [canScrollRightDesktop, setCanScrollRightDesktop] = useState(false);

  const mobileScrollRef = useRef<HTMLDivElement>(null);
  const [canScrollLeftMobile, setCanScrollLeftMobile] = useState(false);
  const [canScrollRightMobile, setCanScrollRightMobile] = useState(false);

  const updateScrollState = React.useCallback(() => {
    const el = scrollContainerRef.current;
    if (el) {
      const left = el.scrollLeft > 0;
      const right = el.scrollLeft + el.clientWidth < el.scrollWidth - 0.5;
      if (left !== canScrollLeftDesktop) setCanScrollLeftDesktop(left);
      if (right !== canScrollRightDesktop) setCanScrollRightDesktop(right);
    }
    const ml = mobileScrollRef.current;
    if (ml) {
      const leftM = ml.scrollLeft > 0;
      const rightM = ml.scrollLeft + ml.clientWidth < ml.scrollWidth - 0.5;
      if (leftM !== canScrollLeftMobile) setCanScrollLeftMobile(leftM);
      if (rightM !== canScrollRightMobile) setCanScrollRightMobile(rightM);
    }
  }, [canScrollLeftDesktop, canScrollRightDesktop, canScrollLeftMobile, canScrollRightMobile]);

  const attachListeners = (node: HTMLDivElement | null, cb: () => void) => {
    if (!node) return;
    node.addEventListener("scroll", cb);
  };
  const detachListeners = (node: HTMLDivElement | null, cb: () => void) => {
    if (!node) return;
    node.removeEventListener("scroll", cb);
  };

  React.useLayoutEffect(() => {
    updateScrollState();
    const onResize = () => updateScrollState();
    const desktopNode = scrollContainerRef.current;
    const mobileNode = mobileScrollRef.current;
    attachListeners(desktopNode, updateScrollState);
    attachListeners(mobileNode, updateScrollState);
    window.addEventListener("resize", onResize);
    const onLoad = () => updateScrollState();
    window.addEventListener("load", onLoad);

    const ro = typeof ResizeObserver !== 'undefined' ? new ResizeObserver(() => updateScrollState()) : null;
    if (ro) {
      if (desktopNode) ro.observe(desktopNode);
      if (mobileNode) ro.observe(mobileNode as Element);
    }

    return () => {
      detachListeners(desktopNode, updateScrollState);
      detachListeners(mobileNode, updateScrollState);
      window.removeEventListener("resize", onResize);
      window.removeEventListener("load", onLoad);
      if (ro) ro.disconnect();
    };
  }, [updateScrollState]);

  const scrollByAmount = (node: HTMLDivElement | null, amount: number) => {
    if (!node) return;
    node.scrollBy({ left: amount, behavior: "smooth" });
  };

  return (
    <div className="w-full mx-auto my-8 px-0 font-sans">
      <div className=" px-[10px] md:px-[40px]">
        {/* Desktop layout */}
        <div className="hidden md:flex items-start gap-6">
          {/* Bouton Filtre */}
          <div className="flex-shrink-0 pt-[6px]">
            <button
              className="flex items-center justify-center bg-[#F5F5F5] text-gray-500 border-none rounded-full py-2 px-6 h-10 cursor-pointer transition-colors duration-200 shadow-sm gap-2"
              style={{
                fontSize: "16px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 400,
                color: "#0D0C22",
              }}
              onClick={() => setIsFilterOpen(true)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="ionicon"
                viewBox="0 0 512 512"
                width="20"
                height="20"
              >
                <path d="M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm32 304h-64a16 16 0 010-32h64a16 16 0 010 32zm48-64H176a16 16 0 010-32h160a16 16 0 010 32zm32-64H144a16 16 0 010-32h224a16 16 0 010 32z"></path>
              </svg>
              Filtre
            </button>
            <ModalFilter
              isOpen={isFilterOpen}
              onClose={() => setIsFilterOpen(false)}
            />
          </div>

          {/* Conteneur scrollable des catégories avec flèches */}
          <div className="relative flex-1 overflow-hidden">
            <button
              aria-label="Scroll left"
              className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 border border-gray-200 shadow-sm rounded-full w-9 h-9 flex items-center justify-center transition-opacity ${
                canScrollLeftDesktop ? "opacity-100" : "opacity-0 pointer-events-none"
              }`}
              onClick={() => scrollByAmount(scrollContainerRef.current, -360)}
            >
              <svg width="18" height="18" viewBox="0 0 20 20" fill="none">
                <path d="M12 5L7 10L12 15" stroke="#111827" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>
            <button
              aria-label="Scroll right"
              className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 border border-gray-200 shadow-sm rounded-full w-9 h-9 flex items-center justify-center transition-opacity ${
                canScrollRightDesktop ? "opacity-100" : "opacity-0 pointer-events-none"
              }`}
              onClick={() => scrollByAmount(scrollContainerRef.current, 360)}
            >
              <svg width="18" height="18" viewBox="0 0 20 20" fill="none">
                <path d="M8 5L13 10L8 15" stroke="#111827" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>
            <div
              ref={scrollContainerRef}
              className="w-full overflow-x-auto pb-2"
              style={{ scrollbarWidth: "none", msOverflowStyle: "none" as any }}
            >
              <div className="flex flex-nowrap gap-8 pt-[6px]" style={{ WebkitOverflowScrolling: "touch" as any }}>
                {categories.map((cat) => (
                  <button
                    key={cat}
                    onClick={() => onCategorySelect(cat)}
                    className={`px-4 py-2 text-[15px] rounded-full cursor-pointer transition-all duration-200 flex-shrink-0 whitespace-nowrap ${
                      selectedCategory === cat
                        ? "bg-[#F5F5F5] text-gray-500 font-medium"
                        : "text-black font-normal hover:bg-[#F5F5F5] hover:text-gray-500"
                    }`}
                    style={{
                      fontSize: "16px",
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: 400,
                      color: "#0D0C22",
                    }}
                  >
                    {cat}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile layout */}
      <div className="md:hidden w-full">
        <div className="flex items-center justify-between mb-4">
          <button className="border border-gray-200 rounded-lg px-4 py-2 bg-white font-medium text-[13px] text-gray-700 cursor-pointer flex items-center">
            <span>Popular</span>
            <svg
              width="14"
              height="14"
              viewBox="0 0 20 20"
              fill="none"
              className="ml-1.5"
            >
              <path
                d="M6 8L10 12L14 8"
                stroke="#a1a1aa"
                strokeWidth="2.2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          <div className="relative">
            <button
              className="border border-gray-200 rounded-3xl px-4 py-2 bg-white font-medium text-[14px] text-gray-700 cursor-pointer flex items-center"
              onClick={() => setShowFilters(!showFilters)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                className="mr-2"
              >
                <path
                  d="M3 6.5C3 6.22386 3.22386 6 3.5 6H16.5C16.7761 6 17 6.22386 17 6.5C17 6.77614 16.7761 7 16.5 7H3.5C3.22386 7 3 6.77614 3 6.5ZM5 10.5C5 10.2239 5.22386 10 5.5 10H14.5C14.7761 10 15 10.2239 15 10.5C15 10.7761 14.7761 11 14.5 11H5.5C5.22386 11 5 10.7761 5 10.5ZM8 14.5C8 14.2239 8.22386 14 8.5 14H11.5C11.7761 14 12 14.2239 12 14.5C12 14.7761 11.7761 15 11.5 15H8.5C8.22386 15 8 14.7761 8 14.5Z"
                  fill="#18181b"
                />
              </svg>
              Filter
            </button>

          
          </div>
        </div>

        {/* Catégories mobiles avec flèches */}
        <div className="relative">
          <button
            aria-label="Scroll left"
            className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 border border-gray-200 shadow-sm rounded-full w-9 h-9 flex items-center justify-center transition-opacity ${
              canScrollLeftMobile ? "opacity-100" : "opacity-0 pointer-events-none"
            }`}
            onClick={() => scrollByAmount(mobileScrollRef.current, -300)}
          >
            <svg width="18" height="18" viewBox="0 0 20 20" fill="none">
              <path d="M12 5L7 10L12 15" stroke="#111827" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>
          <button
            aria-label="Scroll right"
            className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white/90 border border-gray-200 shadow-sm rounded-full w-9 h-9 flex items-center justify-center transition-opacity ${
              canScrollRightMobile ? "opacity-100" : "opacity-0 pointer-events-none"
            }`}
            onClick={() => scrollByAmount(mobileScrollRef.current, 300)}
          >
            <svg width="18" height="18" viewBox="0 0 20 20" fill="none">
              <path d="M8 5L13 10L8 15" stroke="#111827" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>
          <div ref={mobileScrollRef} className="overflow-x-auto pb-2" style={{ scrollbarWidth: "none", msOverflowStyle: "none" as any }}>
            <div className="flex gap-6" style={{ WebkitOverflowScrolling: "touch" as any }}>
              {categories.map((cat) => (
                <button
                  key={cat}
                  onClick={() => onCategorySelect(cat)}
                  className={`px-3 py-2 text-[14px] cursor-pointer flex-shrink-0 whitespace-nowrap ${
                    selectedCategory === cat
                      ? "text-black font-medium border-b-2 border-black"
                      : "text-gray-600 font-normal"
                  }`}
                  style={{
                    fontFamily: "Arial, sans-serif",
                  }}
                >
                  {cat}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CategoryList;
