// components/WorkCreateModal.tsx
"use client";
import React, { useEffect, useMemo, useState } from "react";
import Modal from "./Modal";
import { API_BASE_URL } from "../config";
import { CATEGORIES } from "./categories";

export type AchievementPayload = {
  title: string;
  description?: string;
  category?: string;
  cover_photo: string; // existing path/url if any
  gallery_photos?: string[]; // existing paths/urls if any
  youtube_link?: string;
  status?: string;
};

export default function WorkCreateModal({
  open,
  onClose,
  mode = "create",
  initialAchievement,
  onCompleted,
  title = "Create a new work",
}: {
  open: boolean;
  onClose: () => void;
  mode?: "create" | "edit";
  initialAchievement?: Partial<AchievementPayload> & { id?: number };
  onCompleted?: () => void;
  title?: string;
}) {
  const token = typeof window !== "undefined" ? localStorage.getItem("token") : null;
  const isAuthenticated = !!token;

  title = mode === "edit" ? "Edit work" : "Create a new work";

  const [values, setValues] = useState<AchievementPayload>({
    title: "",
    description: "",
    category: "",
    cover_photo: "",
    gallery_photos: [],
    youtube_link: "",
    status: "published",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Local file selections + previews
  const [coverFile, setCoverFile] = useState<File | null>(null);
  const [coverPreview, setCoverPreview] = useState<string>("");
  const [galleryFiles, setGalleryFiles] = useState<File[]>([]);
  const [galleryPreviews, setGalleryPreviews] = useState<string[]>([]);

  const getImageService = (imagePath: string | undefined, defaultImage: string = "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}/storage${imagePath}`;
    return `${API_BASE_URL}/storage/${imagePath}`;
  };

  useEffect(() => {
    if (open) {
      setError(null);
      if (mode === "edit" && initialAchievement) {
        const existingGallery = (initialAchievement.gallery_photos || []).filter(Boolean) as string[];
        setValues({
          title: initialAchievement.title ?? "",
          description: initialAchievement.description ?? "",
          category: initialAchievement.category ?? "",
          cover_photo: initialAchievement.cover_photo ?? "",
          gallery_photos: existingGallery,
          youtube_link: initialAchievement.youtube_link ?? "",
          status: initialAchievement.status ?? "published",
        });
        setCoverFile(null);
        setCoverPreview(initialAchievement.cover_photo || "");
        setGalleryFiles([]);
        setGalleryPreviews(existingGallery);
      } else {
        setValues({
          title: "",
          description: "",
          category: "",
          cover_photo: "",
          gallery_photos: [],
          youtube_link: "",
          status: "published",
        });
        setCoverFile(null);
        setCoverPreview("");
        setGalleryFiles([]);
        setGalleryPreviews([]);
      }
    }
  }, [open, mode, initialAchievement]);

  const galleryText = useMemo(() => (values.gallery_photos || []).join("\n"), [values.gallery_photos]);

  const onPickCover: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const file = e.target.files?.[0] || null;
    setCoverFile(file);
    if (file) {
      const url = URL.createObjectURL(file);
      setCoverPreview(url);
    } else {
      setCoverPreview(values.cover_photo || "");
    }
  };

  const onPickGallery: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const files = Array.from(e.target.files || []);
    setGalleryFiles(files);
    const urls = files.map((f) => URL.createObjectURL(f));
    setGalleryPreviews(urls.length ? urls : (values.gallery_photos || []));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isAuthenticated) {
      setError("You must be logged in.");
      return;
    }

    if (!values.title?.trim()) {
      setError("Title is required");
      return;
    }
    if (!coverFile && !values.cover_photo?.trim()) {
      setError("Cover photo is required");
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const endpoint =
        mode === "edit" && initialAchievement?.id
          ? `${API_BASE_URL}/api/dashboard/projects/${initialAchievement.id}`
          : `${API_BASE_URL}/api/dashboard/projects`;

      const methode = mode === "edit" ? "POST" : "POST";

      const form = new FormData();
      form.append("title", values.title.trim());
      if (values.description) form.append("description", values.description.trim());
      if (values.category) form.append("category", values.category.trim());
      if (values.youtube_link) form.append("youtube_link", values.youtube_link.trim());
      if (values.status) form.append("status", values.status.trim());

      if (coverFile) {
        form.append("coverPhoto", coverFile);
      }
      // else if (values.cover_photo) {
      //   form.append("coverPhoto", values.cover_photo);
      // }

      if (galleryFiles.length > 0) {
        galleryFiles.forEach((f) => form.append("galleryPhotos[]", f));
      }

      if (mode === "edit" && initialAchievement?.id) {
        form.append('_method', 'PUT');
      }
      // (values.gallery_photos || []).forEach((p) => {
      //   if (p && p.trim().length > 0) form.append("gallery_photos[]", p.trim());
      // });

      const res = await fetch(endpoint, {
        method: methode,
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: form,
      });
      if (!res.ok) {
        const text = await res.text();
        throw new Error(text || "Request failed");
      }
      onCompleted?.();
      onClose();
    } catch (err: any) {
      setError(err?.message || "Failed to submit");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose} title={title} maxWidthClass="max-w-3xl">
      {!isAuthenticated && (
        <div className="mb-3 text-sm text-red-600">You must be logged in to create or edit a work.</div>
      )}
      {error && <div className="mb-3 text-sm text-red-600">{error}</div>}
      <div style={{ maxHeight: "70vh", overflowY: "auto" }}>
        <form onSubmit={handleSubmit} className="mt-2 space-y-6">
          {/* Title */}
          <div className="space-y-2">
            <label htmlFor="ach-title" className="block"
              style={{
                fontSize: "16px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 600,
                color: "#3d3d3dff"
              }}
            >
              Title
            </label>
            <input
              id="ach-title"
              type="text"
              placeholder="Title"
              autoComplete="off"
              className="w-full rounded-xl border border-gray-300 bg-white px-4 py-3 outline-none "
              value={values.title}
              onChange={(e) => setValues((v) => ({ ...v, title: e.target.value }))}
              required
              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"
              }}
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <label htmlFor="ach-title" className="block"
              style={{
                fontSize: "16px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 600,
                color: "#3d3d3dff"
              }}

            >
              Description (optional)
            </label>
            <textarea
              id="ach-desc"
              rows={4}
              className="w-full rounded-xl border border-gray-300 bg-gray-50 px-4 py-3 outline-none  placeholder:text-gray-400"
              value={values.description || ""}
              onChange={(e) => setValues((v) => ({ ...v, description: e.target.value }))}
              placeholder="Describe your work..."
              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"
              }}
            />
          </div>

          {/* Category */}
          <div className="space-y-2">
            <label htmlFor="ach-category" className="block text-sm font-medium">
              Category (optional)
            </label>
            <select
              id="ach-category"
              className="w-full rounded-xl border border-gray-300 bg-white px-4 py-3 outline-none  "
              value={values.category || ""}
              onChange={(e) => setValues((v) => ({ ...v, category: e.target.value }))}
              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"
              }}
            >
              <option value="">Select a category</option>
              {CATEGORIES.map((c) => (
                <option key={c} value={c}>{c}</option>
              ))}
            </select>
          </div>

          {/* Cover photo picker */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">Cover photo</label>
            <input type="file" accept="image/*" onChange={onPickCover}
              className="w-full rounded-2xl"
              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"
              }}

            />
            {(!coverPreview && values.cover_photo) && (
              <div className="text-xs text-gray-500">Existing: {values.cover_photo}</div>
            )}
            {coverPreview && (
              <div className="mt-2">
                <img src={coverPreview} alt="cover preview" className="rounded-md max-h-48 object-cover" />
              </div>
            )}
          </div>

          {/* Gallery photos picker */}
          <div className="space-y-2">
            <label className="block text-sm font-medium">Gallery photos</label>
            <input type="file" accept="image/*" multiple onChange={onPickGallery}
              className="w-full rounded-2xl"
              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"
              }}
            />
            {(values.gallery_photos && values.gallery_photos.length > 0 && galleryFiles.length === 0) && (
              <div className="text-xs text-gray-500 whitespace-pre-wrap">Existing paths:\n{galleryText}</div>
            )}
            {galleryPreviews.length > 0 && (
              <div className="mt-2 grid grid-cols-3 gap-2">
                {galleryPreviews.map((src, idx) => (
                  <img key={idx} src={src} alt={`gallery-${idx}`} className="rounded-md h-24 w-full object-cover" />
                ))}
              </div>
            )}
          </div>

          {/* YouTube link */}
          <div className="space-y-2">
            <label htmlFor="ach-youtube" className="block text-sm font-medium">
              YouTube link (optional)
            </label>
            <input
              id="ach-youtube"
              type="text"
              className="w-full rounded-xl border border-gray-300 bg-white px-4 py-3 outline-none  "
              value={values.youtube_link || ""}
              onChange={(e) => setValues((v) => ({ ...v, youtube_link: e.target.value }))}
              placeholder="https://youtu.be/..."
              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"
              }}
            />
          </div>

          {/* Status */}
          <div className="space-y-2">
            <label htmlFor="ach-status" className="block text-sm font-medium">
              Status (optional)
            </label>
            <select
              id="ach-status"
              className="w-full rounded-xl border border-gray-300 bg-white px-4 py-3 outline-none  "
              value={values.status || ""}
              onChange={(e) => setValues((v) => ({ ...v, status: e.target.value }))}
              style={{
                fontSize: "14px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
                color: "#3d3d3dff",
                backgroundColor: "#F6F7F8"
              }}
            >
              <option value="published">published</option>
              <option value="draft">draft</option>
              <option value="archived">archived</option>
            </select>
          </div>

          {/* Submit */}
          <div className="pt-1">
            <button
              type="submit"
              disabled={loading || !isAuthenticated}
              className="w-full rounded-xl bg-[#0D63F3] px-6 py-3 text-white  tracking-wide disabled:opacity-70"
              style={{
                fontSize: "18px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 400,
                borderRadius: "14px"
              }}
            >
              {loading ? "Saving..." : mode === "edit" ? "Save changes" : "Create work"}
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
