// 🎯 Objectif : Fusionner les fonctionnalités de l'ancien composant "OfferDiscussionPanel" avec le nouveau style "MessageContent"

import React, { useState, useEffect, useRef } from 'react';
import { API_BASE_URL } from './../config';
import { useNotifications } from './notifications/NotificationContext';
import { Send, Loader2 } from 'lucide-react';
interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  email_verified_at: string;
  is_professional: boolean;
  created_at: string;
  updated_at: string;
  profile_completed: boolean;
}

interface FileData {
  id: number;
  original_name: string;
  filename: string;
  mime_type: string;
  size: number;
  human_size: string;
  extension: string;
  storage_type: string;
  status: string;
  download_url: string;
  local_path?: string;
  swisstransfer_url?: string;
  swisstransfer_download_url?: string;
  expires_at: string | null;
  is_expired: boolean;
  created_at: string;
  updated_at: string;
  localUrl?: string; // Pour les fichiers optimistes (upload en cours)
}

interface Message {
  id: number;
  open_offer_id: number;
  sender_id: number;
  receiver_id: number;
  message_text: string;
  created_at: string;
  updated_at: string;
  sender: User;
  receiver: User;
  files?: FileData[];
}

// interface Message {
//   id: number;
//   sender_id: number;
//   sender_name: string;
//   sender_avatar?: string;
//   sender_type: 'client' | 'professional';
//   message_text: string;
//   attachments?: string[];
//   created_at: string;
// }

interface OfferDiscussionPanelProps {
  offerId?: number;
  offerTitle?: string;
  clientId?: number;
  clientName?: string;
  clientAvatar?: string;
  professionalId?: number;
  professionalName?: string;
  professionalAvatar?: string;
  isClient?: boolean;
  onBack?: () => void;
  selectedFiles: File[];
  setSelectedFiles: React.Dispatch<React.SetStateAction<File[]>>;
}
const MessageContent: React.FC<OfferDiscussionPanelProps> = ({
  offerId,
  offerTitle,
  clientId,
  clientName = "Client",  // Valeur par défaut pour éviter les erreurs
  clientAvatar,
  professionalId,
  professionalName = "Professionnel",  // Valeur par défaut pour éviter les erreurs
  professionalAvatar,
  isClient,
  onBack,
  selectedFiles,
  setSelectedFiles,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { addOfferNotification } = useNotifications();
  const [lastMessageId, setLastMessageId] = useState<number | null>(null);
  const [pendingMessages, setPendingMessages] = useState<any[]>([]);

  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');

  // Fonction pour construire l'URL de l'image
  const getImageUrl = (file: FileData): string => {
    // Si l'URL de téléchargement est déjà disponible, l'utiliser
    if (file.download_url) {
      return file.download_url;
    }
    
    // Sinon, construire l'URL manuellement pour les fichiers locaux
    if (file.storage_type === 'local' && file.local_path) {
      return `${API_BASE_URL}/storage/${file.local_path}`;
    }
    
    // Pour les fichiers SwissTransfer
    if (file.storage_type === 'swisstransfer' && file.swisstransfer_download_url) {
      return file.swisstransfer_download_url;
    }
    
    return '';
  };

  // Récupérer les messages
  // Fonction utilitaire pour récupérer les messages
  const fetchMessages = async (sinceId: number | null = null) => {
    if (!token || !offerId) return;
    setLoading(true);
    try {
      let url = `${API_BASE_URL}/api/open-offers/${offerId}/messages`;
      const params = new URLSearchParams();
      if (!isClient) {
        params.append('professional_id', String(currentUser.id));
      }
      if (sinceId !== null) {
        params.append('since_id', String(sinceId));
      }
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des messages');
      }
      const data = await response.json();
      if (data.messages && data.messages.length > 0) {
        setMessages(data.messages); // On remplace toute la liste pour éviter les doublons/optimistes
        setLastMessageId(data.messages[data.messages.length - 1].id);
        // Supprimer les messages optimistes qui ont été validés par l'API
        setPendingMessages((prev) => prev.filter((pending) => {
          // On considère qu'un message est validé s'il existe dans la liste officielle avec même texte, sender et pas d'erreur
          return !data.messages.some((msg: any) =>
            msg.message_text === pending.message_text &&
            msg.sender_id === pending.sender_id &&
            !pending.error
          );
        }));
      }
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de charger les messages');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMessages();
    const intervalId = setInterval(() => {
      fetchMessages(lastMessageId);
    }, 5000);
    return () => clearInterval(intervalId);
  }, [offerId, token, isClient, clientId, clientName, clientAvatar, professionalId, professionalName, professionalAvatar]);

  useEffect(() => {
    // Réinitialiser l'erreur quand un professionnel est sélectionné
    if (isClient && professionalId !== undefined && professionalId !== null) {
      setError(null);
    }
  }, [professionalId, isClient]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setSelectedFiles((prev) => [
        ...prev,
        ...Array.from(files).filter(
          (file) => !prev.some((f) => f.name === file.name && f.size === file.size)
        ),
      ]);
      e.target.value = '';
    }
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() && selectedFiles.length === 0) return;
    if (!token || !offerId) return;
    if (isClient && (professionalId === undefined || professionalId === null)) {
      setError("Veuillez sélectionner un professionnel avant d'envoyer un message.");
      return;
    }
    setSending(true);
    setError(null);

    // Création du message optimiste
    const optimisticId = 'pending-' + Date.now();
    const optimisticMessage = {
      id: optimisticId,
      open_offer_id: offerId,
      sender_id: currentUser.id,
      receiver_id: isClient ? professionalId : clientId,
      message_text: newMessage,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sender: currentUser,
      receiver: {},
      files: selectedFiles.map((file) => ({
        id: 'pending-file-' + file.name + file.size,
        original_name: file.name,
        mime_type: file.type,
        isPending: true,
        // Pour l'affichage local
        localUrl: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
        extension: file.name.split('.').pop(),
      })),
      isPending: true,
      error: null,
    };
    setPendingMessages((prev) => [...prev, optimisticMessage]);
    setNewMessage('');
    setSelectedFiles([]);

    try {
      // Création du message côté serveur
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const body: { message_text: string; receiver_id?: number } = { message_text: newMessage };
      if (isClient && professionalId !== undefined) {
        body.receiver_id = professionalId;
      }
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${offerId}/messages`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      const data = await response.json();
      if (!data.message || !data.message.id) {
        throw new Error('Erreur lors de la création du message');
      }
      const messageId = data.message.id;
      // Upload des fichiers
      if (selectedFiles.length > 0) {
        const formData = new FormData();
        selectedFiles.forEach((file: File) => formData.append('files[]', file));
        formData.append('message_id', messageId);
        const uploadResponse = await fetch(`${API_BASE_URL}/api/files/upload`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        });
        const uploadData = await uploadResponse.json();
        if (!uploadData.success) {
          throw new Error('Erreur lors de l\'upload des fichiers: ' + (uploadData.message || ''));
        }
      }
      // Rafraîchir les messages
      await fetchMessages();
      // Vider le champ message uniquement si PAS de pièce jointe
      if (selectedFiles.length === 0) {
        setNewMessage('');
      }
      setSelectedFiles([]);
      setPendingMessages((prev) => prev.filter((m) => m.id !== optimisticId));
      addOfferNotification('offer_message', {
        offer_id: offerId,
        offer_title: offerTitle||"",
        message: newMessage,
        ...(isClient
          ? { client_id: user.id, client_name: clientName, client_avatar: clientAvatar }
          : { professional_id: user.id, professional_name: professionalName, professional_avatar: professionalAvatar })
      });
    } catch (err) {
      // Afficher l'erreur sur le message optimiste
      setPendingMessages((prev) => prev.map((m) => m.id === optimisticId ? { ...m, error: 'Échec de l\'envoi', isPending: false } : m));
      setError('Envoi échoué.');
    } finally {
      setSending(false);
    }
  };

  // Affichage debug pour vérifier la sélection de fichiers
  useEffect(() => {
    console.log('Fichiers sélectionnés:', selectedFiles);
  }, [selectedFiles]);

  // Filtrage des messages selon le professionnel sélectionné (pour le client)
  let filteredMessages = messages;
  if (isClient && professionalId) {
    filteredMessages = messages.filter(
      (msg) =>
        (msg.sender_id === professionalId && msg.receiver_id === clientId) ||
        (msg.sender_id === clientId && msg.receiver_id === professionalId)
    );
  }

  // Fusionner messages et pendingMessages pour l'affichage
  const allMessages = [...messages, ...pendingMessages];
  const filteredAllMessages = isClient && professionalId
    ? allMessages.filter(
        (msg) =>
          (msg.sender_id === professionalId && msg.receiver_id === clientId) ||
          (msg.sender_id === clientId && msg.receiver_id === professionalId)
      )
    : allMessages;

  return (
    <div style={{ flex: 1, minHeight: 500, padding: 32, background: '#fff' }}>
      <div style={{ textAlign: 'center', margin: '4px 0 8px 0' }}>
        <div style={{ fontWeight: 600, fontSize: 14 }}>You sent a new project request. ssss</div>
        <div style={{ color: '#888', fontSize: 12, marginTop: 2 }}>5:45PM</div>
      </div>
{/* 
      <div style={{ background: '#faf5ff', borderRadius: 16, border: '1px solid #eee', padding: 32, marginBottom: 24 }}>
        <div style={{ background: '#fff', borderRadius: 12, padding: 28 }}>
          <div className="mb-6">
            <div className="font-semibold mb-2">Project Overview</div>
            <div className="text-[13px] text-[#222]">Your project description here...</div>
          </div>
        </div>
      </div> */}

      <div style={{ flex: 1, overflowY: 'auto', marginBottom: 16, maxHeight: '60vh' }}>
        {filteredAllMessages.length === 0 ? (
          <div style={{ color: '#aaa', textAlign: 'center', marginTop: 40 }}>
            Aucun message pour ce professionnel.
          </div>
        ) : (
          filteredAllMessages.map((msg) => {
            const isSent = msg.sender_id === currentUser.id;
            return (
              <div
                key={msg.id}
                style={{
                  display: 'flex',
                  justifyContent: isSent ? 'flex-end' : 'flex-start',
                  marginBottom: 12,
                }}
              >
                <div
                  style={{
                    background: isSent ? '#e5d5fa' : '#f3f0fa',
                    color: '#222',
                    borderRadius: 16,
                    padding: '10px 16px',
                    maxWidth: '70%',
                    textAlign: 'left',
                    boxShadow: '0 1px 4px rgba(160,89,207,0.04)',
                    position: 'relative',
                  }}
                >
                  <div style={{ fontWeight: 'bold', fontSize: 12 }}>
                    {msg.sender?.first_name || 'Utilisateur'}
                  </div>
                  <div style={{ fontSize: 13, marginBottom: 4 }}>
                    {msg.message_text}
                  </div>
                  {/* Affichage des pièces jointes */}
                  {msg.files && msg.files.length > 0 && (
                    <div style={{ marginTop: 8, display: 'flex', flexDirection: 'column', gap: 8 }}>
                      {msg.files.map((file: FileData) => {
                        const isImage = file.mime_type?.startsWith('image/');
                        const getFileIcon = () => {
                          if (/pdf$/.test(file.original_name)) {
                            return (
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#a259cf"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">PDF</text></svg>
                            );
                          }
                          if (/docx?$/.test(file.original_name)) {
                            return (
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#4a90e2"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">DOC</text></svg>
                            );
                          }
                          if (/xlsx?$/.test(file.original_name)) {
                            return (
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#27ae60"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">XLS</text></svg>
                            );
                          }
                          if (/txt$/.test(file.original_name)) {
                            return (
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#f5a623"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">TXT</text></svg>
                            );
                          }
                          if (/zip$|rar$/.test(file.original_name)) {
                            return (
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#b07bfa"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">ZIP</text></svg>
                            );
                          }
                          return (
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#bbb"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">FILE</text></svg>
                          );
                        };

                        // Affichage spécial pour les images
                        if (isImage) {
                          const imageUrl = file.localUrl || getImageUrl(file);
                          console.log('File data:', file);
                          console.log('Download URL:', file.download_url);
                          console.log('Storage type:', file.storage_type);
                          console.log('Local path:', file.local_path);
                          console.log('Generated image URL:', imageUrl);
                          
                          return (
                            <div key={file.id} style={{ 
                              marginBottom: 8,
                              borderRadius: 16,
                              overflow: 'hidden',
                              background: '#fff',
                              border: '1px solid #e5d5fa',
                              boxShadow: '0 2px 8px rgba(160,89,207,0.08)'
                            }}>
                              <img
                                src={imageUrl}
                                alt={file.original_name}
                                style={{
                                  width: '100%',
                                  maxHeight: 180,
                                  borderRadius: 12,
                                  border: '1px solid #e5d5fa',
                                  cursor: 'pointer',
                                  objectFit: 'cover'
                                }}
                                onClick={() => window.open(imageUrl, '_blank')}
                                onError={(e) => {
                                  console.error('Erreur de chargement de l\'image:', file.original_name);
                                  console.error('URL tentée:', imageUrl);
                                  console.error('File data complet:', file);
                                  e.currentTarget.style.display = 'none';
                                }}
                                onLoad={() => {
                                  console.log('Image chargée avec succès:', file.original_name);
                                }}
                              />
                              <div style={{ 
                                padding: '8px 12px',
                                background: '#faf5ff',
                                borderTop: '1px solid #e5d5fa'
                              }}>
                                <div style={{ 
                                  fontSize: 12, 
                                  color: '#666',
                                  fontWeight: 500,
                                  textAlign: 'center'
                                }}>
                                  {file.original_name}
                                </div>
                              </div>
                            </div>
                          );
                        }

                        // Affichage pour les fichiers non-images (PDF, ZIP, etc.)
                        return (
                          <a
                            key={file.id}
                            href={file.download_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              background: '#fff',
                              border: '1px solid #e5d5fa',
                              borderRadius: 12,
                              padding: '8px 12px',
                              textDecoration: 'none',
                              color: '#222',
                              fontSize: 12,
                              maxWidth: 250,
                              minWidth: 0,
                              marginBottom: 4,
                            }}
                          >
                            <span style={{ marginRight: 10 }}>{getFileIcon()}</span>
                            <span style={{ 
                              whiteSpace: 'nowrap', 
                              overflow: 'hidden', 
                              textOverflow: 'ellipsis',
                              flex: 1
                            }}>
                              {file.original_name}
                            </span>
                          </a>
                        );
                      })}
                    </div>
                  )}
                  {/* Affichage erreur et bouton réessayer */}
                  {/* Supprimer ce bloc dans l'affichage des messages : */}
                  {/* {msg.error && (
                    <div style={{ color: 'red', fontSize: 12, marginTop: 6 }}>
                      {msg.error}
                      <button style={{ marginLeft: 8, color: '#a259cf', background: 'none', border: 'none', cursor: 'pointer', fontWeight: 'bold' }} onClick={() => {
                        setPendingMessages((prev) => prev.filter((m) => m.id !== msg.id));
                        setNewMessage(msg.message_text);
                        // Correction linter : désactive l'avertissement pour l'utilisation de any sur f
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        setSelectedFiles(msg.files.map((f: any) => f.file).filter(Boolean));
                      }}>Réessayer</button>
                    </div>
                  )} */}
                  <div style={{ fontSize: 11, color: '#aaa', textAlign: 'right', display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 8 }}>
                    {msg.created_at && !isNaN(new Date(msg.created_at).getTime())
                      ? new Date(msg.created_at).toLocaleTimeString()
                      : ''}
                    {msg.isPending && (
                      <span style={{ fontSize: 11, color: '#bbb', marginLeft: 8 }}>
                        Envoi en cours...
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Affichage de l'erreur */}
      {error && (
        <div style={{ color: 'red', marginBottom: 8 }}>
          {error}
        </div>
      )}

      {/* Affichage des fichiers sélectionnés (juste au-dessus du champ d'input) */}
      {selectedFiles.length > 0 && (
        <div style={{ marginBottom: 12, background: '#f8f4ff', borderRadius: 12, padding: '10px 12px', display: 'flex', flexWrap: 'wrap', gap: 10, minHeight: 48 }}>
          {selectedFiles.map((file, idx) => {
            const isImage = file.type.startsWith('image/');
            // Icônes SVG pour les types connus
            const getIcon = () => {
              if (/pdf$/.test(file.name)) {
                return (
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#a259cf"/><text x="12" y="17" textAnchor="middle" fontSize="9" fill="#fff" fontFamily="Arial" fontWeight="bold">PDF</text></svg>
                );
              }
              if (/docx?$/.test(file.name)) {
                return (
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#4a90e2"/><text x="12" y="17" textAnchor="middle" fontSize="9" fill="#fff" fontFamily="Arial" fontWeight="bold">DOC</text></svg>
                );
              }
              if (/xlsx?$/.test(file.name)) {
                return (
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#27ae60"/><text x="12" y="17" textAnchor="middle" fontSize="9" fill="#fff" fontFamily="Arial" fontWeight="bold">XLS</text></svg>
                );
              }
              if (/txt$/.test(file.name)) {
                return (
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#f5a623"/><text x="12" y="17" textAnchor="middle" fontSize="9" fill="#fff" fontFamily="Arial" fontWeight="bold">TXT</text></svg>
                );
              }
              if (/zip$|rar$/.test(file.name)) {
                return (
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#b07bfa"/><text x="12" y="17" textAnchor="middle" fontSize="9" fill="#fff" fontFamily="Arial" fontWeight="bold">ZIP</text></svg>
                );
              }
              return (
                <svg width="28" height="28" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#bbb"/><text x="12" y="17" textAnchor="middle" fontSize="9" fill="#fff" fontFamily="Arial" fontWeight="bold">FILE</text></svg>
              );
            };

            // Affichage spécial pour les images avant envoi (miniature)
            if (isImage) {
              return (
                <div key={idx} style={{ 
                  position: 'relative',
                  borderRadius: 10,
                  overflow: 'hidden',
                  background: '#fff',
                  border: '1px solid #e5d5fa',
                  width: 70,
                  height: 70,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 1px 4px rgba(160,89,207,0.06)'
                }}>
                  <img
                    src={URL.createObjectURL(file)}
                    alt={file.name}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      display: 'block'
                    }}
                  />
                  <button
                    type="button"
                    onClick={() => handleRemoveFile(idx)}
                    style={{ 
                      position: 'absolute',
                      top: 2,
                      right: 2,
                      background: 'rgba(255,255,255,0.8)',
                      border: 'none',
                      color: '#a259cf',
                      cursor: 'pointer',
                      fontWeight: 'bold',
                      fontSize: 16,
                      lineHeight: 1,
                      borderRadius: '50%',
                      width: 20,
                      height: 20,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      padding: 0
                    }}
                    aria-label="Supprimer le fichier"
                  >
                    ×
                  </button>
                </div>
              );
            }

            // Affichage pour les fichiers non-images avant envoi (miniature)
            return (
              <div key={idx} style={{ 
                background: '#fff', 
                border: '1px solid #e5d5fa', 
                borderRadius: 10, 
                width: 70, 
                height: 70, 
                display: 'flex', 
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                boxShadow: '0 1px 4px rgba(160,89,207,0.06)'
              }}>
                <span style={{ marginRight: 0 }}>{getIcon()}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveFile(idx)}
                  style={{ 
                    position: 'absolute',
                    top: 2,
                    right: 2,
                    background: 'rgba(255,255,255,0.8)',
                    border: 'none',
                    color: '#a259cf',
                    cursor: 'pointer',
                    fontWeight: 'bold',
                    fontSize: 16,
                    lineHeight: 1,
                    borderRadius: '50%',
                    width: 20,
                    height: 20,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: 0
                  }}
                  aria-label="Supprimer le fichier"
                >
                  ×
                </button>
              </div>
            );
          })}
        </div>
      )}

      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            background: '#fff',
            border: '1.5px solid #e5d5fa',
            borderRadius: 28,
            padding: '8px 18px',
            flex: 1,
            boxShadow: '0 1px 4px rgba(160,89,207,0.04)',
            position: 'relative',
          }}
        >
          {/* Trombone à gauche */}
          <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer', marginRight: 8 }}>
            <input
              type="file"
              multiple
              style={{ display: 'none' }}
              onChange={handleFileChange}
            />
            {/* Icône trombone SVG */}
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#a259cf" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21.44 11.05l-9.19 9.19a5 5 0 01-7.07-7.07l9.19-9.19a3 3 0 014.24 4.24l-9.2 9.19a1 1 0 01-1.41-1.41l9.2-9.19"/>
            </svg>
          </label>
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={(e) => { if (e.key === 'Enter') handleSendMessage(); }}
            placeholder="Write your message..."
            style={{ flex: 1, border: 'none', outline: 'none', background: 'transparent', fontSize: 15 }}
          />
          <button
            onClick={handleSendMessage}
            disabled={sending || (!newMessage.trim() && selectedFiles.length === 0)}
            style={{ width: 36, height: 36, borderRadius: '50%', background: '#f3f0fa', border: 'none', display: 'flex', alignItems: 'center', justifyContent: 'center', cursor: 'pointer' }}
            aria-label="Send message"
          >
            <Send size={18} stroke="#a259cf" strokeWidth={2.2} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default MessageContent;




// const MessageContent = () => {
//   return (
//     <div style={{ flex: 1, minHeight: 500, padding: 32, background: '#fff' }}>
//       {/* Bloc info envoi de projet */}
//       <div style={{ textAlign: 'center', margin: '4px 0 8px 0' }}>
//         <div style={{ fontWeight: 600, fontSize: 14 }}>You sent a new project request.</div>
//         <div style={{ color: '#888', fontSize: 12, marginTop: 2 }}>5:45PM</div>
//       </div>
//       {/* Bloc Project Request stylisé */}
//       <div style={{ background: 'rgb(250 245 255 / var(--tw-bg-opacity, 1))',  borderRadius: 16, border: '1px solid #eee', padding: 32, marginBottom: 24, boxShadow: '0 2px 8px rgba(60,30,90,0.03)' }}>
//         <div className="flex items-center mb-6">
//           {/* Icône bulle dans un cercle violet */}
//           <span className="flex items-center justify-center mr-3" style={{ width: 36, height: 36, borderRadius: '50%', background: '#a259cf1a' }}>
//             <svg width="22" height="22" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" stroke="#a259cf" strokeWidth="2" fill="none"/><path d="M8 10h8M8 14h5" stroke="#a259cf" strokeWidth="2" strokeLinecap="round"/></svg>
//           </span>
//           <span className="font-bold text-lg text-[#222] mr-2">Project Request</span>
//           <span className="ml-1 text-gray-400 cursor-pointer" title="Info">
//             <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="12" cy="12" r="10" stroke="#bbb" strokeWidth="2" fill="none"/><text x="12" y="16" textAnchor="middle" fontSize="13" fill="#bbb" fontFamily="Arial" fontWeight="bold">i</text></svg>
//           </span>
//         </div>
//         <div style={{background: '#fff' , borderRadius: 12, padding: 28 }}>
//           <div className="mb-6">
//             <div className="font-semibold mb-2">Project Overview</div>
//             <div className="text-[13px] text-[#222]">Hi-Render is a specialized 3D visualization studio working with architects, interior designers, and developers. We are seeking a web agency or freelance web developer to redesign and develop our new website: www.hi-render.com. The goal is to create a sleek, minimal, and professional digital presence that showcases our portfolio, communicates our services clearly, and supports lead generation.</div>
//           </div>
//           <div>
//             <div className="font-semibold mb-2">Objectives</div>
//             <div className="text-[13px] text-[#222] mb-1">Reflect our brand identity: minimal, precise, architectural.</div>
//             <div className="text-[13px] text-[#222]">Improve user experience and site navigation.</div>
//           </div>
//         </div>
//       </div>
//       {/* Nouvelle barre d'envoi de message stylisée avec icônes à l'extérieur */}
//       <div style={{ display: 'flex', alignItems: 'center', marginTop: 'auto' }}>
//         {/* Icône caméra */}
//         <button
//           type="button"
//           style={{
//             width: 36,
//             height: 36,
//             borderRadius: '50%',
//             background: 'rgba(160,89,207,0.07)',
//             border: 'none',
//             display: 'flex',
//             alignItems: 'center',
//             justifyContent: 'center',
//             marginRight: 8,
//             outline: 'none',
//             cursor: 'pointer',
//           }}
//           aria-label="Send video"
//         >
//           <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#a259cf" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="2" y="7" width="15" height="10" rx="2"/><path d="M17 8l5 4-5 4V8z"/></svg>
//         </button>
//         {/* Icône trombone */}
//         <button
//           type="button"
//           style={{
//             width: 36,
//             height: 36,
//             borderRadius: '50%',
//             background: 'rgba(160,89,207,0.07)',
//             border: 'none',
//             display: 'flex',
//             alignItems: 'center',
//             justifyContent: 'center',
//             marginRight: 16,
//             outline: 'none',
//             cursor: 'pointer',
//           }}
//           aria-label="Attach file"
//         >
//           <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#a259cf" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21.44 11.05l-9.19 9.19a5 5 0 01-7.07-7.07l9.19-9.19a3 3 0 014.24 4.24l-9.2 9.19a1 1 0 01-1.41-1.41l9.2-9.19"/></svg>
//         </button>
//         {/* Barre d'envoi (input + flèche) */}
//         <div
//           style={{
//             display: 'flex',
//             alignItems: 'center',
//             background: '#fff',
//             border: '1.5px solid #e5d5fa',
//             borderRadius: 28,
//             padding: '8px 18px',
//             flex: 1,
//             boxShadow: '0 1px 4px rgba(160,89,207,0.04)',
//           }}
//         >
//           {/* Bouton emoji */}
//           <button
//             type="button"
//             style={{
//               width: 28,
//               height: 28,
//               borderRadius: '50%',
//               background: 'none',
//               border: 'none',
//               display: 'flex',
//               alignItems: 'center',
//               justifyContent: 'center',
//               marginRight: 8,
//               cursor: 'pointer',
//               fontSize: 20,
//               color: '#a259cf',
//             }}
//             aria-label="Add emoji"
//           >
//             {/* Icône smiley SVG */}
//             <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="#a259cf" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
//               <circle cx="12" cy="12" r="10" />
//               <path d="M8 15s1.5 2 4 2 4-2 4-2" />
//               <line x1="9" y1="9" x2="9.01" y2="9" />
//               <line x1="15" y1="9" x2="15.01" y2="9" />
//             </svg>
//           </button>
//           <input
//             type="text"
//             placeholder="Write your message..."
//             style={{
//               flex: 1,
//               border: 'none',
//               outline: 'none',
//               background: 'transparent',
//               fontSize: 15,
//               padding: '8px 0',
//               marginRight: 12,
//               color: '#222',
//             }}
//           />
//           {/* Bouton envoyer (flèche vers le haut) */}
//           <button
//             type="button"
//             style={{
//               width: 36,
//               height: 36,
//               borderRadius: '50%',
//               background: '#f3f0fa',
//               border: 'none',
//               display: 'flex',
//               alignItems: 'center',
//               justifyContent: 'center',
//               cursor: 'pointer',
//               transition: 'background 0.2s',
//             }}
//             aria-label="Send message"
//           >
//             {/* Flèche vers le haut */}
//             <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="#a259cf" strokeWidth="2.2" strokeLinecap="round" strokeLinejoin="round"><line x1="12" y1="19" x2="12" y2="5"/><polyline points="5 12 12 5 19 12"/></svg>
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default MessageContent; 