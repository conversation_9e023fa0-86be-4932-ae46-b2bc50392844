import React, { useState, useEffect } from "react";
import QuoteRequestModal from '../components/QuoteRequestModal';
import { useParams, useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';
import { getAllCategories } from '../data/categories'
import { Button } from './ui/buttons';
import EditProfileModal from "./EditProfileModal";

interface HeroProfileProps {
  isPro: boolean;
  pro_detail?: FreelanceProfile;
}

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  is_professional: boolean;
}

// Interface pour les éléments du portfolio
interface PortfolioItem {
  id?: number;
  path?: string;
  name?: string;
  type?: string;
  created_at?: string;
  // description?: string;
}

interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  phone: string;
  email?: string;
  address: string;
  city: string;
  country: string;
  skills: string[] | null;
  languages: string[] | null;
  availability_status: string | null;
  services_offered: string[];
  hourly_rate: string;
  completion_percentage: number;
  created_at: string;
  updated_at: string;
  avatar?: string;
  cover_photo?: string;
  profile_picture_path?: string;
  rating?: number;
  review_count?: number;
  bio?: string;
  title?: string;
  portfolio?: PortfolioItem[];
  user: User;
  likes_count?: number;
  views_count?: number;
}

interface Achievement {
  id: number;
  freelance_profile_id: number;
  title: string;
  organization: string;
  date_obtained: string;
  description: string;
  file_path: string | null;
  achievement_url: string | null;
  created_at: string;
  updated_at: string;
}

const HeroProfile: React.FC<HeroProfileProps> = ({ isPro, pro_detail }) => {
  const [isLargeScreen, setIsLargeScreen] = useState(false);

  const navigate = useNavigate();

  const [showQuoteModal, setShowQuoteModal] = useState(false);
  const [professional, setProfessional] = useState<FreelanceProfile | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [showEditProfileModal, setShowEditProfileModal] = useState(false);

  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  const getUrlProlfil = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  const getImageUrl = (imagePath: string | undefined, defaultImage: string = "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}/storage${imagePath}`;
    return `${API_BASE_URL}/storage/${imagePath}`;
  };

  useEffect(() => {
    const checkScreenSize = () => {
      setIsLargeScreen(window.innerWidth >= 1025);
    };

    // Vérifier la taille initiale
    checkScreenSize();

    // Écouter les changements de taille
    window.addEventListener("resize", checkScreenSize);

    // Nettoyer l'écouteur d'événement
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  useEffect(() => {
    if (pro_detail) {
      setProfessional(pro_detail);
      setLoading(false);
    }
  }, [pro_detail]);



  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  const handleBackToList = () => {
    // navigate('/lists-independants');
    navigate(-1);
  };

  if (error && !professional) {
    return (
      <div className="min-h-screen bg-white flex justify-center items-center">
        <div className="text-center p-6 bg-red-50 rounded-lg border border-red-200 max-w-md">
          {/* <h2 className="text-xl font-semibold text-red-700 mb-2">Erreur</h2>: */}
          {/* <p className="text-neutral-700">{error}</p> */}
          <p className="text-neutral-700">Chargement encores</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={handleBackToList}
          >
            Retour
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="bg-white w-full flex items-stretch">
        <div
          className={`w-full px-[10px] md:px-[40px] mx-auto grid ${isLargeScreen ? "grid-cols-2" : "grid-cols-1"
            } gap-10 items-end`} >
          {/* Left column - content */}
          <div className="flex flex-col justify-end text-left h-full w-full">
            {/* Container for profile image and title in column */}
            <div className="flex flex-col items-start mb-4 md:mb-5 lg:mb-6">
              <img
                src={getUrlProlfil(professional?.avatar)}
                alt={`${professional?.first_name} ${professional?.last_name}`}
                className="w-16 h-16 sm:w-18 sm:h-18 md:w-20 md:h-20 lg:w-22 lg:h-22 xl:w-24 xl:h-24 rounded-full object-cover mb-4 md:mb-5 lg:mb-6"
                style={{ maxWidth: "100px", maxHeight: "100px" }}
                onError={(e) => {
                  e.currentTarget.onerror = null;
                  e.currentTarget.src = 'https://hi-3d.com/wp-content/uploads/2025/08/Photo-de-profil-03.jpg';
                }}
              />
              <h1
                className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-normal text-left pb-1 md:pb-1.5 lg:pb-2"
                style={{
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 500,
                  lineHeight: "1em",
                  color: "#0D0C22",
                }}
              >
                {`${professional?.first_name} ${professional?.last_name}`}
              </h1>
              <h2
                className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-normal text-left mt-1 md:mt-1.5 lg:mt-2"
                style={{
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 500,
                  color: "#0D0C22",
                  lineHeight: "0.89em",
                  fontSize: "clamp(24px, 4vw, 56px)"
                }}
              >
                {professional?.title || 'Architecture visualisation'}
              </h2>
            </div>

            <p
              className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl mb-6 md:mb-7 lg:mb-8 xl:mb-10 font-normal text-left"
              style={{
                fontFamily: "'Inter', sans-serif",
                fontWeight: 400,
                lineHeight: "1.4em",
                color: "#0D0C22",
                fontSize: "clamp(14px, 3vw, 20px)"
              }}
            >
              I believe 3D is more than just a tool – it's a medium of expression,
              a language without words, and a bridge between imagination and
              reality.
            </p>

            {isPro === false && (
              <div className="flex items-center justify-start space-x-2 sm:space-x-3 md:space-x-3 lg:space-x-4 xl:space-x-4 flex-wrap gap-2">
                <button
                  type="button"
                  onClick={() => setShowQuoteModal(true)}
                  className="border-none rounded-full py-2 px-4 sm:py-2 sm:px-5 md:py-2.5 md:px-6 lg:py-3 lg:px-7 xl:py-3 xl:px-8 font-sans font-medium text-xs sm:text-sm md:text-base lg:text-base xl:text-base cursor-pointer transition-colors duration-200 shadow-sm"
                  style={{
                    fontFamily: "Arial, sans-serif",
                    backgroundColor: "#000",
                    color: "#fff",
                    whiteSpace: "nowrap"
                  }}
                  onMouseOver={(e) => (e.currentTarget.style.color = "#9ca3af")}
                  onMouseOut={(e) => (e.currentTarget.style.color = "#fff")}
                >
                  Let's make a deal
                </button>

                <button
                  type="button"
                  onClick={() => setShowQuoteModal(true)}
                  className="bg-transparent text-black rounded-full py-2 px-4 sm:py-2 sm:px-5 md:py-2.5 md:px-6 lg:py-3 lg:px-7 xl:py-3 xl:px-8 font-sans font-medium text-xs sm:text-sm md:text-base lg:text-base xl:text-base cursor-pointer transition-colors duration-200 shadow-sm hover:bg-black"
                  style={{
                    fontFamily: "Arial, sans-serif",
                    backgroundColor: "#f6f7f8",
                    whiteSpace: "nowrap"
                  }}
                  onMouseOver={(e) => (e.currentTarget.style.color = "#9ca3af")}
                  onMouseOut={(e) => (e.currentTarget.style.color = "#000")}
                >
                  Invite to an open offer
                </button>

                <div
                  className="flex items-center justify-center cursor-pointer transition-colors duration-200 rounded-xl"
                  style={{
                    width: "44px",
                    height: "36px",
                    backgroundColor: "#f6f7f8",
                  }}
                  onMouseOver={(e) =>
                    (e.currentTarget.style.backgroundColor = "#e5e7eb")
                  }
                  onMouseOut={(e) =>
                    (e.currentTarget.style.backgroundColor = "#f6f7f8")
                  }
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 29 29"
                    fill="none"
                  >
                    <g clipPath="url(#clip0_201_86853)">
                      <path
                        d="M5.0105 17.7471C5.0105 18.7432 5.67847 19.3995 6.73316 19.3995H13.073V24.8721C13.073 26.6651 13.823 28.1534 14.116 28.1534C14.3972 28.1534 15.1472 26.6651 15.1472 24.8721V19.3995H21.4871C22.5417 19.3995 23.2097 18.7432 23.2097 17.7471C23.2097 15.2745 21.2293 12.6612 17.9363 11.4659L17.5496 6.08691C19.2605 5.11426 20.6667 4.01269 21.2761 3.22754C21.5808 2.8291 21.7332 2.43066 21.7332 2.0791C21.7332 1.36426 21.1824 0.836914 20.3621 0.836914H7.86988C7.03784 0.836914 6.49878 1.36426 6.49878 2.0791C6.49878 2.43066 6.63941 2.8291 6.94409 3.22754C7.55347 4.01269 8.95972 5.11426 10.6707 6.08691L10.2839 11.4659C6.99097 12.6612 5.0105 15.2745 5.0105 17.7471Z"
                        fill="black"
                        fillOpacity="0.85"
                      />
                    </g>
                    <defs>
                      <clipPath id="clip0_201_86853">
                        <rect
                          width="18.1992"
                          height="28.043"
                          fill="white"
                          transform="translate(5.0105 0.110352)"
                        />
                      </clipPath>
                    </defs>
                  </svg>
                </div>
              </div>
            )}

            {isPro === true && (
              <div className="flex items-center justify-start space-x-2 sm:space-x-3 md:space-x-3 lg:space-x-4 xl:space-x-4 flex-wrap gap-2">
                <button
                  type="button"
                  onClick={() => setShowEditProfileModal(true)}
                  className="bg-transparent text-black rounded-full py-2 px-4 sm:py-2 sm:px-5 md:py-2.5 md:px-6 lg:py-3 lg:px-7 xl:py-3 xl:px-8 font-sans font-medium text-xs sm:text-sm md:text-base lg:text-base xl:text-base cursor-pointer transition-colors duration-200 shadow-sm hover:bg-black"
                  style={{
                    fontFamily: "Arial, sans-serif",
                    backgroundColor: "#f6f7f8",
                    whiteSpace: "nowrap"
                  }}
                  onMouseOver={(e) => (e.currentTarget.style.color = "#9ca3af")}
                  onMouseOut={(e) => (e.currentTarget.style.color = "#000")}
                >
                  Edit my profile
                </button>

                <div
                  className="flex items-center justify-center cursor-pointer transition-colors duration-200 rounded-xl"
                  style={{
                    width: "44px",
                    height: "36px",
                    backgroundColor: "#f6f7f8",
                  }}
                  onMouseOver={(e) =>
                    (e.currentTarget.style.backgroundColor = "#e5e7eb")
                  }
                  onMouseOut={(e) =>
                    (e.currentTarget.style.backgroundColor = "#f6f7f8")
                  }
                >
                  <p className="text-sm md:text-base">...</p>
                </div>
              </div>
            )}
          </div>

          {/* Right column - image (visible seulement à partir de 1025px) */}
          {isLargeScreen && (
            <div className="flex justify-end w-full h-[400px] lg:h-[500px]">
              <img
                src={getUrlProlfil(professional?.cover_photo) || "https://hi-3d.com/wp-content/uploads/2025/08/basel-lindenhofareal-miller-maranta-visualisierung-1-922x1024.jpg"}
                alt="3D Art"
                className="rounded-lg object-cover w-full h-full"
                onError={(e) => {
                  e.currentTarget.onerror = null; // empêche les boucles infinies
                  e.currentTarget.src = 'https://hi-3d.com/wp-content/uploads/2025/08/basel-lindenhofareal-miller-maranta-visualisierung-1-922x1024.jpg'; // chemin de l'image par défaut
                }}
              />
            </div>
          )}
        </div>
      </div>

      <QuoteRequestModal
        token={token}
        user={user}
        pro={professional}
        open={showQuoteModal}
        onOpenChange={setShowQuoteModal}
      />

      <EditProfileModal
        open={showEditProfileModal}
        onClose={() => setShowEditProfileModal(false)}
        onSaved={(p) => {
          // rafraîchir l’écran si besoin
          window.location.reload();
          console.log("profile saved", p);
        }}
      />

    </>
  );
};

export default HeroProfile;