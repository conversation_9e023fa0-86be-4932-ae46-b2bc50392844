import React, { useState, useRef, useEffect } from 'react';
import { Send } from 'lucide-react';
import { API_BASE_URL } from './../config';
import { useNotifications } from './notifications/NotificationContext';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  email_verified_at: string;
  is_professional: boolean;
  created_at: string;
  updated_at: string;
  profile_completed: boolean;
}

interface FileData {
  id: number;
  original_name: string;
  filename: string;
  mime_type: string;
  size: number;
  human_size: string;
  extension: string;
  storage_type: string;
  status: string;
  download_url: string;
  local_path?: string;
  swisstransfer_url?: string;
  swisstransfer_download_url?: string;
  expires_at: string | null;
  is_expired: boolean;
  created_at: string;
  updated_at: string;
  localUrl?: string;
}

interface Message {
  id: number;
  open_offer_id: number;
  sender_id: number;
  receiver_id: number;
  message_text: string;
  created_at: string;
  updated_at: string;
  sender: User;
  receiver: User;
  files?: FileData[];
}

interface OfferDiscussionPanelProps {
  offerId?: number;
  offerTitle?: string;
  clientId?: number;
  clientName?: string;
  clientAvatar?: string;
  professionalId?: number;
  professionalName?: string;
  professionalAvatar?: string;
  isClient?: boolean;
  onBack?: () => void;
  selectedFiles: File[];
  setSelectedFiles: React.Dispatch<React.SetStateAction<File[]>>;
}

const MessageContent_mobile_new: React.FC<OfferDiscussionPanelProps> = ({
  offerId,
  offerTitle,
  clientId,
  clientName = "Client",
  clientAvatar,
  professionalId,
  professionalName = "Professionnel",
  professionalAvatar,
  isClient,
  selectedFiles,
  setSelectedFiles,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { addOfferNotification } = useNotifications();
  const [lastMessageId, setLastMessageId] = useState<number | null>(null);
  const [pendingMessages, setPendingMessages] = useState<any[]>([]);
  const [isInputFocused, setIsInputFocused] = useState(false);

  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');

  const getImageUrl = (file: FileData): string => {
    if (file.download_url) {
      return file.download_url;
    }
    
    if (file.storage_type === 'local' && file.local_path) {
      return `${API_BASE_URL}/storage/${file.local_path}`;
    }
    
    if (file.storage_type === 'swisstransfer' && file.swisstransfer_download_url) {
      return file.swisstransfer_download_url;
    }
    
    return '';
  };

  const mergeMessagesNonDestructive = (existing: Message[], incoming: Message[]) => {
    const byId = new Map<number, Message>();
    for (const m of existing) {
      if (typeof m.id === 'number') byId.set(m.id, m);
    }
    for (const m of incoming) {
      byId.set(m.id, m);
    }
    const merged = Array.from(byId.values()).sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());
    return merged;
  };

  const fetchMessages = async (sinceId: number | null = null, options: { background?: boolean } = {}) => {
    if (!token || !offerId) return;
    if (sending && options.background) return; // ne pas poller pendant un envoi
    if (!options.background) setLoading(true);
    try {
      let url = `${API_BASE_URL}/api/open-offers/${offerId}/messages`;
      const params = new URLSearchParams();
      if (!isClient) {
        params.append('professional_id', String(currentUser.id));
      }
      if (sinceId !== null) {
        params.append('since_id', String(sinceId));
      }
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des messages');
      }
      const data = await response.json();
      if (data.messages && data.messages.length > 0) {
        setLastMessageId(data.messages[data.messages.length - 1].id);
        setMessages(prev => {
          const base = mergeMessagesNonDestructive(prev.filter(m => !(m as any).isPending) as Message[], data.messages);
          // On conserve les messages optimistes
          const pending = prev.filter(m => (m as any).isPending);
          return [...base, ...pending];
        });
        // Nettoyage des pendings validés
        setPendingMessages((prev) => prev.filter((pending) => {
          return !data.messages.some((msg: any) =>
            msg.message_text === pending.message_text &&
            msg.sender_id === pending.sender_id &&
            !pending.error
          );
        }));
      }
    } catch (err) {
      console.error('Erreur:', err);
      if (!options.background) setError('Impossible de charger les messages');
    } finally {
      if (!options.background) {
        setLoading(false);
      }
    }
  };

  useEffect(() => {
    fetchMessages(null, { background: false });
    const intervalId = setInterval(() => {
      fetchMessages(lastMessageId, { background: true });
    }, 5000);
    return () => clearInterval(intervalId);
  }, [offerId, token, isClient, clientId, clientName, clientAvatar, professionalId, professionalName, professionalAvatar]);

  useEffect(() => {
    if (messagesEndRef.current && !isInputFocused) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, pendingMessages, isInputFocused]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setSelectedFiles((prev) => [
        ...prev,
        ...Array.from(files).filter(
          (file) => !prev.some((f) => f.name === file.name && f.size === file.size)
        ),
      ]);
      e.target.value = '';
    }
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() && selectedFiles.length === 0) return;
    if (!token || !offerId) return;
    if (isClient && (professionalId === undefined || professionalId === null)) {
      setError("Veuillez sélectionner un professionnel avant d'envoyer un message.");
      return;
    }
    setSending(true);
    setError(null);

    const messageToSend = newMessage;
    const filesToSend = [...selectedFiles];

    const optimisticId = 'pending-' + Date.now();
    const optimisticMessage = {
      id: optimisticId as any,
      open_offer_id: offerId,
      sender_id: currentUser.id,
      receiver_id: isClient ? professionalId : clientId,
      message_text: messageToSend,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sender: currentUser,
      receiver: {},
      files: filesToSend.map((file) => ({
        id: ('pending-file-' + file.name + file.size) as any,
        original_name: file.name,
        mime_type: file.type,
        isPending: true,
        localUrl: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
        extension: file.name.split('.').pop(),
      })),
      isPending: true,
      error: null,
    };
    setPendingMessages((prev) => [...prev, optimisticMessage]);
    setNewMessage('');
    setSelectedFiles([]);

    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const body: { message_text: string; receiver_id?: number } = { message_text: messageToSend };
      if (isClient && professionalId !== undefined) {
        body.receiver_id = professionalId;
      }
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${offerId}/messages`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      const data = await response.json();
      if (!data.message || !data.message.id) {
        throw new Error('Erreur lors de la création du message');
      }
      const messageId = data.message.id;
      
      if (filesToSend.length > 0) {
        const formData = new FormData();
        filesToSend.forEach((file: File) => formData.append('files[]', file));
        formData.append('message_id', messageId);
        const uploadResponse = await fetch(`${API_BASE_URL}/api/files/upload`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        });
        const uploadData = await uploadResponse.json();
        if (!uploadData.success) {
          throw new Error('Erreur lors de l\'upload des fichiers: ' + (uploadData.message || ''));
        }
      }
      
      await fetchMessages(null, { background: true });
      setPendingMessages((prev) => prev.filter((m) => m.id !== optimisticId));
      addOfferNotification('offer_message', {
        offer_id: offerId,
        offer_title: offerTitle||"",
        message: messageToSend,
        ...(isClient
          ? { client_id: user.id, client_name: clientName, client_avatar: clientAvatar }
          : { professional_id: user.id, professional_name: professionalName, professional_avatar: professionalAvatar })
      });
    } catch (err) {
      setPendingMessages((prev) => prev.map((m) => m.id === optimisticId ? { ...m, error: 'Échec de l\'envoi', isPending: false } : m));
      setError('Envoi échoué.');
    } finally {
      setSending(false);
    }
  };

  let filteredMessages = messages;
  if (isClient && professionalId) {
    filteredMessages = messages.filter(
      (msg) =>
        (msg.sender_id === professionalId && msg.receiver_id === clientId) ||
        (msg.sender_id === clientId && msg.receiver_id === professionalId)
    );
  }

  const allMessages = [...messages, ...pendingMessages];
  const filteredAllMessages = isClient && professionalId
    ? allMessages.filter(
        (msg) =>
          (msg.sender_id === professionalId && msg.receiver_id === clientId) ||
          (msg.sender_id === clientId && msg.receiver_id === professionalId)
      )
    : allMessages;

  if (loading) {
    return (
      <div className="flex-1 flex justify-center items-center bg-[#F5F5F5]" style={{ fontFamily: "'Inter', sans-serif" }}>
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full w-full bg-[#F5F5F5]" style={{ fontFamily: "'Inter', sans-serif" }}>
      <div
        className="flex-1 overflow-y-auto p-4"
        style={{
          maxHeight: 'calc(100vh - 180px)',
          overflowY: 'scroll',
          scrollbarWidth: 'thin',
          paddingBottom: '80px' // Ajout d'un padding pour éviter que le contenu soit caché par la zone fixe
        }}
      >
        {filteredAllMessages.length === 0 ? (
          <div className="text-gray-400 text-center mt-10">
            Aucun message pour ce professionnel.
          </div>
        ) : (
          <div className="space-y-3">
            {filteredAllMessages.map((msg) => {
              const isSent = msg.sender_id === currentUser.id;
              return (
                <div
                  key={msg.id}
                  className={`flex ${isSent ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] rounded-2xl p-4 ${isSent ? 'bg-[#fae4b0]' : 'bg-white'
                      }`}
                    style={{
                      boxShadow: "0 1px 4px rgba(160,89,207,0.04)",
                    }}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-semibold text-sm">
                        {msg.sender?.first_name || "Utilisateur"}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(msg.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </div>
                    <div className="text-sm text-gray-800 mb-2">
                      {msg.message_text}
                    </div>

                    {msg.files && msg.files.length > 0 && (
                      <div className="grid grid-cols-1 gap-2 mt-2">
                        {msg.files.map((file: FileData) => {
                          const isImage = file.mime_type?.startsWith('image/');
                          const getFileIcon = () => {
                            if (/pdf$/.test(file.original_name)) {
                              return (
                                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                                  <span className="text-xs font-bold text-purple-700">PDF</span>
                                </div>
                              );
                            }
                            if (/docx?$/.test(file.original_name)) {
                              return (
                                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                  <span className="text-xs font-bold text-blue-700">DOC</span>
                                </div>
                              );
                            }
                            if (/xlsx?$/.test(file.original_name)) {
                              return (
                                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                  <span className="text-xs font-bold text-green-700">XLS</span>
                                </div>
                              );
                            }
                            if (/txt$/.test(file.original_name)) {
                              return (
                                <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                                  <span className="text-xs font-bold text-yellow-700">TXT</span>
                                </div>
                              );
                            }
                            if (/zip$|rar$/.test(file.original_name)) {
                              return (
                                <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                                  <span className="text-xs font-bold text-indigo-700">ZIP</span>
                                </div>
                              );
                            }
                            return (
                              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                                <span className="text-xs font-bold text-gray-700">FILE</span>
                              </div>
                            );
                          };

                          if (isImage) {
                            const imageUrl = file.localUrl || getImageUrl(file);
                            return (
                              <div key={file.id} className="rounded-lg overflow-hidden bg-white border border-gray-200">
                                <img
                                  src={imageUrl}
                                  alt={file.original_name}
                                  className="w-full h-32 object-cover cursor-pointer"
                                  onClick={() => window.open(imageUrl, '_blank')}
                                  onError={(e) => {
                                    console.error('Erreur de chargement de l\'image:', file.original_name);
                                    e.currentTarget.style.display = 'none';
                                  }}
                                />
                                <div className="p-2 bg-gray-50 border-t border-gray-200">
                                  <div className="text-xs text-gray-600 font-medium truncate">
                                    {file.original_name}
                                  </div>
                                </div>
                              </div>
                            );
                          }

                          return (
                            <a
                              key={file.id}
                              href={file.download_url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center p-2 bg-white rounded-lg border border-gray-200 hover:bg-gray-50"
                            >
                              {getFileIcon()}
                              <div className="ml-2">
                                <div className="text-xs font-medium text-gray-800 truncate max-w-xs">
                                  {file.original_name}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {file.human_size}
                                </div>
                              </div>
                            </a>
                          );
                        })}
                      </div>
                    )}
                    
                    {msg.isPending && (
                      <div className="text-xs text-gray-400">
                        Envoi en cours...
                      </div>
                    )}
                    {msg.error && (
                      <div className="text-xs text-red-500">
                        {msg.error}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 mx-4 rounded-lg">
          {error}
        </div>
      )}

      {selectedFiles.length > 0 && (
        <div className="bg-[#f8f4ff] rounded-lg p-3 mx-4 my-2 flex flex-wrap gap-2 fixed bottom-24 left-0 right-0 z-10">
          {selectedFiles.map((file, idx) => {
            const isImage = file.type.startsWith("image/");

            return (
              <div key={idx} className="relative">
                {isImage ? (
                  <div className="w-16 h-16 rounded-lg overflow-hidden border border-gray-200">
                    <img
                      src={URL.createObjectURL(file)}
                      alt={file.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="w-16 h-16 rounded-lg bg-gray-100 border border-gray-200 flex items-center justify-center">
                    <span className="text-xs font-medium truncate px-1">
                      {file.name.split('.').pop()}
                    </span>
                  </div>
                )}
                <button
                  onClick={() => handleRemoveFile(idx)}
                  className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                >
                  ×
                </button>
              </div>
            );
          })}
        </div>
      )}

      {/* Zone de saisie en position fixe */}
      <div className="fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-200">
        <div className="flex items-center bg-white rounded-2xl border border-gray-300 p-2">
          <label className="cursor-pointer p-2">
            <input
              type="file"
              multiple
              className="hidden"
              onChange={handleFileChange}
            />
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <path d="M21.44 11.05l-9.19 9.19a5 5 0 01-7.07-7.07l9.19-9.19a3 3 0 014.24 4.24l-9.2 9.19a1 1 0 01-1.41-1.41l9.2-9.19" />
            </svg>
          </label>

          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") handleSendMessage();
            }}
            onFocus={() => setIsInputFocused(true)}
            onBlur={() => setIsInputFocused(false)}
            placeholder="Écrivez votre message..."
            className="flex-1 border-none outline-none bg-transparent px-3 py-2"
          />

          <button
            onClick={handleSendMessage}
            disabled={sending || (!newMessage.trim() && selectedFiles.length === 0)}
            className="bg-black text-white rounded-full w-10 h-10 flex items-center justify-center disabled:opacity-50"
          >
            <Send size={18} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default MessageContent_mobile_new;