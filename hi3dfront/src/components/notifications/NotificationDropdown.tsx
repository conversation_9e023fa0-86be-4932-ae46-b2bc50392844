import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Bell } from 'lucide-react';
import NotificationList from './NotificationList';
import { NotificationItemProps } from './NotificationItem';

interface NotificationDropdownProps {
  notifications: NotificationItemProps[];
  onMarkAsRead: (id: number) => void;
  onMarkAllAsRead: () => void;
  loading?: boolean;
  error?: string | null;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  notifications,
  onMarkAsRead,
  onMarkAllAsRead,
  loading = false,
  error,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // Count unread notifications
  const unreadCount = notifications.filter(notification => !notification.read).length;

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle notification click
  const handleNotificationClick = (notification: NotificationItemProps) => {
    // Mark as read
    if (!notification.read) {
      onMarkAsRead(notification.id);
    }

    // Navigate to the appropriate page based on notification type
    if (notification.link) {
      navigate(notification.link);
    } else {
      switch (notification.type) {
        case 'message':
          navigate(`/discussions/${notification.sender?.id}`);
          break;
        case 'project':
          navigate(`/offre/${notification.id}`);
          break;
        case 'payment':
          navigate('/invoices');
          break;
        case 'review':
          navigate(`/profile/${notification.sender?.id}`);
          break;
        default:
          // Do nothing for system notifications
          break;
      }
    }

    // Close dropdown
    setIsOpen(false);
  };

  // Handle view all notifications
  const handleViewAll = () => {
    navigate('/notifications');
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        className="relative p-2 text-neutral-600 hover:text-neutral-900 focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Notifications"
      >
        <Bell className="h-6 w-6" />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-primary-600 rounded-full">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 z-50">
          <NotificationList
            notifications={notifications}
            onNotificationClick={handleNotificationClick}
            onMarkAllAsRead={onMarkAllAsRead}
            onViewAll={handleViewAll}
            loading={loading}
            error={error}
          />
        </div>
      )}
    </div>
  );
};

export default NotificationDropdown;
