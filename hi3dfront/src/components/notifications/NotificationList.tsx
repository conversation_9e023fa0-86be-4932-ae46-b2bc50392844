import React from 'react';
import NotificationItem, { NotificationItemProps } from './NotificationItem';
import Button from '../ui/Button';

interface NotificationListProps {
  notifications: NotificationItemProps[];
  onNotificationClick: (notification: NotificationItemProps) => void;
  onMarkAllAsRead?: () => void;
  onViewAll?: () => void;
  loading?: boolean;
  error?: string | null;
  emptyMessage?: string;
}

const NotificationList: React.FC<NotificationListProps> = ({
  notifications,
  onNotificationClick,
  onMarkAllAsRead,
  onViewAll,
  loading = false,
  error,
  emptyMessage = 'Aucune notification',
}) => {
  // Count unread notifications
  const unreadCount = notifications.filter(notification => !notification.read).length;

  return (
    <div className="bg-white rounded-lg shadow-lg border border-neutral-200 overflow-hidden max-w-md w-full max-h-[80vh] flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-neutral-200 flex justify-between items-center">
        <div>
          <h3 className="font-semibold text-neutral-900">Notifications</h3>
          {unreadCount > 0 && (
            <p className="text-xs text-neutral-600">
              {unreadCount} non {unreadCount === 1 ? 'lue' : 'lues'}
            </p>
          )}
        </div>

        {onMarkAllAsRead && unreadCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onMarkAllAsRead}
          >
            Tout marquer comme lu
          </Button>
        )}
      </div>

      {/* Notification list */}
      <div className="overflow-y-auto flex-1">
        {loading ? (
          <div className="flex justify-center items-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-600"></div>
          </div>
        ) : error ? (
          <div className="p-6 text-center">
            <p className="text-red-500">{error}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => window.location.reload()}
            >
              Réessayer
            </Button>
          </div>
        ) : notifications.length === 0 ? (
          <div className="p-8 text-center text-neutral-500">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-12 w-12 mx-auto text-neutral-300 mb-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"
              />
            </svg>
            <p>{emptyMessage}</p>
          </div>
        ) : (
          notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              {...notification}
              onClick={() => onNotificationClick(notification)}
            />
          ))
        )}
      </div>

      {/* Footer */}
      {onViewAll && notifications.length > 0 && (
        <div className="p-3 border-t border-neutral-200 bg-neutral-50">
          <Button
            variant="ghost"
            fullWidth
            onClick={onViewAll}
          >
            Voir toutes les notifications
          </Button>
        </div>
      )}
    </div>
  );
};

export default NotificationList;
