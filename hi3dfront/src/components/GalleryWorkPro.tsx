import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import WorkCreateModal from "./WorkCreateModal";
import { API_BASE_URL } from "../config";

type Props = {
  items?: any[] | undefined;
  itemsPro?: any[] | undefined;
  onRefresh?: () => void;
};


const GalleryWorkPro: React.FC<Props> = ({items,itemsPro,onRefresh})=> {
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const perPage = 6; // nombre d’items affichés par page

  const [open, setOpen] = useState(false);
  const [mode, setMode] = useState<"create" | "edit">("create");
  const [editing, setEditing] = useState<any | null>(null);

   const getImageService = (imagePath: string | undefined, defaultImage: string = "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") => {
            if (!imagePath) return defaultImage;
              if (imagePath.startsWith('http')) return imagePath;  
              if (imagePath.startsWith('/')) return `${API_BASE_URL}/storage${imagePath}`;
              return `${API_BASE_URL}/storage/${imagePath}`;
            };

  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const isPro = !!user?.is_professional;

  const handleDelete = async (id: number) => {
    const token = localStorage.getItem("token");
    if (!token) return;
    const ok = window.confirm("Supprimer ce work ? Cette action est définitive.");
    if (!ok) return;
    try {
      const res = await fetch(`${API_BASE_URL}/api/achievements/${id}`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!res.ok) {
        const text = await res.text();
        throw new Error(text || "Delete failed");
      }
      onRefresh?.();
    } catch (e) {
      console.error(e);
      alert("La suppression a échoué.");
    }
  };

  if(itemsPro){
  
      const indexOfLast = currentPage * perPage;
      const indexOfFirst = indexOfLast - perPage;
      const currentWork = itemsPro.slice(indexOfFirst, indexOfLast);
  
    const handlePrev = () => {
      if (currentPage > 1) setCurrentPage((p) => p - 1);
    };
  
    const handleNext = () => {
      if (currentPage < Math.ceil(itemsPro.length / perPage)) setCurrentPage((p) => p + 1);
    };
  
      return (
        <>
      <div className="w-full mx-auto py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4 justify-items-center mb-8 px-[10px] md:px-[40px]">

          <div
            onClick={() => { setMode("create"); setEditing(null); setOpen(true); }}
            className="group w-full overflow-hidden relative flex flex-col cursor-pointer h-[320px]"
          >
            <div className="relative w-full h-full">
              <div
                style={{
                  backgroundColor: "#f3f4f6",
                  borderRadius: "5px",
                }}
                className="absolute inset-0 flex items-center justify-center"
              >
                <span 
                className=" text-lg  text-center"
                style={{
                      fontSize: "20px",
                      fontWeight: 400,
                      fontFamily: "'Inter', sans-serif",
                      color: "#000000"
                    }}
                >
                  Show your work to your customers <br/>Add new one
                </span>
              </div>
            </div>
          </div>
  
          {currentWork.map((item) => (
          <div
            key={item.id}
            onClick={() => isPro ? (setMode("edit"), setEditing({
              id: item.id,
              title: item.title,
              description: item.description,
              category: item.category,
              cover_photo: getImageService(item.cover_photo) || getImageService(item.image_url) || getImageService(item.file_path),
              gallery_photos: (item.gallery_photos || item.file_urls || []).map((p: any) => typeof p === 'string' ? getImageService(p) : getImageService(p?.path)).filter(Boolean),
              youtube_link: item.youtube_link,
              status: item.status,
            }), setOpen(true)) : setOpen(true)}
            className="group w-full bg-white overflow-hidden relative flex flex-col cursor-pointer h-[320px]"
          >
            {/* Image container */}
            <div className="relative w-full h-full">
              <div
                style={{
                  backgroundImage: `url(${getImageService(item.cover_photo)})`,
                  borderRadius: "5px",
                }}
                className="absolute inset-0 bg-cover bg-center"
              />
            </div>
            {isPro && (
              <div className="absolute top-2 right-2 flex gap-2">
                <button
                  className="px-3 py-1 text-xs rounded bg-white/90 hover:bg-white border border-gray-300"
                  onClick={(e) => { e.stopPropagation(); setMode("edit"); setEditing({
                    id: item.id,
                    title: item.title,
                    description: item.description,
                    category: item.category,
                    cover_photo: getImageService(item.cover_photo) || getImageService(item.image_url) || getImageService(item.file_path),
                    gallery_photos: (item.gallery_photos || item.file_urls || []).map((p: any) => typeof p === 'string' ? getImageService(p) : getImageService(p?.path)).filter(Boolean),
                    youtube_link: item.youtube_link,
                    status: item.status,
                  }); setOpen(true); }}
                >
                  Edit
                </button>
                <button
                  className="px-3 py-1 text-xs rounded bg-white/90 hover:bg-white border border-red-300 text-red-600"
                  onClick={(e) => { e.stopPropagation(); handleDelete(item.id); }}
                >
                  Delete
                </button>
              </div>
            )}
          </div>
        ))}
        </div>
        {itemsPro.length > 6 && (
          <div className="flex justify-center items-center gap-4 mt-6">
            <button
              className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
              style={{
                      fontFamily: "'Inter', sans-serif",
                    }}
              onClick={handlePrev}
              disabled={currentPage === 1}
            >
              Précédent
            </button>
            <button
              className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
              style={{
                      fontFamily: "'Inter', sans-serif",
                    }}
              onClick={handleNext}
              disabled={currentPage === Math.ceil(itemsPro.length / perPage)}
            >
              Suivant
            </button>
          </div>
        )}
      </div>
  
       <WorkCreateModal
        open={open}
        onClose={() => setOpen(false)}
        mode={mode}
        initialAchievement={editing || undefined}
        onCompleted={() => { setOpen(false); onRefresh?.(); }}
      />
      </>
      );
    }

  if (items?.length === 0 || items === undefined) {
    return (
      <div className="text-center py-20 text-gray-600 flex flex-col items-center justify-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="w-24 h-24 text-gray-400 mb-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
          strokeWidth={1.5}
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            d="M3 7a2 2 0 012-2h5l2 2h7a2 2 0 012 2v7a2 2 0 01-2 2H5a2 2 0 01-2-2V7z"
          />
        </svg>
        <h2 className="text-2xl font-semibold">No projects found</h2>
        <p className="text-gray-500 mt-2 max-w-md">
          We couldn't find any projects to display. Please check back later or try a different search.
        </p>
      </div>
    );
  }
  const indexOfLast = currentPage * perPage;
  const indexOfFirst = indexOfLast - perPage;
  const currentWork = items.slice(indexOfFirst, indexOfLast);

  const handlePrev = () => {
    if (currentPage > 1) setCurrentPage((p) => p - 1);
  };

  const handleNext = () => {
    if (currentPage < Math.ceil(items.length / perPage)) setCurrentPage((p) => p + 1);
  };

  return (
    <div className="w-full mx-auto py-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 justify-items-center mb-6 px-2 md:px-[40px]">
        {currentWork.map((item) => (
          <div
            key={item.id}
            onClick={() => {
                navigate("/details-search", {
                  state: { service: item },
                });
              }}
            className="group w-full bg-white overflow-hidden relative flex flex-col cursor-pointer h-[320px]"
          >
            {/* Image container */}
            <div className="relative w-full h-full">
              <div
                style={{
                  backgroundImage: `url(${item.image_url})`,
                  borderRadius: "5px",
                }}
                className="absolute inset-0 bg-cover bg-center"
              />
            </div>
          </div>
        ))}
      </div>
      {items.length > 6 && (
        <div className="flex justify-center items-center gap-4 mt-6">
          <button
            className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
            style={{
                    fontFamily: "'Inter', sans-serif",
                  }}
            onClick={handlePrev}
            disabled={currentPage === 1}
          >
            Précédent
          </button>
          <button
            className="px-0 py-2 text-gray-700 font-medium hover:text-black transition disabled:opacity-50 disabled:cursor-not-allowed bg-transparent border-none"
            style={{
                    fontFamily: "'Inter', sans-serif",
                  }}
            onClick={handleNext}
            disabled={currentPage === Math.ceil(items.length / perPage)}
          >
            Suivant
          </button>
        </div>
      )}
    </div>
  );
};

export default GalleryWorkPro;