import React, { useState } from 'react';
import { COUNTRIES } from "./../data/countries";

interface ModalFilterProps {
  isOpen: boolean;
  onClose: () => void;
  onApply?: (filters: { country: string; language: string; minPrice: number; maxPrice: number }) => void;
}

const ModalFilter: React.FC<ModalFilterProps> = ({ isOpen, onClose, onApply }) => {
  const [selectedCountry, setSelectedCountry] = useState<string>('');
  const [selectedLanguage, setSelectedLanguage] = useState<string>('');
  const [minPrice, setMinPrice] = useState<string>('');
  const [maxPrice, setMaxPrice] = useState<string>('');
  const [isProUser, setIsProUser] = useState<boolean>(false);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div
        className="bg-white p-6 relative"
        style={{ width: "690px", height: "297px", borderRadius: "16px" }}
      >
        {/* Bouton de fermeture en haut à droite */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 text-2xl"
        >
          &times;
        </button>

        <div className="flex justify-between mt-2">
          {/* Colonne de gauche */}
          <div className="flex-1 pr-4">
            {/* Pays Section */}
            <div className="mb-6">
              <h3
                className="font-medium mb-1 text-gray-700"
                style={{ fontFamily: "Inter, sans-serif", fontSize: "12px" }}
              >
                Pays
              </h3>
              <select
                value={selectedCountry}
                onChange={(e) => setSelectedCountry(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md bg-gray-50"
                style={{ fontFamily: "Inter, sans-serif", fontSize: "12px" }}
              >
                <option value="">Sélectionner le pays</option>
                {COUNTRIES.map((country) => (
                  <option key={country} value={country.toLowerCase()}>
                    {country}
                  </option>
                ))}
                {/* <option value="france">France</option>
                <option value="belgique">Belgique</option>
                <option value="suisse">Suisse</option> */}
              </select>
            </div>

            {/* Price Range Section */}
            <div>
              <h3 
                className="font-medium mb-1 text-gray-700" 
                style={{ fontFamily: "Inter, sans-serif", fontSize: "12px" }}
              >
                Price range
              </h3>
              <div className="flex space-x-2">
                <input
                  type="number"
                  placeholder="Min"
                  value={minPrice}
                  onChange={(e) => setMinPrice(e.target.value)}
                  className="w-1/2 p-2 border border-gray-300 rounded-md text-sm"
                />
                <input
                  type="number"
                  placeholder="Max"
                  value={maxPrice}
                  onChange={(e) => setMaxPrice(e.target.value)}
                  className="w-1/2 p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>
          </div>

          {/* Colonne de droite */}
          <div className="flex-1 pl-4">
            {/* Language Section */}
            <div className="mb-6">
              <h3 
                className="font-medium mb-1 text-gray-700" 
                style={{ fontFamily: "Inter, sans-serif", fontSize: "12px" }}
              >
                Language
              </h3>
              <select
                value={selectedLanguage}
                onChange={(e) => setSelectedLanguage(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md bg-gray-50 text-sm"
              >
                <option value="">Select -</option>
                <option value="français">French</option>
                <option value="anglais">English</option>
                <option value="italien">Italian</option>
                <option value="espagnol">Spanish</option>
                <option value="allemand">German</option>
                <option value="portugais">Portuguese</option>
                <option value="russe">Russian</option>
                <option value="chinois">Chinese</option>
                <option value="japonais">Japanese</option>
                <option value="arabe">Arabic</option>
              </select>
            </div>

            {/* Type of user Section */}
            <div>
              <h3 
                className="font-medium mb-1 text-gray-700" 
                style={{ fontFamily: "Inter, sans-serif", fontSize: "12px" }}
              >
                Type of user
              </h3>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="pro-user"
                  checked={isProUser}
                  onChange={(e) => setIsProUser(e.target.checked)}
                  className="mr-2 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <label 
                  htmlFor="pro-user" 
                  className="text-gray-700 text-sm"
                  style={{ fontFamily: "Inter, sans-serif" }}
                >
                  PRO USER
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Filter Button en bas à gauche */}
        <div className="absolute bottom-6 left-6">
          <button 
            className="bg-blue-600 text-white py-2 px-6 hover:bg-blue-700 transition-colors font-medium text-sm"
            style={{ fontFamily: "Inter, sans-serif", borderRadius: "18px" }}
            onClick={() => {
              const filters = {
                country: (selectedCountry || '').trim(),
                language: (selectedLanguage || '').trim(),
                minPrice: Number(minPrice) || 0,
                maxPrice: Number(maxPrice) || 0,
              };
              onApply && onApply(filters);
              onClose();
            }}
          >
            Filter
          </button>
        </div>
      </div>
    </div>
  );
};

export default ModalFilter;