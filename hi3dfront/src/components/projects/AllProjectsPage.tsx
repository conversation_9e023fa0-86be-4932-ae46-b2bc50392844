import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  Filter,
  SlidersHorizontal,
  Calendar,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import ProjectCard, { ProjectCardProps } from '../dashboard/ProjectCard';
import Badge from '../ui/Badge';

const AllProjectsPage: React.FC = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<ProjectCardProps[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<ProjectCardProps[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);

  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');
  const isProfessional = user?.is_professional === true;

  useEffect(() => {
    const fetchProjects = async () => {
      setLoading(true);
      try {
        // Endpoint différent selon le type d'utilisateur
        const endpoint = isProfessional
          ? '/api/open-offers' // Pour les professionnels: tous les projets disponibles
          : '/api/client/open-offers';   // Pour les clients: leurs propres projets

        const response = await fetch(`${API_BASE_URL}${endpoint}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des projets');
        }

        const data = await response.json();
        console.log('API response:', data); // Pour déboguer la structure de la réponse

        // Vérifier la structure de la réponse et extraire les offres
        const offers = data.client_open_offers || data.data || data;

        // Transformer les données en format ProjectCardProps
        const formattedProjects = (Array.isArray(offers) ? offers : []).map((project: any) => ({
          id: project.id,
          title: project.title,
          description: project.description,
          budget: `${project.budget} €`,
          deadline: project.deadline,
          status: project.status,
          client: isProfessional ? {
            id: project.user_id,
            name: project.user_name || 'Client',
            avatar: project.user_avatar || '',
          } : undefined,
          professional: !isProfessional ? {
            id: project.professional_id,
            name: project.professional_name || 'Professionnel',
            avatar: project.professional_avatar || '',
          } : undefined,
        }));

        setProjects(formattedProjects);
        setFilteredProjects(formattedProjects);
        setError(null);
      } catch (err) {
        console.error('Error fetching projects:', err);
        setError('Impossible de récupérer les projets. Veuillez réessayer plus tard.');
        // Utiliser des données statiques en cas d'erreur
        setProjects(getMockProjects());
        setFilteredProjects(getMockProjects());
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [token, isProfessional]);

  // Filtrer les projets lorsque les filtres changent
  useEffect(() => {
    let result = [...projects];

    // Filtre par recherche
    if (searchTerm) {
      result = result.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtre par statut
    if (statusFilter !== 'all') {
      result = result.filter(project => project.status === statusFilter);
    }

    setFilteredProjects(result);
  }, [searchTerm, statusFilter, projects]);

  const handleProjectClick = (projectId: number) => {
    navigate(`/offre/${projectId}`);
  };

  const handleCreateProject = () => {
    navigate('/create-project-client');
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return <Badge color="blue">Ouvert</Badge>;
      case 'in_progress':
        return <Badge color="green">En cours</Badge>;
      case 'completed':
        return <Badge color="purple">Terminé</Badge>;
      case 'cancelled':
        return <Badge color="red">Annulé</Badge>;
      default:
        return <Badge color="gray">Inconnu</Badge>;
    }
  };

  // Données statiques pour le fallback
  const getMockProjects = (): ProjectCardProps[] => {
    if (isProfessional) {
      return [
        {
          id: 1,
          title: 'Création d\'un personnage 3D pour jeu vidéo',
          description: 'Modélisation et animation d\'un personnage principal pour un jeu d\'aventure.',
          budget: '1 200 €',
          deadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'open',
          client: {
            id: 101,
            name: 'Studio GameArt',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          },
        },
        {
          id: 2,
          title: 'Animation d\'une scène d\'introduction',
          description: 'Création d\'une animation 3D de 30 secondes pour l\'introduction d\'une application mobile.',
          budget: '800 €',
          deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'in_progress',
          client: {
            id: 102,
            name: 'AppTech Solutions',
            avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
          },
        },
        {
          id: 3,
          title: 'Modélisation d\'objets pour environnement virtuel',
          description: 'Création de 10 objets 3D pour un environnement de réalité virtuelle.',
          budget: '500 €',
          deadline: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'completed',
          client: {
            id: 103,
            name: 'VR Experiences',
            avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
          },
        },
      ];
    } else {
      return [
        {
          id: 1,
          title: 'Création d\'un personnage 3D pour jeu vidéo',
          description: 'Modélisation et animation d\'un personnage principal pour un jeu d\'aventure.',
          budget: '1 200 €',
          deadline: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'in_progress',
          professional: {
            id: 201,
            name: 'Thomas Martin',
            avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
          },
        },
        {
          id: 2,
          title: 'Animation d\'une scène d\'introduction',
          description: 'Création d\'une animation 3D de 30 secondes pour l\'introduction d\'une application mobile.',
          budget: '800 €',
          deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'in_progress',
          professional: {
            id: 202,
            name: 'Sophie Dubois',
            avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
          },
        },
        {
          id: 3,
          title: 'Modélisation d\'objets pour environnement virtuel',
          description: 'Création de 10 objets 3D pour un environnement de réalité virtuelle.',
          budget: '500 €',
          deadline: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'completed',
          professional: {
            id: 203,
            name: 'Lucas Bernard',
            avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
          },
        },
      ];
    }
  };

  return (
    <DashboardLayout
      title={isProfessional ? "Tous les projets disponibles" : "Tous mes projets"}
      subtitle={isProfessional
        ? "Explorez les projets disponibles et trouvez votre prochaine opportunité"
        : "Gérez tous vos projets en cours et terminés"
      }
      actions={
        !isProfessional ? (
          <Button
            variant="primary"
            leftIcon={<Plus className="h-5 w-5" />}
            onClick={handleCreateProject}
            style={{ backgroundColor: '#2980b9', color: 'white', padding: '0.75rem 1.5rem', fontWeight: 'bold', borderRadius: '0.5rem' }}
          >
            Créer un projet
          </Button>
        ) : undefined
      }
    >
      {/* Barre de recherche et filtres */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Rechercher un projet..."
              className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <select
              className="px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              aria-label="Filtrer par statut"
            >
              <option value="all">Tous les statuts</option>
              <option value="open">Ouverts</option>
              <option value="in_progress">En cours</option>
              <option value="completed">Terminés</option>
              <option value="cancelled">Annulés</option>
            </select>
            <Button
              variant="outline"
              leftIcon={<SlidersHorizontal className="h-5 w-5" />}
              onClick={() => setShowFilters(!showFilters)}
            >
              Filtres
            </Button>
          </div>
        </div>

        {/* Filtres avancés */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-neutral-200 grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Budget</label>
              <select
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                aria-label="Filtrer par budget"
              >
                <option value="">Tous les budgets</option>
                <option value="0-500">Moins de 500 €</option>
                <option value="500-1000">500 € - 1 000 €</option>
                <option value="1000-5000">1 000 € - 5 000 €</option>
                <option value="5000+">Plus de 5 000 €</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Date limite</label>
              <select
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                aria-label="Filtrer par date limite"
              >
                <option value="">Toutes les dates</option>
                <option value="week">Cette semaine</option>
                <option value="month">Ce mois-ci</option>
                <option value="quarter">Ce trimestre</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Catégorie</label>
              <select
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                aria-label="Filtrer par catégorie"
              >
                <option value="">Toutes les catégories</option>
                <option value="modeling">Modélisation 3D</option>
                <option value="animation">Animation</option>
                <option value="rendering">Rendu</option>
                <option value="vfx">Effets spéciaux</option>
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Liste des projets */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-lg font-semibold text-red-700 mb-2">Erreur</h2>
          <p className="text-red-600">{error}</p>
          <Button
            variant="primary"
            className="mt-4"
            onClick={() => window.location.reload()}
          >
            Réessayer
          </Button>
        </div>
      ) : filteredProjects.length === 0 ? (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-8 text-center">
          <Clock className="h-16 w-16 text-neutral-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-neutral-800 mb-2">Aucun projet trouvé</h2>
          <p className="text-neutral-600 mb-6">
            {isProfessional
              ? "Aucun projet ne correspond à vos critères de recherche. Essayez de modifier vos filtres."
              : "Vous n'avez pas encore de projets. Créez votre premier projet pour commencer."
            }
          </p>
          {!isProfessional && (
            <Button
              variant="primary"
              leftIcon={<Plus className="h-5 w-5" />}
              onClick={handleCreateProject}
            >
              Créer un projet
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          {/* Statistiques */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex items-center">
              <div className="bg-blue-100 p-3 rounded-full mr-4">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-neutral-600">Total</p>
                <p className="text-xl font-semibold">{projects.length}</p>
              </div>
            </div>
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex items-center">
              <div className="bg-amber-100 p-3 rounded-full mr-4">
                <Clock className="h-6 w-6 text-amber-600" />
              </div>
              <div>
                <p className="text-sm text-neutral-600">En cours</p>
                <p className="text-xl font-semibold">
                  {projects.filter(p => p.status === 'in_progress').length}
                </p>
              </div>
            </div>
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex items-center">
              <div className="bg-green-100 p-3 rounded-full mr-4">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-neutral-600">Terminés</p>
                <p className="text-xl font-semibold">
                  {projects.filter(p => p.status === 'completed').length}
                </p>
              </div>
            </div>
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex items-center">
              <div className="bg-red-100 p-3 rounded-full mr-4">
                <XCircle className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-neutral-600">Annulés</p>
                <p className="text-xl font-semibold">
                  {projects.filter(p => p.status === 'cancelled').length}
                </p>
              </div>
            </div>
          </div>

          {/* Liste des projets */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h3 className="text-lg font-semibold text-neutral-900">
                {filteredProjects.length} projet{filteredProjects.length > 1 ? 's' : ''}
              </h3>
            </div>
            <div className="p-6 grid grid-cols-1 gap-6">
              {filteredProjects.map(project => (
                <ProjectCard
                  key={project.id}
                  {...project}
                  onClick={() => handleProjectClick(project.id)}
                />
              ))}
            </div>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default AllProjectsPage;
