import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Filter,
  SlidersHorizontal,
  Calendar,
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  Briefcase,
  DollarSign,
  Tag
} from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import ProjectCard, { ProjectCardProps } from '../dashboard/ProjectCard';
import Badge from '../ui/Badge';
import Alert from '../ui/Alert';

const ProfessionalProjectsPage: React.FC = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState<ProjectCardProps[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<ProjectCardProps[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [budgetFilter, setBudgetFilter] = useState<string>('all');
  const [showFilters, setShowFilters] = useState(false);
  const [categories, setCategories] = useState<string[]>([]);

  const token = localStorage.getItem('token');

  // Charger les projets au chargement du composant
  useEffect(() => {
    const fetchProjects = async () => {
      setLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/open-offers`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des projets');
        }

        const data = await response.json();
        console.log('API response:', data);

        // Extraire les offres
        const offers = data.open_offers || [];

        // Collecter toutes les catégories uniques pour les filtres
        const allCategories = new Set<string>();
        offers.forEach((offer: any) => {
          const offerCategories = typeof offer.categories === 'string' 
            ? JSON.parse(offer.categories) 
            : (offer.categories || []);
          
          offerCategories.forEach((cat: string) => allCategories.add(cat));
        });
        setCategories(Array.from(allCategories));

        // Transformer les données en format ProjectCardProps
        const formattedProjects = offers.map((project: any) => ({
          id: project.id,
          title: project.title,
          description: project.description,
          budget: `${project.budget} €`,
          deadline: project.deadline,
          status: project.status || 'open',
          categories: typeof project.categories === 'string' 
            ? JSON.parse(project.categories) 
            : (project.categories || []),
          client: {
            id: project.user_id,
            name: project.user ? `${project.user.first_name} ${project.user.last_name}` : 'Client',
            avatar: project.user?.profile_picture_path || '',
          },
        }));

        setProjects(formattedProjects);
        setFilteredProjects(formattedProjects);
        setError(null);
      } catch (err) {
        console.error('Error fetching projects:', err);
        setError('Impossible de récupérer les projets. Veuillez réessayer plus tard.');
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [token]);

  // Filtrer les projets lorsque les filtres changent
  useEffect(() => {
    let result = [...projects];

    // Filtre par recherche
    if (searchTerm) {
      result = result.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtre par statut
    if (statusFilter !== 'all') {
      result = result.filter(project => project.status === statusFilter);
    }

    // Filtre par catégorie
    if (categoryFilter !== 'all') {
      result = result.filter(project => 
        project.categories && project.categories.includes(categoryFilter)
      );
    }

    // Filtre par budget
    if (budgetFilter !== 'all') {
      const [min, max] = budgetFilter.split('-').map(Number);
      result = result.filter(project => {
        const budget = parseInt(project.budget.replace(/[^0-9]/g, ''));
        if (max) {
          return budget >= min && budget <= max;
        } else {
          return budget >= min;
        }
      });
    }

    setFilteredProjects(result);
  }, [searchTerm, statusFilter, categoryFilter, budgetFilter, projects]);

  const handleProjectClick = (projectId: number) => {
    navigate(`/dashboard/offers/${projectId}`);
  };

  const handleApplyToProject = async(projectId: number) => {
    // navigate(`/dashboard/open-offers/${projectId}/apply`);
    const token = localStorage.getItem("token");
        try {
            const response = await fetch(`${API_BASE_URL}/api/open-offers/${projectId}/apply`, {
                method: 'POST',
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || 'Une erreur est survenue');
            }
            alert(`Vous avez marqué l'offre comme intéressé.`);
        } catch (err) {
            alert(err instanceof Error ? err.message : "Une erreur est survenue.");
        }
  };

  //  const handleInterested = async (offer: Offer) => {
  //       const token = localStorage.getItem("token");
  //       try {
  //           const response = await fetch(`${API_BASE_URL}/api/open-offers/${offer.id}/apply`, {
  //               method: 'POST',
  //               headers: {
  //                   Authorization: `Bearer ${token}`,
  //                   'Content-Type': 'application/json',
  //               },
  //           });

  //           if (!response.ok) {
  //               const errorData = await response.json();
  //               throw new Error(errorData.message || 'Une erreur est survenue');
  //           }
  //           alert(`Vous avez marqué l'offre '${offer.title}' comme intéressé.`);
  //       } catch (err) {
  //           alert(err instanceof Error ? err.message : "Une erreur est survenue.");
  //       }
  //   };

  return (
    <DashboardLayout
      title="Trouver des projets"
      subtitle="Explorez les projets disponibles et trouvez votre prochaine opportunité"
      actions={
        <div className="flex space-x-2">
          <Button
            variant="outline"
            leftIcon={<Filter className="h-5 w-5" />}
            onClick={() => setShowFilters(!showFilters)}
          >
            {showFilters ? 'Masquer les filtres' : 'Afficher les filtres'}
          </Button>
        </div>
      }
    >
      {/* Afficher les erreurs */}
      {error && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      {/* Barre de recherche */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Rechercher un projet..."
              className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex gap-2">
            <select
              className="px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              aria-label="Filtrer par statut"
            >
              <option value="all">Tous les statuts</option>
              <option value="open">Ouverts</option>
              <option value="in_progress">En cours</option>
              <option value="completed">Terminés</option>
              <option value="cancelled">Annulés</option>
            </select>
            <Button
              variant="outline"
              leftIcon={<SlidersHorizontal className="h-5 w-5" />}
              onClick={() => setShowFilters(!showFilters)}
            >
              Filtres
            </Button>
          </div>
        </div>

        {/* Filtres avancés */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-neutral-200 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Budget</label>
              <select
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                value={budgetFilter}
                onChange={(e) => setBudgetFilter(e.target.value)}
                aria-label="Filtrer par budget"
              >
                <option value="all">Tous les budgets</option>
                <option value="0-500">Moins de 500 €</option>
                <option value="500-1000">500 € - 1 000 €</option>
                <option value="1000-5000">1 000 € - 5 000 €</option>
                <option value="5000-10000">5 000 € - 10 000 €</option>
                <option value="10000">Plus de 10 000 €</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-neutral-700 mb-1">Catégorie</label>
              <select
                className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                aria-label="Filtrer par catégorie"
              >
                <option value="all">Toutes les catégories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
          </div>
        )}
      </div>

      {/* Statistiques */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex items-center">
          <div className="bg-blue-100 p-3 rounded-full mr-4">
            <Briefcase className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <p className="text-sm text-neutral-600">Projets disponibles</p>
            <p className="text-xl font-semibold">{projects.length}</p>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex items-center">
          <div className="bg-green-100 p-3 rounded-full mr-4">
            <DollarSign className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <p className="text-sm text-neutral-600">Budget moyen</p>
            <p className="text-xl font-semibold">
              {projects.length > 0
                ? `${Math.round(
                    projects.reduce(
                      (sum, project) => sum + parseInt(project.budget.replace(/[^0-9]/g, '')),
                      0
                    ) / projects.length
                  )} €`
                : '0 €'}
            </p>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-4 flex items-center">
          <div className="bg-purple-100 p-3 rounded-full mr-4">
            <Tag className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <p className="text-sm text-neutral-600">Catégories</p>
            <p className="text-xl font-semibold">{categories.length}</p>
          </div>
        </div>
      </div>

      {/* Liste des projets */}
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      ) : filteredProjects.length === 0 ? (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-8 text-center">
          <Clock className="h-16 w-16 text-neutral-300 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-neutral-800 mb-2">Aucun projet trouvé</h2>
          <p className="text-neutral-600 mb-6">
            Aucun projet ne correspond à vos critères de recherche. Essayez de modifier vos filtres.
          </p>
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-neutral-200">
            <h3 className="text-lg font-semibold text-neutral-900">
              {filteredProjects.length} projet{filteredProjects.length > 1 ? 's' : ''} disponible{filteredProjects.length > 1 ? 's' : ''}
            </h3>
          </div>
          <div className="p-6 grid grid-cols-1 gap-6">
            {filteredProjects.map(project => (
              <div key={project.id} className="border border-neutral-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-neutral-900 mb-2">{project.title}</h3>
                  <p className="text-neutral-600 mb-4 line-clamp-2">{project.description}</p>
                  
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.categories && project.categories.map((category, index) => (
                      <Badge key={index} color="blue">{category}</Badge>
                    ))}
                    <Badge color="green">{project.budget}</Badge>
                    <Badge color="purple">
                      {new Date(project.deadline).toLocaleDateString('fr-FR', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric'
                      })}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between mt-4">
                    <div className="flex items-center">
                      <div className="w-10 h-10 rounded-full bg-neutral-200 overflow-hidden mr-3">
                        {project.client?.avatar ? (
                          <img 
                            src={project.client.avatar} 
                            alt={project.client.name} 
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-primary-100 text-primary-700 font-semibold">
                            {project.client?.name.charAt(0)}
                          </div>
                        )}
                      </div>
                      <span className="text-sm text-neutral-700">{project.client?.name}</span>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        onClick={() => handleProjectClick(project.id)}
                      >
                        Voir détails
                      </Button>
                      {/* <Button
                        variant="primary"
                        onClick={() => handleApplyToProject(project.id)}
                      >
                        Postuler
                      </Button> */}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default ProfessionalProjectsPage;
