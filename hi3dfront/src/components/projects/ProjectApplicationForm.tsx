import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, DollarSign, Calendar, Clock, Upload, X, FileText, Send } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import FormInput from '../ui/FormInput';

interface ProjectApplicationFormProps {
  onCancel?: () => void;
}

const ProjectApplicationForm: React.FC<ProjectApplicationFormProps> = ({ onCancel }) => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [project, setProject] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  
  // Form state
  const [coverLetter, setCoverLetter] = useState('');
  const [proposedRate, setProposedRate] = useState('');
  const [estimatedDuration, setEstimatedDuration] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  
  // Form errors
  const [formErrors, setFormErrors] = useState({
    coverLetter: '',
    proposedRate: '',
    estimatedDuration: '',
  });

  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  // Fetch project details
  useEffect(() => {
    const fetchProject = async () => {
      setIsLoading(true);
      try {
        const response = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Erreur lors de la récupération des détails du projet');
        }

        const data = await response.json();
        setProject(data.offer);
        
        // Set default proposed rate based on project budget
        if (data.offer.budget) {
          const budgetValue = parseFloat(data.offer.budget.replace(/[^0-9.]/g, ''));
          setProposedRate(budgetValue.toString());
        }
        
        setError(null);
      } catch (err) {
        console.error('Error fetching project:', err);
        setError('Impossible de charger les détails du projet');
      } finally {
        setIsLoading(false);
      }
    };

    if (token && id) {
      fetchProject();
    }
  }, [token, id]);

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files);
      setFiles(prev => [...prev, ...newFiles]);
    }
  };

  // Remove file
  const handleRemoveFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Validate form
  const validateForm = (): boolean => {
    const errors = {
      coverLetter: '',
      proposedRate: '',
      estimatedDuration: '',
    };
    
    let isValid = true;
    
    if (!coverLetter.trim()) {
      errors.coverLetter = 'La lettre de motivation est requise';
      isValid = false;
    } else if (coverLetter.length < 100) {
      errors.coverLetter = 'La lettre de motivation doit contenir au moins 100 caractères';
      isValid = false;
    }
    
    if (!proposedRate.trim()) {
      errors.proposedRate = 'Le taux proposé est requis';
      isValid = false;
    } else if (isNaN(parseFloat(proposedRate))) {
      errors.proposedRate = 'Le taux proposé doit être un nombre';
      isValid = false;
    }
    
    if (!estimatedDuration.trim()) {
      errors.estimatedDuration = 'La durée estimée est requise';
      isValid = false;
    }
    
    setFormErrors(errors);
    return isValid;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setSubmitting(true);
    
    try {
      // In a real implementation, you would upload files and get their URLs
      // For now, we'll just simulate this
      
      const payload = {
        offer_id: parseInt(id!),
        professional_id: user.id,
        cover_letter: coverLetter,
        proposed_rate: proposedRate,
        estimated_duration: estimatedDuration,
        attachments: files.map(file => file.name), // In a real implementation, this would be file URLs
      };
      
      const response = await fetch(`${API_BASE_URL}/api/offer-applications`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error('Erreur lors de l\'envoi de la candidature');
      }

      // Navigate back to project details
      navigate(`/projects/${id}?applied=true`);
    } catch (err) {
      console.error('Error submitting application:', err);
      alert('Erreur lors de l\'envoi de la candidature');
    } finally {
      setSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      navigate(`/projects/${id}`);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  return (
    <DashboardLayout
      title="Postuler à un projet"
      subtitle={project ? project.title : 'Chargement...'}
      actions={
        <Button
          variant="outline"
          leftIcon={<ArrowLeft className="h-5 w-5" />}
          onClick={handleCancel}
        >
          Retour au projet
        </Button>
      }
    >
      {isLoading ? (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-neutral-600">Chargement des détails du projet...</p>
        </div>
      ) : error ? (
        <div className="bg-white rounded-lg border border-red-200 shadow-sm p-8 text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button
            variant="primary"
            onClick={() => navigate('/projects')}
          >
            Retour aux projets
          </Button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Project Summary */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">Résumé du projet</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <p className="text-sm text-neutral-500 mb-1">Budget</p>
                  <div className="flex items-center text-neutral-900 font-medium">
                    <DollarSign className="h-5 w-5 text-neutral-500 mr-1" />
                    {project?.budget}
                  </div>
                </div>
                
                <div>
                  <p className="text-sm text-neutral-500 mb-1">Date limite</p>
                  <div className="flex items-center text-neutral-900 font-medium">
                    <Calendar className="h-5 w-5 text-neutral-500 mr-1" />
                    {formatDate(project?.deadline)}
                  </div>
                </div>
                
                <div>
                  <p className="text-sm text-neutral-500 mb-1">Client</p>
                  <div className="flex items-center text-neutral-900 font-medium">
                    {project?.user?.first_name} {project?.user?.last_name}
                  </div>
                </div>
              </div>
              
              <div className="mt-4">
                <p className="text-sm text-neutral-500 mb-1">Description</p>
                <p className="text-neutral-700">
                  {project?.description}
                </p>
              </div>
            </div>
          </div>
          
          {/* Application Form */}
          <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
            <div className="px-6 py-4 border-b border-neutral-200">
              <h2 className="text-lg font-semibold text-neutral-900">Votre candidature</h2>
            </div>
            
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Cover Letter */}
              <div>
                <label htmlFor="coverLetter" className="block text-sm font-medium text-neutral-700 mb-1">
                  Lettre de motivation <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="coverLetter"
                  rows={6}
                  placeholder="Présentez-vous, expliquez pourquoi vous êtes intéressé par ce projet et pourquoi vous êtes qualifié pour le réaliser..."
                  value={coverLetter}
                  onChange={(e) => setCoverLetter(e.target.value)}
                  className={`w-full px-3 py-2 border ${
                    formErrors.coverLetter ? 'border-red-500' : 'border-neutral-300'
                  } rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500`}
                  required
                />
                {formErrors.coverLetter && (
                  <p className="mt-1 text-sm text-red-600">{formErrors.coverLetter}</p>
                )}
                <p className="mt-1 text-sm text-neutral-500">
                  {coverLetter.length} caractères (minimum 100)
                </p>
              </div>
              
              {/* Proposed Rate */}
              <FormInput
                label="Taux proposé (€)"
                id="proposedRate"
                type="number"
                placeholder="Ex: 1000"
                value={proposedRate}
                onChange={(e) => setProposedRate(e.target.value)}
                error={formErrors.proposedRate}
                icon={<DollarSign className="h-5 w-5 text-neutral-400" />}
                required
              />
              
              {/* Estimated Duration */}
              <FormInput
                label="Durée estimée"
                id="estimatedDuration"
                placeholder="Ex: 2 semaines"
                value={estimatedDuration}
                onChange={(e) => setEstimatedDuration(e.target.value)}
                error={formErrors.estimatedDuration}
                icon={<Clock className="h-5 w-5 text-neutral-400" />}
                helperText="Indiquez le temps nécessaire pour réaliser ce projet"
                required
              />
              
              {/* File Upload */}
              <div>
                <label className="block text-sm font-medium text-neutral-700 mb-1">
                  Fichiers joints (portfolio, exemples de travaux...)
                </label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-neutral-300 border-dashed rounded-md">
                  <div className="space-y-1 text-center">
                    <Upload className="mx-auto h-12 w-12 text-neutral-400" />
                    <div className="flex text-sm text-neutral-600">
                      <label
                        htmlFor="file-upload"
                        className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500"
                      >
                        <span>Télécharger des fichiers</span>
                        <input
                          id="file-upload"
                          name="file-upload"
                          type="file"
                          className="sr-only"
                          multiple
                          onChange={handleFileUpload}
                        />
                      </label>
                      <p className="pl-1">ou glisser-déposer</p>
                    </div>
                    <p className="text-xs text-neutral-500">
                      PNG, JPG, PDF jusqu'à 10MB
                    </p>
                  </div>
                </div>
                
                {/* File List */}
                {files.length > 0 && (
                  <ul className="mt-3 divide-y divide-neutral-200 border border-neutral-200 rounded-md overflow-hidden">
                    {files.map((file, index) => (
                      <li key={index} className="px-4 py-3 flex items-center justify-between text-sm">
                        <div className="flex items-center">
                          <FileText className="h-5 w-5 text-neutral-400 mr-2" />
                          <span className="truncate">{file.name}</span>
                        </div>
                        <button
                          type="button"
                          onClick={() => handleRemoveFile(index)}
                          className="ml-2 text-red-600 hover:text-red-800"
                        >
                          <X className="h-5 w-5" />
                        </button>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
              
              {/* Form Actions */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-neutral-200">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCancel}
                  disabled={submitting}
                >
                  Annuler
                </Button>
                
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={submitting}
                  leftIcon={<Send className="h-5 w-5" />}
                >
                  Envoyer ma candidature
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default ProjectApplicationForm;
