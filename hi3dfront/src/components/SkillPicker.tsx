// components/SkillPicker.tsx
"use client";
import React, { useMemo, useState } from "react";

export type SkillsByCategory = Record<string, string[]>;

export default function SkillPicker({
  categories,
  value,
  onChange,
  allowCustom = false,
  customPlaceholder = "Ajouter une compétence…",
}: {
  categories: SkillsByCategory;
  value: string[];
  onChange: (skills: string[]) => void;
  allowCustom?: boolean;
  customPlaceholder?: string;
}) {
  const [query, setQuery] = useState("");
  const [custom, setCustom] = useState("");

  const normalized = useMemo(() => {
    const q = query.trim().toLowerCase();
    if (!q) return categories;
    const filtered: SkillsByCategory = {};
    for (const [cat, list] of Object.entries(categories)) {
      const hits = list.filter((s) => s.toLowerCase().includes(q));
      if (hits.length) filtered[cat] = hits;
    }
    return filtered;
  }, [categories, query]);

  const isSelected = (s: string) => value.includes(s);

  const toggleSkill = (s: string) => {
    onChange(
      isSelected(s) ? value.filter((x) => x !== s) : [...value, s]
    );
  };

  const selectAllInCategory = (cat: string) => {
    const all = categories[cat];
    const merged = Array.from(new Set([...value, ...all]));
    onChange(merged);
  };

  const clearCategory = (cat: string) => {
    const all = categories[cat];
    onChange(value.filter((s) => !all.includes(s)));
  };

  const addCustom = () => {
    const s = custom.trim();
    if (!s) return;
    if (!value.includes(s)) onChange([...value, s]);
    setCustom("");
  };

  return (
    <div className="space-y-6">
      {/* Barre de recherche */}
      <div className="flex items-center gap-3">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Rechercher une compétence…"
          className="w-full rounded-lg border border-neutral-300 bg-white px-4 py-2 outline-none focus:ring-2 focus:ring-blue-500"
        />
        {query && (
          <button
            type="button"
            onClick={() => setQuery("")}
            className="px-3 py-2 rounded-lg border border-neutral-300 hover:bg-neutral-50"
          >
            Effacer
          </button>
        )}
      </div>

      {/* Liste par catégories */}
      <div className="space-y-6">
        {Object.entries(normalized).map(([cat, skills]) => {
          const selectedCount = skills.filter(isSelected).length;
          const totalInCat = (categories[cat] || []).length;

          return (
            <section key={cat} className="border border-neutral-200 rounded-xl">
              <header className="flex flex-wrap items-center justify-between gap-3 px-4 py-3 bg-neutral-50 rounded-t-xl">
                <div className="font-medium">
                  {cat}{" "}
                  <span className="text-sm text-neutral-500">
                    ({selectedCount}/{totalInCat})
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    type="button"
                    onClick={() => selectAllInCategory(cat)}
                    className="px-3 py-1.5 text-sm rounded-lg border border-blue-200 text-blue-700 bg-blue-50 hover:bg-blue-100"
                  >
                    Tout sélectionner
                  </button>
                  <button
                    type="button"
                    onClick={() => clearCategory(cat)}
                    className="px-3 py-1.5 text-sm rounded-lg border border-neutral-300 hover:bg-neutral-100"
                  >
                    Vider
                  </button>
                </div>
              </header>

              <div className="p-4">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-2">
                  {skills.map((s) => (
                    <label
                      key={s}
                      className={`inline-flex items-center gap-2 text-sm rounded-lg border px-3 py-2 cursor-pointer ${
                        isSelected(s)
                          ? "border-blue-300 bg-blue-50"
                          : "border-neutral-300 bg-white hover:bg-neutral-50"
                      }`}
                    >
                      <input
                        type="checkbox"
                        className="size-4 accent-blue-600"
                        checked={isSelected(s)}
                        onChange={() => toggleSkill(s)}
                      />
                      <span>{s}</span>
                    </label>
                  ))}
                </div>
              </div>
            </section>
          );
        })}
      </div>

      {/* Chips des sélectionnées */}
      <div className="space-y-2">
        <div className="text-sm font-medium">Compétences sélectionnées</div>
        <div className="min-h-[52px] p-3 border border-neutral-200 rounded-lg bg-neutral-50">
          {value.length ? (
            <div className="flex flex-wrap gap-2">
              {value.map((s) => (
                <span
                  key={s}
                  className="inline-flex items-center px-3 py-1 rounded-full bg-white border border-neutral-300 shadow-sm text-sm"
                >
                  {s}
                  <button
                    type="button"
                    onClick={() => toggleSkill(s)}
                    className="ml-1.5 text-neutral-500 hover:text-red-500"
                    aria-label={`Retirer ${s}`}
                    title={`Retirer ${s}`}
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          ) : (
            <div className="text-neutral-500 text-sm">Aucune compétence sélectionnée</div>
          )}
        </div>
      </div>

      {/* Ajout custom (optionnel) */}
      {allowCustom && (
        <div className="flex">
          <input
            type="text"
            value={custom}
            onChange={(e) => setCustom(e.target.value)}
            onKeyDown={(e) => e.key === "Enter" && (e.preventDefault(), addCustom())}
            className="block w-full px-3 py-2 border border-neutral-300 rounded-l-lg shadow-sm focus:ring-2 focus:ring-blue-500"
            placeholder={customPlaceholder}
          />
          <button
            type="button"
            onClick={addCustom}
            className="px-4 py-2 rounded-r-lg bg-blue-600 text-white hover:bg-blue-700"
          >
            Ajouter
          </button>
        </div>
      )}
    </div>
  );
}
