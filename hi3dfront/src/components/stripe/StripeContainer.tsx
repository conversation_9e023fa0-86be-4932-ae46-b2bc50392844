import React from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import PaymentForm from './PaymentForm';

// Assurez-vous de remplacer cette clé par votre clé publique Stripe
const stripePromise = loadStripe('pk_test_51OYqNsFKK6JoGdxmMWzT2SX9IciQGeQSItvJ8TxyoaCpVdrMs5dnpSCy2sSaygudhqbsnkznIavIu2l1hzmYwzaz00zTk4g7hM');

interface StripeContainerProps {
  selectedPlan: {
    name: string;
    price: number;
  } | null;
  // userId: number; // Supprimé car non nécessaire pour PaymentForm
}

const StripeContainer: React.FC<StripeContainerProps> = ({ selectedPlan }) => {
  return (
    <Elements stripe={stripePromise}>
      <PaymentForm selectedPlan={selectedPlan} />
    </Elements>
  );
};

export default StripeContainer; 