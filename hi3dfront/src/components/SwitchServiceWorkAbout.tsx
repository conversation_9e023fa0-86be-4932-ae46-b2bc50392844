import React from "react";

interface SwitchServiceWorkAboutProps {
  activeTab: string;
  onTabChange: (tab: 'services' | 'work' | 'about') => void;
}

const SwitchServiceWorkAbout: React.FC<SwitchServiceWorkAboutProps> = ({ activeTab, onTabChange }) => {
  return (
    <div className="w-full px-[10px] md:px-[40px] mx-auto mt-20 py-2">
      <div className="flex space-x-4">
        {/* Bouton Services */}
        <button
          type="button"
          onClick={() => onTabChange('services')}
          className="rounded-full py-2 px-8 font-sans font-medium text-base cursor-pointer transition-colors duration-200 shadow-sm"
          style={{
            fontFamily: "'Inter', sans-serif",
            backgroundColor: activeTab === 'services' ? "rgb(0, 110, 255)" : "#f6f7f8",
            fontWeight: 400,
            color: activeTab === 'services' ? "white" : "rgb(72, 72, 72)",
            fontSize: "16px",
          }}
          onMouseOver={(e) => {
            if (activeTab !== 'services') {
              e.currentTarget.style.backgroundColor = "rgb(0, 110, 255)";
              e.currentTarget.style.color = "white";
            }
          }}
          onMouseOut={(e) => {
            if (activeTab !== 'services') {
              e.currentTarget.style.backgroundColor = "#f6f7f8";
              e.currentTarget.style.color = "rgb(72, 72, 72)";
            }
          }}
        >
          Services
        </button>

        {/* Bouton Work */}
        <button
          type="button"
          onClick={() => onTabChange('work')}
          className="bg-transparent rounded-full py-2 px-8 font-sans font-medium text-base cursor-pointer transition-colors duration-200 shadow-sm"
          style={{
            fontFamily: "'Inter', sans-serif",
            backgroundColor: activeTab === 'work' ? "rgb(0, 110, 255)" : "#f6f7f8",
            fontWeight: 400,
            color: activeTab === 'work' ? "white" : "rgb(72, 72, 72)",
            fontSize: "16px",
          }}
          onMouseOver={(e) => {
            if (activeTab !== 'work') {
              e.currentTarget.style.backgroundColor = "rgb(0, 110, 255)";
              e.currentTarget.style.color = "white";
            }
          }}
          onMouseOut={(e) => {
            if (activeTab !== 'work') {
              e.currentTarget.style.backgroundColor = "#f6f7f8";
              e.currentTarget.style.color = "rgb(72, 72, 72)";
            }
          }}
        >
          Work
        </button>

        {/* Bouton About */}
        <button
          type="button"
          onClick={() => onTabChange('about')}
          className="bg-transparent rounded-full py-2 px-8 font-sans font-medium text-base cursor-pointer transition-colors duration-200 shadow-sm"
          style={{
            fontFamily: "'Inter', sans-serif",
            backgroundColor: activeTab === 'about' ? "rgb(0, 110, 255)" : "#f6f7f8",
            fontWeight: 400,
            color: activeTab === 'about' ? "white" : "rgb(72, 72, 72)",
            fontSize: "16px",
          }}
          onMouseOver={(e) => {
            if (activeTab !== 'about') {
              e.currentTarget.style.backgroundColor = "rgb(0, 110, 255)";
              e.currentTarget.style.color = "white";
            }
          }}
          onMouseOut={(e) => {
            if (activeTab !== 'about') {
              e.currentTarget.style.backgroundColor = "#f6f7f8";
              e.currentTarget.style.color = "rgb(72, 72, 72)";
            }
          }}
        >
          About
        </button>
      </div>
    </div>
  );
};

export default SwitchServiceWorkAbout;