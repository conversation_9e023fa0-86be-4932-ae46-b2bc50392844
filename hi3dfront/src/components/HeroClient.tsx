import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from 'react-router-dom';
import { API_BASE_URL } from '../config';
import { getAllCategories } from '../data/categories'
import { Button } from './ui/buttons';
import ClientProfileEditModal from "./ClientProfileEditModal";

interface HeroProfileProps {
  profile: any;
}



const HeroClient: React.FC<HeroProfileProps> = ({ profile }) => {
  const [isLargeScreen, setIsLargeScreen] = useState(false);

  const navigate = useNavigate();

  const [showQuoteModal, setShowQuoteModal] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);

  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  const getUrlProlfil = (imagePath: string | undefined, defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  const getImageUrl = (imagePath: string | undefined, defaultImage: string = "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D") => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith('http')) return imagePath;
    if (imagePath.startsWith('/')) return `${API_BASE_URL}/storage${imagePath}`;
    return `${API_BASE_URL}/storage/${imagePath}`;
  };

  useEffect(() => {
    const checkScreenSize = () => {
      setIsLargeScreen(window.innerWidth >= 1025);
    };

    // Vérifier la taille initiale
    checkScreenSize();

    // Écouter les changements de taille
    window.addEventListener("resize", checkScreenSize);

    // Nettoyer l'écouteur d'événement
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);



  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <div className="flex justify-center items-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  const handleBackToList = () => {
    // navigate('/lists-independants');
    navigate(-1);
  }; 

  return (
    <>
      <div className="bg-white w-full flex items-stretch">
        <div
          className={`w-full px-[10px] md:px-[40px] mx-auto grid ${isLargeScreen ? "grid-cols-2" : "grid-cols-1"
            } gap-10 items-end`} >
          {/* Left column - content */}
          <div className="flex flex-col justify-end text-left h-full w-full">
            {/* Container for profile image and title in column */}
            <div className="flex flex-col items-start mb-4 md:mb-5 lg:mb-6">
              <img
                src={getUrlProlfil(profile?.avatar)}
                alt={`${profile?.first_name} ${profile?.last_name}`}
                className="w-16 h-16 sm:w-18 sm:h-18 md:w-20 md:h-20 lg:w-22 lg:h-22 xl:w-24 xl:h-24 rounded-full object-cover mb-4 md:mb-5 lg:mb-6"
                style={{ maxWidth: "100px", maxHeight: "100px" }}
                onError={(e) => {
                  e.currentTarget.onerror = null;
                  e.currentTarget.src = 'https://hi-3d.com/wp-content/uploads/2025/08/Photo-de-profil-03.jpg';
                }}
              />
              <h1
                className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-normal text-left pb-1 md:pb-1.5 lg:pb-2"
                style={{
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 500,
                  lineHeight: "1em",
                  color: "#0D0C22",
                }}
              >
                {`${profile?.first_name} ${profile?.last_name}`}
              </h1>
              <h2
                className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-normal text-left mt-1 md:mt-1.5 lg:mt-2"
                style={{
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 500,
                  color: "#0D0C22",
                  lineHeight: "0.89em",
                  fontSize: "clamp(24px, 4vw, 56px)"
                }}
              >
                {profile.position ? `${profile.position}` : 'Client'}
                {profile.company_name ? ` chez ${profile.company_name}` : ''}
              </h2>
            </div>

            <p
              className="text-sm sm:text-base md:text-lg lg:text-xl xl:text-2xl mb-6 md:mb-7 lg:mb-8 xl:mb-10 font-normal text-left"
              style={{
                fontFamily: "'Inter', sans-serif",
                fontWeight: 400,
                lineHeight: "1.4em",
                color: "#0D0C22",
                fontSize: "clamp(14px, 3vw, 20px)"
              }}
            >
              I believe 3D is more than just a tool – it's a medium of expression,
              a language without words, and a bridge between imagination and
              reality.
            </p>
            
            <div className="flex items-center justify-start space-x-2 sm:space-x-3 md:space-x-3 lg:space-x-4 xl:space-x-4 flex-wrap gap-2">
              <button
                type="button"
                onClick={() => setOpen(true)}
                className="bg-transparent text-black rounded-full py-2 px-4 sm:py-2 sm:px-5 md:py-2.5 md:px-6 lg:py-3 lg:px-7 xl:py-3 xl:px-8 font-sans font-medium text-xs sm:text-sm md:text-base lg:text-base xl:text-base cursor-pointer transition-colors duration-200 shadow-sm hover:bg-black"
                style={{
                  fontFamily: "Arial, sans-serif",
                  backgroundColor: "#f6f7f8",
                  whiteSpace: "nowrap"
                }}
                onMouseOver={(e) => (e.currentTarget.style.color = "#9ca3af")}
                onMouseOut={(e) => (e.currentTarget.style.color = "#000")}
              >
                Edit my profile
              </button>

              <div
                className="flex items-center justify-center cursor-pointer transition-colors duration-200 rounded-xl"
                style={{
                  width: "44px",
                  height: "36px",
                  backgroundColor: "#f6f7f8",
                }}
                onMouseOver={(e) =>
                  (e.currentTarget.style.backgroundColor = "#e5e7eb")
                }
                onMouseOut={(e) =>
                  (e.currentTarget.style.backgroundColor = "#f6f7f8")
                }
              >
                <p className="text-sm md:text-base">...</p>
              </div>
            </div>
          </div>

          {/* Right column - image (visible seulement à partir de 1025px) */}
          {isLargeScreen && (
            <div className="flex justify-end w-full h-[400px] lg:h-[500px]">
              <img
                src={getUrlProlfil(profile?.avatar) || "https://hi-3d.com/wp-content/uploads/2025/08/basel-lindenhofareal-miller-maranta-visualisierung-1-922x1024.jpg"}
                alt="3D Art"
                className="rounded-lg object-cover w-full h-full"
                onError={(e) => {
                  e.currentTarget.onerror = null; // empêche les boucles infinies
                  e.currentTarget.src = 'https://hi-3d.com/wp-content/uploads/2025/08/basel-lindenhofareal-miller-maranta-visualisierung-1-922x1024.jpg'; // chemin de l'image par défaut
                }}
              />
            </div>
          )}
        </div>
      </div>

      <ClientProfileEditModal
        open={open}
        onClose={() => setOpen(false)}
        portal={false} // <-- mets false si tu ouvres par-dessus un autre popup
        onSaved={(resp) => {
          console.log("Profil mis à jour:", resp);
          window.location.reload();
          // rafraîchis l'UI/le cache si besoin
        }}
      />

    </>
  );
};

export default HeroClient;