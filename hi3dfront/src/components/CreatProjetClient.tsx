import React from 'react';

// Types pour la compatibilité
interface Offer {
  id: number;
  title: string;
  categories: string[];
  budget: string;
  deadline: string;
  company: string;
  website: string;
  description: string;
  files: File[];
  recruitmentType: 'company' | 'personal';
  openToApplications: boolean;
  autoInvite: boolean;
  status: string;
  filters?: any;
}

interface CreatProjetClientProps {
  onClose?: () => void;
  onAddOffer?: (offer: Offer) => void;
  existingOffer?: Offer;
}

// Composant vide pour la compatibilité
const CreatProjetClient: React.FC<CreatProjetClientProps> = ({ onClose, onAddOffer, existingOffer }) => {
  return (
    <div className="hidden">
      {/* Ce composant est maintenu pour la compatibilité mais n'est plus utilisé */}
    </div>
  );
};

export default CreatProjetClient;
