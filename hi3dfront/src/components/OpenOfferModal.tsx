// components/OpenOfferModal.tsx
"use client";
import React, { useEffect, useMemo, useState } from "react";
import Modal from "./Modal";
import { API_BASE_URL } from "./../config";

type BudgetKey =
  | "$ 5-100" | "$ 100-250" | "$ 250-500" | "$ 500-1000"
  | "$ 1'000-2'500" | "$ 2'500-5'000" | "$ 5'000-10'000"
  | "$ 10'000-25'000" | "$ 25'000 +" | "OTHER";

const BUDGETS: Exclude<BudgetKey, "OTHER">[] = [
  "$ 5-100", "$ 100-250", "$ 250-500", "$ 500-1000",
  "$ 1'000-2'500", "$ 2'500-5'000", "$ 5'000-10'000",
  "$ 10'000-25'000", "$ 25'000 +",
];

// 👉 Ta liste de catégories
const CATEGORIES: string[] = [
  "Modélisation 3D","Animation 3D","Rendu 3D","Texturing & Shading","Rigging & Skinning",
  "Compositing 3D","Effets Visuels (VFX)","Architecture 3D","Jeux Vidéo 3D","Produit 3D & Industriel",
  "Personnages 3D","Environnements 3D","Motion Design 3D","Réalité Virtuelle (VR)","Réalité Augmentée (AR)",
  "Scan 3D & Photogrammétrie","Modélisation Médicale 3D","Impression 3D","Simulations Physiques","Éclairage 3D",
];

export type OpenOfferMinimal = {
  id?: number;
  user_id?: number;
  title: string;
  budget: string;
  deadline: string;          // yyyy-mm-dd (comme <input type="date">)
  description: string;

  categories?: string[];
  company?: string;
  website?: string;
  recruitment_type?: "company" | "project";
  open_to_applications?: boolean;
  auto_invite?: boolean;
};

export default function OpenOfferModal({
  open,
  onClose,
  initialData,
  onSaved,
  title = "Describe your project",
  // Endpoints réels de ta page existante
  createUrl = `${API_BASE_URL}/api/open-offers`,
  updateUrl = (id: number) => `${API_BASE_URL}/api/open-offers/${id}`,
  token: tokenProp,
  user,
  inviteProfessionalId,       // si présent => auto_invite true + envoi d'invitation après création
  portal = true,
}: {
  open: boolean;
  onClose: () => void;
  initialData?: Partial<OpenOfferMinimal>;
  onSaved?: (payload: OpenOfferMinimal) => void;
  title?: string;
  createUrl?: string;
  updateUrl?: (id: number) => string;
  token?: string | null | undefined;
  user?: { id?: number; [k: string]: any };
  inviteProfessionalId?: number | null;
  portal?: boolean;
}) {
  const isEditMode = !!initialData?.id;

  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Champs UI
  const [offerTitle, setOfferTitle] = useState(initialData?.title ?? "");
  const [deadline, setDeadline] = useState<string>(() => {
    if (!initialData?.deadline) return "";
    const d = new Date(initialData.deadline);
    return isNaN(d.getTime()) ? "" : d.toISOString().split("T")[0];
  });
  const [description, setDescription] = useState(initialData?.description ?? "");

  // Budget preset / custom
  const deducedPreset = useMemo<BudgetKey>(() => {
    const raw = (initialData?.budget ?? "").trim();
    if (!raw) return "$ 5-100";
    const match = BUDGETS.find((b) => b === raw);
    return (match as BudgetKey) || "OTHER";
  }, [initialData?.budget]);

  const [budgetPreset, setBudgetPreset] = useState<BudgetKey>(deducedPreset);
  const [budgetCustom, setBudgetCustom] = useState<string>(() =>
    deducedPreset === "OTHER" ? (initialData?.budget ?? "") : ""
  );
  const budgetValue = budgetPreset === "OTHER" ? budgetCustom : budgetPreset;

  // Nouveaux champs attendus par l’API (exemple qui marche)
  const [categories, setCategories] = useState<string[]>(initialData?.categories ?? []);
  const [company, setCompany] = useState(initialData?.company ?? "");
  const [website, setWebsite] = useState(initialData?.website ?? "");
  const [recruitmentType, setRecruitmentType] = useState<"company" | "project">(
    (initialData?.recruitment_type as any) ?? "company"
  );
  const [openToApplications, setOpenToApplications] = useState<boolean>(
    initialData?.open_to_applications ?? true
  );
  // si inviteProfessionalId existe, auto_invite forcé à true ; sinon toggle manuel
  const [autoInvite, setAutoInvite] = useState<boolean>(
    inviteProfessionalId ? true : (initialData?.auto_invite ?? false)
  );

  useEffect(() => {
    if (open) setError(null);
  }, [open]);

  const toggleCategory = (c: string) =>
    setCategories((arr) => (arr.includes(c) ? arr.filter((x) => x !== c) : [...arr, c]));

  const isValidUrl = (u: string) => {
    try { new URL(u); return true; } catch { return false; }
  };

  // ✅ validation stricte pour éviter le 402
  const validate = () => {
    if (!offerTitle.trim()) return "Le titre de l’offre est requis";
    if (!budgetValue.trim()) return "Le budget est requis";
    if (!description.trim()) return "La description est requise";
    if (!deadline) return "La date limite est requise";
    const d = new Date(deadline);
    const today = new Date();
    d.setHours(0,0,0,0); today.setHours(0,0,0,0);
    if (d < today) return "La date limite doit être dans le futur";

    // if (categories.length === 0) return "Sélectionnez au moins une catégorie";
    // if (!company.trim()) return "Le champ société (company) est requis";
    // if (!website.trim()) return "Le site web est requis";
    // if (!isValidUrl(website.trim())) return "L’URL du site web n’est pas valide";
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Contrôle de propriété en édition (comme ta page)
    if (isEditMode && initialData?.user_id && user?.id && initialData.user_id !== user.id) {
      setError("Vous n'êtes pas autorisé à modifier cette offre. Seul le propriétaire peut la modifier.");
      return;
    }

    const err = validate();
    if (err) { setError(err); return; }

    setSaving(true);
    setError(null);
    try {
      const tk = tokenProp ?? localStorage.getItem("token");
      if (!tk) throw new Error("Vous devez être connecté.");

      // IMPORTANT: on envoie bien "YYYY-MM-DD" (pas ISO) comme dans ton exemple
      const formattedDeadline = deadline;

      const payload = {
        title: offerTitle.trim(),
        categories,
        budget: budgetValue.trim(),
        deadline: formattedDeadline,
        company: company.trim(),
        website: website.trim(),
        description: description.trim(),
        recruitment_type: recruitmentType,
        open_to_applications: openToApplications,
        auto_invite: inviteProfessionalId ? true : autoInvite,
        // status / filters optionnels — non envoyés ici pour coller à l’exemple OK
      };

      const url = isEditMode ? updateUrl(initialData!.id!) : createUrl;
      const method = isEditMode ? "PUT" : "POST";

      const res = await fetch(url, {
        method,
        headers: { Authorization: `Bearer ${tk}`, "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      const data = await res.json().catch(() => ({}));
      if (!res.ok) {
        // log utile
        console.error("Erreur API:", res.status, data);
        if (res.status === 403) {
          throw new Error(`Vous n'êtes pas autorisé à ${isEditMode ? "modifier" : "créer"} cette offre.`);
        }
        // ton backend utilise 402 pour validation => on renvoie le message backend si dispo
        throw new Error(data?.message || `Erreur (${res.status}) lors de ${isEditMode ? "la modification" : "la création"} de l’offre`);
      }

      // ID renvoyé: open_offer.id ou id
      const offerId = data?.open_offer?.id || data?.id || initialData?.id;

      // auto-invite si demandé
      if (!isEditMode && offerId && inviteProfessionalId) {
        try {
          await fetch(`${API_BASE_URL}/api/open-offers/${offerId}/invite`, {
            method: "POST",
            headers: { Authorization: `Bearer ${tk}`, "Content-Type": "application/json" },
            body: JSON.stringify({ professional_id: inviteProfessionalId }),
          });
        } catch (invErr) {
          console.error("Erreur d'invitation:", invErr);
        }
      }

      const saved: OpenOfferMinimal = {
        id: offerId, user_id: initialData?.user_id,
        title: payload.title, budget: payload.budget, deadline: payload.deadline, description: payload.description,
        categories: payload.categories, company: payload.company, website: payload.website,
        recruitment_type: payload.recruitment_type, open_to_applications: payload.open_to_applications,
        auto_invite: payload.auto_invite,
      };

      onSaved?.(saved);
      onClose();
    } catch (e: any) {
      setError(e?.message || "Impossible d’enregistrer l’offre");
    } finally {
      setSaving(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose} title={title} maxWidthClass="max-w-3xl" portal={portal}>
      <form onSubmit={handleSubmit} className="mt-2">
        <div className="max-h-[70vh] overflow-y-auto pr-2 space-y-6 px-9">

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">{error}</div>
          )}

          {/* Title */}
          <div className="space-y-2">
            <label htmlFor="offer-title" className="block" style={{ fontSize: 14, fontFamily: "Inter, sans-serif", fontWeight: 500 }}>
              Titre de l’offre *
            </label>
            <input
              id="offer-title" type="text" autoComplete="off"
              placeholder="Ex: Personnage 3D pour trailer"
              className="w-full rounded-xl px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
              style={{ borderRadius: 20, border: "1px solid #1f1f1fff", backgroundColor: "#f6f7f8", fontFamily: "Inter, sans-serif" }}
              value={offerTitle} onChange={(e) => setOfferTitle(e.target.value)} required
            />
          </div>

          {/* Categories */}
          {/* <fieldset className="space-y-3">
            <legend className="text-sm font-medium" style={{ fontSize: 14, fontFamily: "Inter, sans-serif", fontWeight: 500 }}>
              Catégories 
            </legend>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
              {CATEGORIES.map((c) => {
                const checked = categories.includes(c);
                return (
                  <label key={c}
                    className={`inline-flex items-center gap-2 text-sm rounded-lg border px-3 py-2 cursor-pointer ${
                      checked ? "border-blue-300 bg-blue-50" : "border-gray-300 bg-white hover:bg-gray-50"
                    }`}
                  >
                    <input type="checkbox" className="size-4 accent-blue-600"
                      checked={checked} onChange={() => toggleCategory(c)} />
                    <span>{c}</span>
                  </label>
                );
              })}
            </div>
          </fieldset> */}

          {/* Budget */}
          <fieldset className="space-y-3">
            <legend className="text-sm font-medium" style={{ fontSize: 14, fontFamily: "Inter, sans-serif", fontWeight: 500 }}>
              Budget *
            </legend>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-x-4 gap-y-3">
              {BUDGETS.map((b) => (
                <label key={b} className="inline-flex items-center gap-2 text-sm cursor-pointer p-2 rounded-lg hover:bg-gray-50 transition-colors" style={{ fontFamily: "Inter, sans-serif" }}>
                  <input type="radio" name="budget" value={b}
                    checked={budgetPreset === b}
                    onChange={() => { setBudgetPreset(b); setBudgetCustom(""); }}
                    className="size-4 accent-black" />
                  <span>{b}</span>
                </label>
              ))}
              <label className="inline-flex items-center gap-2 text-sm cursor-pointer p-2 rounded-lg hover:bg-gray-50 transition-colors" style={{ fontFamily: "Inter, sans-serif" }}>
                <input type="radio" name="budget" value="OTHER"
                  checked={budgetPreset === "OTHER"} onChange={() => setBudgetPreset("OTHER")}
                  className="size-4 accent-black" />
                <span>Autre</span>
              </label>
            </div>
            {budgetPreset === "OTHER" && (
              <div className="mt-2">
                <input
                  type="text" placeholder="Ex: 2000-5000 EUR"
                  className="w-full rounded-xl px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
                  style={{ borderRadius: 20, border: "1px solid #1f1f1fff", backgroundColor: "#f6f7f8", fontFamily: "Inter, sans-serif" }}
                  value={budgetCustom} onChange={(e) => setBudgetCustom(e.target.value)} required
                />
              </div>
            )}
          </fieldset>

          {/* Deadline */}
          <div className="space-y-2">
            <label htmlFor="offer-deadline" className="block" style={{ fontSize: 14, fontFamily: "Inter, sans-serif", fontWeight: 500 }}>
              Date limite *
            </label>
            <input
              id="offer-deadline" type="date"
              className="w-full rounded-xl px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
              style={{ borderRadius: 20, border: "1px solid #1f1f1fff", backgroundColor: "#f6f7f8", fontFamily: "Inter, sans-serif" }}
              value={deadline} onChange={(e) => setDeadline(e.target.value)} required
            />
          </div>

          {/* Company & Website */}
          {/* <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="block" style={{ fontSize: 14, fontFamily: "Inter, sans-serif", fontWeight: 500 }}>Société  </label>
              <input
                type="text" placeholder="SMARTEK"
                className="w-full rounded-xl px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
                style={{ borderRadius: 20, border: "1px solid #1f1f1fff", backgroundColor: "#f6f7f8", fontFamily: "Inter, sans-serif" }}
                value={company} onChange={(e) => setCompany(e.target.value)} 
              />
            </div>
            <div className="space-y-2">
              <label className="block" style={{ fontSize: 14, fontFamily: "Inter, sans-serif", fontWeight: 500 }}>Site web  </label>
              <input
                type="text" placeholder="https://www.entreprisetest.com"
                className="w-full rounded-xl px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
                style={{ borderRadius: 20, border: "1px solid #1f1f1fff", backgroundColor: "#f6f7f8", fontFamily: "Inter, sans-serif" }}
                value={website} onChange={(e) => setWebsite(e.target.value)}
              />
            </div>
          </div> */}

          {/* Recruitment type */}
          {/* <fieldset className="space-y-2">
            <legend className="text-sm font-medium" style={{ fontSize: 14, fontFamily: "Inter, sans-serif", fontWeight: 500 }}>
              Type de recrutement 
            </legend>
            <div className="flex flex-wrap gap-4">
              {(["company","project"] as const).map((v) => (
                <label key={v} className="inline-flex items-center gap-2 text-sm cursor-pointer">
                  <input type="radio" name="recruitment_type" value={v}
                    checked={recruitmentType === v} onChange={() => setRecruitmentType(v)} className="size-4 accent-black" />
                  <span>{v === "company" ? "company" : "project"}</span>
                </label>
              ))}
            </div>
          </fieldset> */}

          {/* Switches */}
          {/* <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <label className="inline-flex items-center gap-2 text-sm cursor-pointer">
              <input type="checkbox" className="size-4 accent-blue-600"
                checked={openToApplications} onChange={(e) => setOpenToApplications(e.target.checked)} />
              <span>Ouvrir aux candidatures</span>
            </label>

            <label className="inline-flex items-center gap-2 text-sm cursor-pointer">
              <input type="checkbox" className="size-4 accent-blue-600"
                checked={inviteProfessionalId ? true : autoInvite}
                onChange={(e) => setAutoInvite(e.target.checked)}
                disabled={!!inviteProfessionalId}
              />
              <span>Auto-inviter le professionnel</span>
              {inviteProfessionalId ? <em className="text-xs text-gray-500">(forcé via inviteProfessionalId)</em> : null}
            </label>
          </div> */}

          {/* Description */}
          <div className="space-y-2">
            <label htmlFor="offer-desc" className="block" style={{ fontSize: 14, fontFamily: "Inter, sans-serif", fontWeight: 500 }}>
              Description *
            </label>
            <textarea
              id="offer-desc" rows={6}
              className="w-full rounded-xl px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-gray-400"
              style={{ borderRadius: 20, border: "1px solid #1f1f1fff", backgroundColor: "#f6f7f8", fontFamily: "Inter, sans-serif" }}
              placeholder="Description détaillée de l'offre ouverte avec tous les champs."
              value={description} onChange={(e) => setDescription(e.target.value)} required
            />
          </div>
        </div>

        {/* Footer */}
        <div className="pt-5 px-9">
          <button
            type="submit" disabled={saving}
            className="w-full rounded-xl bg-[#006eff] px-6 py-3 text-white font-semibold tracking-wide disabled:opacity-70"
            style={{ borderRadius: 20, fontSize: 20, fontWeight: 500, fontFamily: "Inter, sans-serif", height: 58 }}
          >
            {saving ? (isEditMode ? "Mise à jour…" : "Création…") : isEditMode ? "Mettre à jour l’offre" : "Créer l’offre"}
          </button>
        </div>
      </form>
    </Modal>
  );
}
