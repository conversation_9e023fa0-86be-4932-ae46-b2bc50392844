import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Send, ArrowLeft, Paperclip, Image, File, X, Check, Clock, Calendar, DollarSign, User, Star, MessageSquare } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
import Avatar from '../ui/Avatar';

interface Message {
  id: number;
  sender_id: number;
  recipient_id: number;
  content: string;
  attachment_url?: string;
  attachment_type?: 'image' | 'document';
  created_at: string;
  read: boolean;
}

interface Conversation {
  id: number;
  service_id?: number;
  service_title?: string;
  other_user: {
    id: number;
    first_name: string;
    last_name: string;
    profile_picture_path?: string;
  };
  last_message?: {
    content: string;
    created_at: string;
  };
  status: 'active' | 'completed' | 'cancelled';
}

interface ServiceDetails {
  id: number;
  title: string;
  price: number;
  execution_time: string;
  imageUrl?: string;
}

const ServiceConversation: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [attachment, setAttachment] = useState<File | null>(null);
  const [showAttachmentOptions, setShowAttachmentOptions] = useState(false);
  const [serviceDetails, setServiceDetails] = useState<ServiceDetails | null>(null);
  const [showServiceDetails, setShowServiceDetails] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');

  // Charger la conversation et les messages
  useEffect(() => {
    const fetchConversation = async () => {
      if (!token || !id) return;

      setIsLoading(true);
      try {
        // Récupérer les détails de la conversation
        const conversationResponse = await fetch(`${API_BASE_URL}/api/conversations/${id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!conversationResponse.ok) {
          throw new Error('Erreur lors de la récupération de la conversation');
        }

        const conversationData = await conversationResponse.json();
        setConversation(conversationData);

        // Récupérer les messages de la conversation
        const messagesResponse = await fetch(`${API_BASE_URL}/api/conversations/${id}/messages`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!messagesResponse.ok) {
          throw new Error('Erreur lors de la récupération des messages');
        }

        const messagesData = await messagesResponse.json();
        setMessages(messagesData);

        // Si la conversation est liée à un service, récupérer les détails du service
        if (conversationData.service_id) {
          const serviceResponse = await fetch(`${API_BASE_URL}/api/service-offers/${conversationData.service_id}`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });

          if (serviceResponse.ok) {
            const serviceData = await serviceResponse.json();
            setServiceDetails({
              id: serviceData.id,
              title: serviceData.title,
              price: serviceData.price,
              execution_time: serviceData.execution_time,
              imageUrl: serviceData.files && serviceData.files.length > 0 ? serviceData.files[0] : undefined,
            });
          }
        }

        // Marquer les messages comme lus
        await fetch(`${API_BASE_URL}/api/conversations/${id}/mark-read`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger la conversation');
      } finally {
        setIsLoading(false);
      }
    };

    fetchConversation();

    // Mettre en place un polling pour récupérer les nouveaux messages
    const intervalId = setInterval(() => {
      fetchNewMessages();
    }, 10000); // Toutes les 10 secondes

    return () => clearInterval(intervalId);
  }, [id, token]);

  // Récupérer les nouveaux messages
  const fetchNewMessages = async () => {
    if (!token || !id) return;

    try {
      const lastMessageId = messages.length > 0 ? messages[messages.length - 1].id : 0;

      const response = await fetch(`${API_BASE_URL}/api/conversations/${id}/messages?after=${lastMessageId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des nouveaux messages');
      }

      const newMessagesData = await response.json();

      if (newMessagesData.length > 0) {
        setMessages(prev => [...prev, ...newMessagesData]);

        // Marquer les messages comme lus
        await fetch(`${API_BASE_URL}/api/conversations/${id}/mark-read`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
      }
    } catch (err) {
      console.error('Erreur lors de la récupération des nouveaux messages:', err);
    }
  };

  // Faire défiler vers le bas lorsque de nouveaux messages sont ajoutés
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Envoyer un message
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if ((!newMessage.trim() && !attachment) || !token || !id) {
      return;
    }

    try {
      const formData = new FormData();
      formData.append('content', newMessage);
      formData.append('conversation_id', id);

      if (attachment) {
        formData.append('attachment', attachment);
      }

      const response = await fetch(`${API_BASE_URL}/api/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Erreur lors de l\'envoi du message');
      }

      const messageData = await response.json();
      setMessages(prev => [...prev, messageData]);
      setNewMessage('');
      setAttachment(null);
      setShowAttachmentOptions(false);
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible d\'envoyer le message');
    }
  };

  // Gérer l'ajout d'une pièce jointe
  const handleAttachment = (type: 'image' | 'document') => {
    if (fileInputRef.current) {
      fileInputRef.current.accept = type === 'image' ? 'image/*' : '.pdf,.doc,.docx,.xls,.xlsx,.txt';
      fileInputRef.current.click();
    }
    setShowAttachmentOptions(false);
  };

  // Gérer le changement de fichier
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setAttachment(file);
  };

  // Formater la date
  const formatMessageDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);

    // Si c'est aujourd'hui, afficher l'heure
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' });
    }

    // Si c'est hier, afficher "Hier" et l'heure
    if (date.toDateString() === yesterday.toDateString()) {
      return `Hier, ${date.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}`;
    }

    // Sinon, afficher la date complète
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !conversation) {
    return (
      <DashboardLayout>
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
        >
          {error || 'Conversation non trouvée'}
        </Alert>
        <div className="mt-6 text-center">
          <Button
            variant="outline"
            onClick={() => navigate('/dashboard/messages')}
          >
            Retour aux messages
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout
      title="Conversation"
      subtitle={conversation.service_title ? `À propos de: ${conversation.service_title}` : undefined}
      actions={
        <Button
          variant="outline"
          leftIcon={<ArrowLeft className="h-5 w-5" />}
          onClick={() => navigate('/dashboard/messages')}
        >
          Retour aux messages
        </Button>
      }
    >
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Zone de conversation */}
        <div className="lg:col-span-3 bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden flex flex-col h-[calc(100vh-240px)]">
          {/* En-tête de la conversation */}
          <div className="px-6 py-4 border-b border-neutral-200 flex justify-between items-center">
            <div className="flex items-center">
              <Avatar
                size="md"
                src={conversation.other_user.profile_picture_path}
                fallback={(conversation.other_user.first_name || 'U').charAt(0)}
                className="mr-3"
              />
              <div>
                <h3 className="text-lg font-semibold text-neutral-900">
                  {conversation.other_user.first_name} {conversation.other_user.last_name}
                </h3>
                <div className="flex items-center text-sm text-neutral-500">
                  <span className="inline-block w-2 h-2 rounded-full bg-green-500 mr-2"></span>
                  <span>En ligne</span>
                </div>
              </div>
            </div>

            {serviceDetails && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowServiceDetails(!showServiceDetails)}
              >
                {showServiceDetails ? 'Masquer les détails' : 'Voir les détails du service'}
              </Button>
            )}
          </div>

          {/* Détails du service (si disponible) */}
          {showServiceDetails && serviceDetails && (
            <div className="px-6 py-3 bg-neutral-50 border-b border-neutral-200">
              <div className="flex items-center">
                {serviceDetails.imageUrl && (
                  <img
                    src={serviceDetails.imageUrl}
                    alt={serviceDetails.title}
                    className="h-12 w-12 object-cover rounded-md mr-3"
                  />
                )}
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-neutral-900">{serviceDetails.title}</h4>
                  <div className="flex items-center mt-1 text-sm text-neutral-500">
                    <DollarSign className="h-4 w-4 mr-1" />
                    <span className="mr-3">{serviceDetails.price} €</span>
                    <Clock className="h-4 w-4 mr-1" />
                    <span>{serviceDetails.execution_time}</span>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate(`/services/${serviceDetails.id}`)}
                >
                  Voir le service
                </Button>
              </div>
            </div>
          )}

          {/* Messages */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4">
            {messages.length === 0 ? (
              <div className="text-center py-12">
                <div className="mx-auto w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mb-4">
                  <MessageSquare className="h-8 w-8 text-neutral-400" />
                </div>
                <h3 className="text-lg font-medium text-neutral-700 mb-1">Aucun message</h3>
                <p className="text-neutral-500">Commencez la conversation en envoyant un message</p>
              </div>
            ) : (
              messages.map((message) => {
                const isCurrentUser = message.sender_id === currentUser.id;
                return (
                  <div
                    key={message.id}
                    className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className="flex items-end">
                      {!isCurrentUser && (
                        <Avatar
                          size="sm"
                          src={conversation.other_user.profile_picture_path}
                          fallback={(conversation.other_user.first_name || 'U').charAt(0)}
                          className="mr-2 mb-1"
                        />
                      )}
                      <div
                        className={`max-w-md rounded-lg px-4 py-2 ${
                          isCurrentUser
                            ? 'bg-primary-600 text-white rounded-br-none'
                            : 'bg-neutral-100 text-neutral-900 rounded-bl-none'
                        }`}
                      >
                        {message.content}

                        {message.attachment_url && (
                          <div className="mt-2">
                            {message.attachment_type === 'image' ? (
                              <img
                                src={message.attachment_url}
                                alt="Pièce jointe"
                                className="max-w-full rounded-md"
                              />
                            ) : (
                              <a
                                href={message.attachment_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center text-sm font-medium underline"
                              >
                                <File className="h-4 w-4 mr-1" />
                                Voir le document
                              </a>
                            )}
                          </div>
                        )}

                        <div className={`text-xs mt-1 ${isCurrentUser ? 'text-primary-100' : 'text-neutral-500'}`}>
                          {formatMessageDate(message.created_at)}
                          {isCurrentUser && (
                            <span className="ml-2">
                              {message.read ? (
                                <Check className="h-3 w-3 inline" />
                              ) : (
                                <Clock className="h-3 w-3 inline" />
                              )}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Formulaire d'envoi de message */}
          <div className="px-6 py-4 border-t border-neutral-200">
            <form onSubmit={handleSendMessage} className="flex items-end space-x-2">
              <div className="relative flex-1">
                {attachment && (
                  <div className="absolute bottom-full mb-2 bg-neutral-100 rounded-md p-2 flex items-center">
                    <span className="text-sm text-neutral-700 truncate max-w-xs">
                      {attachment.name}
                    </span>
                    <button
                      type="button"
                      onClick={() => setAttachment(null)}
                      className="ml-2 text-neutral-500 hover:text-neutral-700"
                      title="Supprimer la pièce jointe"
                      aria-label="Supprimer la pièce jointe"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                )}
                <textarea
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Écrivez votre message..."
                  className="w-full border border-neutral-300 rounded-md p-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 resize-none"
                  rows={3}
                ></textarea>
                <div className="absolute bottom-3 right-3 flex space-x-2">
                  <button
                    type="button"
                    onClick={() => setShowAttachmentOptions(!showAttachmentOptions)}
                    className="text-neutral-500 hover:text-neutral-700"
                    title="Ajouter une pièce jointe"
                    aria-label="Ajouter une pièce jointe"
                  >
                    <Paperclip className="h-5 w-5" />
                  </button>

                  {showAttachmentOptions && (
                    <div className="absolute bottom-full right-0 mb-2 bg-white rounded-md shadow-md border border-neutral-200 p-2 flex space-x-2">
                      <button
                        type="button"
                        onClick={() => handleAttachment('image')}
                        className="flex items-center text-sm text-neutral-700 hover:text-primary-600 p-2 rounded-md hover:bg-neutral-50"
                      >
                        <Image className="h-4 w-4 mr-1" />
                        Image
                      </button>
                      <button
                        type="button"
                        onClick={() => handleAttachment('document')}
                        className="flex items-center text-sm text-neutral-700 hover:text-primary-600 p-2 rounded-md hover:bg-neutral-50"
                      >
                        <File className="h-4 w-4 mr-1" />
                        Document
                      </button>
                    </div>
                  )}

                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    className="hidden"
                    title="Sélectionner un fichier"
                    aria-label="Sélectionner un fichier"
                  />
                </div>
              </div>
              <Button
                type="submit"
                variant="primary"
                disabled={!newMessage.trim() && !attachment}
                style={{ backgroundColor: '#2980b9', color: 'white' }}
              >
                <Send className="h-5 w-5" />
              </Button>
            </form>
          </div>
        </div>

        {/* Informations sur l'interlocuteur */}
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-neutral-200">
            <h3 className="text-lg font-semibold text-neutral-900">À propos</h3>
          </div>
          <div className="p-6">
            <div className="flex flex-col items-center mb-6">
              <Avatar
                size="xl"
                src={conversation.other_user.profile_picture_path}
                fallback={(conversation.other_user.first_name || 'U').charAt(0)}
                className="mb-3"
              />
              <h4 className="text-lg font-semibold text-neutral-900">
                {conversation.other_user.first_name} {conversation.other_user.last_name}
              </h4>
              <Button
                variant="ghost"
                size="sm"
                className="mt-1 text-primary-600 hover:text-primary-700"
                onClick={() => navigate(`/professionals/${conversation.other_user.id}`)}
              >
                Voir le profil complet
              </Button>
            </div>

            <div className="space-y-4">
              <div className="flex items-start">
                <div className="flex-shrink-0 bg-neutral-100 text-neutral-700 rounded-md p-2 mr-3">
                  <Calendar className="h-5 w-5" />
                </div>
                <div>
                  <h5 className="text-sm font-medium text-neutral-700">Membre depuis</h5>
                  <p className="text-neutral-900">Janvier 2023</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0 bg-neutral-100 text-neutral-700 rounded-md p-2 mr-3">
                  <Clock className="h-5 w-5" />
                </div>
                <div>
                  <h5 className="text-sm font-medium text-neutral-700">Temps de réponse</h5>
                  <p className="text-neutral-900">Généralement en quelques heures</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="flex-shrink-0 bg-neutral-100 text-neutral-700 rounded-md p-2 mr-3">
                  <Star className="h-5 w-5" />
                </div>
                <div>
                  <h5 className="text-sm font-medium text-neutral-700">Évaluation</h5>
                  <p className="text-neutral-900">4.8/5 (15 avis)</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

// MessageSquare est maintenant importé de lucide-react

export default ServiceConversation;
