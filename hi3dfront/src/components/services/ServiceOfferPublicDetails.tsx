import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Clock, DollarSign, Eye, ThumbsUp, Tag, Calendar, MessageSquare, Star, User, ChevronRight, ChevronLeft } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import Layout from '../layout/Layout';
import Container from '../layout/Container';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
import type { ServiceOffer } from './types';

interface Review {
  id: number;
  user_id: number;
  user_name: string;
  user_avatar?: string;
  rating: number;
  comment: string;
  created_at: string;
}

const ServiceOfferPublicDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [service, setService] = useState<ServiceOffer | null>(null);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showContactForm, setShowContactForm] = useState(false);
  const [message, setMessage] = useState('');
  const [messageSent, setMessageSent] = useState(false);
  const [similarServices, setSimilarServices] = useState<ServiceOffer[]>([]);
  const [messageSend, setMessageSend] = useState(false);

  // Récupérer les détails du service
  useEffect(() => {
    const fetchServiceDetails = async () => {
      setIsLoading(true);
      try {
        // Essayer d'abord avec l'endpoint public
        let response = await fetch(`${API_BASE_URL}/api/service-offers/${id}/public`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        // Si l'endpoint public échoue, essayer avec l'endpoint standard
        if (!response.ok) {
          console.log('Public endpoint failed, trying standard endpoint');
          response = await fetch(`${API_BASE_URL}/api/service-offers/${id}`, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
            },
          });

          if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
          }
        }

        const data = await response.json();
        console.log('Service details data:', data);

        // Extraire les données du service (peut être dans data ou data.data)
        const serviceData = data.data || data;

        // Log détaillé pour déboguer la structure des données
        console.log('Service data structure:', {
          serviceData,
          hasUser: !!serviceData.user,
          userDetails: serviceData.user ? {
            id: serviceData.user.id,
            firstName: serviceData.user.first_name,
            lastName: serviceData.user.last_name
          } : 'No user data'
        });

        // Transformer les données de l'API
        const formattedService: ServiceOffer = {
          id: serviceData.id,
          title: serviceData.title,
          description: serviceData.description,
          price: serviceData.price,
          price_unit: serviceData.price_unit || 'par projet',
          execution_time: serviceData.execution_time,
          concepts: serviceData.concepts,
          revisions: serviceData.revisions,
          categories: typeof serviceData.categories === 'string' ?
            (() => {
              try {
                return JSON.parse(serviceData.categories || '[]');
              } catch (e) {
                console.error('Erreur de parsing des catégories:', e);
                return [];
              }
            })() : (serviceData.categories || []),
          is_private: serviceData.is_private,
          status: serviceData.status,
          created_at: serviceData.created_at,
          updated_at: serviceData.updated_at,
          user: serviceData.user ? {
            id: serviceData.user.id || (serviceData.user_id ? serviceData.user_id : null),
            first_name: serviceData.user.first_name,
            last_name: serviceData.user.last_name,
            profile_picture_path: serviceData.user.profile_picture_path,
          } : serviceData.user_id ? {
            id: serviceData.user_id,
            first_name: "Professionnel",
            last_name: "",
            profile_picture_path: null,
          } : undefined,
          files: serviceData.files || [],
          imageUrl: serviceData.files && serviceData.files.length > 0
            ? `${API_BASE_URL}/storage/${serviceData.files[0].path}`
            : 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          likes: serviceData.likes || 0,
          views: serviceData.views || 0,
          rating: serviceData.rating || 0,
        };

        setService(formattedService);

        // Récupérer les avis
        if (data.reviews || serviceData.reviews) {
          setReviews(data.reviews || serviceData.reviews || []);
        }

        // Récupérer les services similaires
        if (data.similar_services || serviceData.similar_services) {
          const similarServicesData = data.similar_services || serviceData.similar_services || [];
          setSimilarServices(similarServicesData.map((s: any) => ({
            id: s.id,
            title: s.title,
            description: s.description,
            price: s.price,
            execution_time: s.execution_time,
            concepts: s.concepts,
            revisions: s.revisions,
            categories: typeof s.categories === 'string' ? JSON.parse(s.categories) : (s.categories || []),
            is_private: s.is_private,
            status: s.status,
            created_at: s.created_at,
            updated_at: s.updated_at,
            user: s.user ? {
              id: s.user.id,
              first_name: s.user.first_name,
              last_name: s.user.last_name,
              profile_picture_path: s.user.profile_picture_path,
            } : null,
            files: s.files || [],
            imageUrl: s.files && s.files.length > 0
              ? `${API_BASE_URL}/storage/${s.files[0].path}`
              : 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
            likes: s.likes || 0,
            views: s.views || 0,
            rating: s.rating || 0,
          })));
        }
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les détails du service');
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchServiceDetails();
    }
  }, [id]);

  // Envoyer un message au professionnel
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!message.trim()) {
      return;
    }

    const token = localStorage.getItem('token');

    if (!token) {
      navigate('/login', { state: { from: `/services/${id}` } });
      return;
    }

    try {
      // Vérifier que les données requises sont présentes
      if (!service?.id) {
        setError('Impossible d\'identifier le service');
        return;
      }

      // Si l'utilisateur n'est pas défini, utiliser l'ID du propriétaire du service
      const recipientId = service?.user?.id || service.user_id;

      if (!recipientId) {
        setError('Impossible d\'identifier le professionnel');
        return;
      }

      console.log('Recipient ID:', recipientId);

      const requestData = {
        recipient_id: recipientId,
        content: message,
        service_id: service.id,
      };
      console.log('Sending message data:', requestData);
      setMessageSend(true);

      const response = await fetch(`${API_BASE_URL}/api/messages/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const responseData = await response.json();
      console.log('Message response:', responseData);

      if (!response.ok) {
        setMessageSend(false);
        if (response.status === 422 && responseData.errors) {
          // Erreurs de validation
          const errorMessages = Object.values(responseData.errors).flat().join(', ');
          throw new Error(`Validation échouée: ${errorMessages}`);
        }
        throw new Error('Erreur lors de l\'envoi du message');
      }

      setMessageSend(false);
      setMessageSent(true);
      setMessage('');

      // Fermer le formulaire après 3 secondes
      setTimeout(() => {
        setShowContactForm(false);
        setMessageSent(false);
      }, 3000);
    } catch (err) {
      setMessageSend(false);
      console.error('Erreur:', err);
      setError(err instanceof Error ? err.message : 'Impossible d\'envoyer le message');
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  // Afficher les étoiles pour les avis
  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-500 fill-yellow-500' : 'text-neutral-300'
            }`}
          />
        ))}
      </div>
    );
  };

  // Calculer la note moyenne
  const averageRating = reviews.length > 0
    ? reviews.reduce((acc, review) => acc + review.rating, 0) / reviews.length
    : 0;

  if (isLoading) {
    return (
      <Layout>
        <Container className="py-12">
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
        </Container>
      </Layout>
    );
  }

  if (error || !service) {
    return (
      <Layout>
        <Container className="py-12">
          <Alert
            type="error"
            title="Erreur"
            onClose={() => setError(null)}
          >
            {error || 'Service non trouvé'}
          </Alert>
          <div className="mt-6 text-center">
            <Button
              variant="outline"
              onClick={() => navigate('/services')}
            >
              Retour à la liste des services
            </Button>
          </div>
        </Container>
      </Layout>
    );
  }

  return (
    <Layout>
      <Container className="py-12">
        <div className="mb-6">
          <div className="flex items-center text-sm text-neutral-500 mb-2">
            <Button
              variant="ghost"
              size="sm"
              leftIcon={<ChevronLeft className="h-4 w-4" />}
              onClick={() => navigate('/services')}
              className="hover:bg-transparent hover:text-primary-600 p-0"
            >
              Retour aux services
            </Button>
            <ChevronRight className="h-4 w-4 mx-2" />
            <span>{service.categories[0] || 'Catégorie'}</span>
          </div>
          <h1 className="text-3xl font-bold text-neutral-900">{service.title}</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Colonne principale */}
          <div className="lg:col-span-2 space-y-8">
            {/* Image du service */}
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <img
                src={service.imageUrl}
                alt={service.title}
                className="w-full h-96 object-cover"
              />
            </div>

            {/* Description du service */}
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="p-6">
                <h2 className="text-xl font-semibold text-neutral-900 mb-4">Description</h2>
                <div className="prose prose-neutral max-w-none">
                  <p className="whitespace-pre-line">{service.description}</p>
                </div>

                {/* Catégories */}
                {service.categories && service.categories.length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-lg font-medium text-neutral-900 mb-2">Catégories</h3>
                    <div className="flex flex-wrap gap-2">
                      {service.categories.map((category, index) => (
                        <div key={index} className="flex items-center text-sm bg-neutral-100 text-neutral-700 px-3 py-1 rounded-full">
                          <Tag className="h-4 w-4 mr-2" />
                          <span>{category}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Ce que vous obtenez */}
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="p-6">
                <h2 className="text-xl font-semibold text-neutral-900 mb-4">Ce que vous obtenez</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 bg-primary-100 text-primary-800 rounded-md p-2 mr-3">
                      <Clock className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-neutral-700">Délai de livraison</h3>
                      <p className="text-neutral-900">{service.execution_time}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 bg-green-100 text-green-800 rounded-md p-2 mr-3">
                      <Tag className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-neutral-700">Concepts</h3>
                      <p className="text-neutral-900">{service.concepts}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 bg-blue-100 text-blue-800 rounded-md p-2 mr-3">
                      <ChevronRight className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-neutral-700">Révisions</h3>
                      <p className="text-neutral-900">{service.revisions}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 bg-amber-100 text-amber-800 rounded-md p-2 mr-3">
                      <Calendar className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-neutral-700">Disponible depuis</h3>
                      <p className="text-neutral-900">{formatDate(service.created_at)}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Avis */}
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold text-neutral-900">Avis des clients</h2>
                  <div className="flex items-center">
                    {renderStars(averageRating)}
                    <span className="ml-2 text-neutral-700 font-medium">
                      {averageRating.toFixed(1)} ({reviews.length} avis)
                    </span>
                  </div>
                </div>

                {reviews.length === 0 ? (
                  <div className="text-center py-8">
                    <Star className="h-12 w-12 text-neutral-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-neutral-700 mb-1">Aucun avis pour le moment</h3>
                    <p className="text-neutral-500">Soyez le premier à évaluer ce service après l'avoir utilisé</p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {reviews.map((review) => (
                      <div key={review.id} className="border-b border-neutral-200 pb-6 last:border-b-0 last:pb-0">
                        <div className="flex items-start">
                          <div className="flex-shrink-0 mr-4">
                            {review.user_avatar ? (
                              <img
                                src={review.user_avatar}
                                alt={review.user_name}
                                className="h-10 w-10 rounded-full"
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-neutral-200 flex items-center justify-center">
                                <User className="h-6 w-6 text-neutral-500" />
                              </div>
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="text-sm font-medium text-neutral-900">{review.user_name}</h4>
                                <div className="flex items-center mt-1">
                                  {renderStars(review.rating)}
                                  <span className="ml-2 text-sm text-neutral-500">
                                    {formatDate(review.created_at)}
                                  </span>
                                </div>
                              </div>
                            </div>
                            <div className="mt-2 text-neutral-700">
                              <p>{review.comment}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Carte de prix et contact */}
            <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden sticky top-6">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-2xl font-bold text-neutral-900">{service.price} €</h3>
                  <div className="flex items-center text-sm text-neutral-500">
                    <Eye className="h-4 w-4 mr-1" />
                    <span>{service.views}</span>
                    <ThumbsUp className="h-4 w-4 ml-3 mr-1" />
                    <span>{service.likes}</span>
                  </div>
                </div>

                <div className="space-y-4 mb-6">
                  <Button
                    variant="primary"
                    leftIcon={<MessageSquare className="h-5 w-5" />}
                    onClick={() => {
                      // Vérifier si l'utilisateur est connecté
                      const token = localStorage.getItem('token');
                      if (!token) {
                        navigate('/login', { state: { from: `/services/${id}` } });
                        return;
                      }
                      setShowContactForm(true);
                    }}
                    fullWidth
                    style={{ backgroundColor: '#2980b9', color: 'white' }}
                  >
                    Contacter le professionnel
                  </Button>

                  <Button
                    variant="outline"
                    leftIcon={<Calendar className="h-5 w-5" />}
                    onClick={() => {
                      // Vérifier si l'utilisateur est connecté
                      const token = localStorage.getItem('token');
                      if (!token) {
                        navigate('/login', { state: { from: `/services/${id}` } });
                        return;
                      }
                      // Rediriger vers la page de réservation (à implémenter)
                      navigate(`/services/${id}/book`);
                    }}
                    fullWidth
                  >
                    Réserver ce service
                  </Button>
                </div>

                {/* Formulaire de contact */}
                {showContactForm && (
                  <div className="mt-4 border-t border-neutral-200 pt-4">
                    {messageSent ? (
                      <div className="bg-green-50 border border-green-200 rounded-md p-4 text-green-800">
                        <p className="font-medium">Message envoyé avec succès!</p>
                        <p className="text-sm mt-1">Le professionnel vous répondra bientôt.</p>
                      </div>
                    ) : (
                      <form onSubmit={handleSendMessage}>
                        <h4 className="text-sm font-medium text-neutral-900 mb-2">Envoyer un message</h4>
                        <textarea
                          value={message}
                          onChange={(e) => setMessage(e.target.value)}
                          placeholder="Décrivez votre projet et vos besoins..."
                          className="w-full border border-neutral-300 rounded-md p-3 mb-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                          rows={4}
                          required
                        ></textarea>
                        <div className="flex space-x-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => setShowContactForm(false)}
                          >
                            Annuler
                          </Button>
                          <Button
                            type="submit"
                            variant="primary"
                            style={{ backgroundColor: '#2980b9', color: 'white' }}
                            disabled={messageSend}
                          >
                            {messageSend? 'Envoie encours...':'Envoyer'}
                          </Button>
                        </div>
                      </form>
                    )}
                  </div>
                )}
              </div>

              {/* Informations sur le professionnel */}
              {service.user && (
                <div className="px-6 py-4 bg-neutral-50 border-t border-neutral-200">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 mr-3">
                      {service.user.profile_picture_path ? (
                        <img
                          src={service.user.profile_picture_path}
                          alt={`${service.user.first_name} ${service.user.last_name}`}
                          className="h-10 w-10 rounded-full"
                        />
                      ) : (
                        <div className="h-10 w-10 rounded-full bg-neutral-200 flex items-center justify-center">
                          <User className="h-6 w-6 text-neutral-500" />
                        </div>
                      )}
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-neutral-900">
                        {service.user.first_name} {service.user.last_name}
                      </h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 h-auto text-primary-600 hover:text-primary-700"
                        onClick={() => service.user && navigate(`/professionals/${service.user.id}`)}
                      >
                        Voir le profil
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Services similaires */}
            {similarServices.length > 0 && (
              <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-neutral-200">
                  <h3 className="text-lg font-semibold text-neutral-900">Services similaires</h3>
                </div>
                <div className="p-6 space-y-4">
                  {similarServices.slice(0, 3).map((similarService) => (
                    <div
                      key={similarService.id}
                      className="flex items-start cursor-pointer hover:bg-neutral-50 p-2 rounded-md transition-colors"
                      onClick={() => navigate(`/services/${similarService.id}`)}
                    >
                      <img
                        src={similarService.imageUrl}
                        alt={similarService.title}
                        className="h-16 w-16 object-cover rounded-md mr-3"
                      />
                      <div className="flex-1">
                        <h4 className="text-sm font-medium text-neutral-900 line-clamp-2">{similarService.title}</h4>
                        <div className="flex items-center mt-1">
                          <DollarSign className="h-4 w-4 text-neutral-500" />
                          <span className="text-sm text-neutral-700 font-medium ml-1">{similarService.price} €</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </Container>
    </Layout>
  );
};

export default ServiceOfferPublicDetails;
