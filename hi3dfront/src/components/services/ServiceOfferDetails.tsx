import React from 'react';
import { Clock, DollarSign, Eye, ThumbsUp, Tag, Calendar, Edit, Trash2, Share2, User } from 'lucide-react';
import Button from '../ui/Button';
import type { ServiceOffer } from './types';

interface ServiceOfferDetailsProps {
  service: ServiceOffer;
  onEdit: (service: ServiceOffer) => void;
  onDelete: (id: number) => void;
  onShare: (id: number) => void;
}

const ServiceOfferDetails: React.FC<ServiceOfferDetailsProps> = ({
  service,
  onEdit,
  onDelete,
  onShare,
}) => {
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    });
  };

  const user = JSON.parse(localStorage.getItem('user') || '{}');

  console.log("Utisateur: ",user);
  console.log("Service Zio: ",service);

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'archived':
        return 'bg-neutral-100 text-neutral-800';
      default:
        return 'bg-neutral-100 text-neutral-800';
    }
  };

  // Get status label
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'draft':
        return 'Brouillon';
      case 'published':
        return 'Publié';
      case 'archived':
        return 'Archivé';
      default:
        return 'Inconnu';
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Main content */}
      <div className="lg:col-span-2">
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
          {/* Header with image */}
          <div className="relative h-64">
            <img
              src={service.imageUrl || 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'}
              alt={service.title}
              className="w-full h-full object-cover"
              onError={(e) => {
                console.error("Image failed to load:", service.imageUrl);
                e.currentTarget.src = 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
              }}
            />

            {/* Status badge */}
            <div className="absolute top-4 right-4">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusBadgeColor(service.status)}`}>
                {getStatusLabel(service.status)}
              </span>
            </div>

            {/* Private badge */}
            {service.is_private && (
              <div className="absolute top-4 left-4">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-neutral-800 text-white">
                  Privé
                </span>
              </div>
            )}
          </div>

          <div className="p-6">
            <h1 className="text-2xl font-bold text-neutral-900 mb-4">{service.title}</h1>

            {/* Categories */}
            {service.categories && service.categories.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-6">
                {service.categories.map((category, index) => (
                  <div key={index} className="flex items-center text-sm bg-neutral-100 text-neutral-700 px-3 py-1 rounded-full">
                    <Tag className="h-4 w-4 mr-2" />
                    <span>{category}</span>
                  </div>
                ))}
              </div>
            )}

            {/* Description */}
            <div className="prose prose-neutral max-w-none mb-6">
              <p className="whitespace-pre-line">{service.description}</p>
            </div>

            {/* Key details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              {/* Price */}
              <div className="flex items-center">
                <DollarSign className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Prix</h3>
                  <p className="text-neutral-900">{service.price} €</p>
                </div>
              </div>

              {/* Execution time */}
              <div className="flex items-center">
                <Clock className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Temps d'exécution</h3>
                  <p className="text-neutral-900">{service.execution_time}</p>
                </div>
              </div>

              {/* Concepts */}
              <div className="flex items-center">
                <Tag className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Concepts</h3>
                  <p className="text-neutral-900">{service.concepts}</p>
                </div>
              </div>

              {/* Revisions */}
              <div className="flex items-center">
                <Edit className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Révisions</h3>
                  <p className="text-neutral-900">{service.revisions}</p>
                </div>
              </div>

              {/* Views */}
              <div className="flex items-center">
                <Eye className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">Vues</h3>
                  <p className="text-neutral-900">{service.views || 0}</p>
                </div>
              </div>

              {/* Likes */}
              <div className="flex items-center">
                <ThumbsUp className="h-5 w-5 text-neutral-500 mr-3" />
                <div>
                  <h3 className="text-sm font-medium text-neutral-700">J'aime</h3>
                  <p className="text-neutral-900">{service.likes || 0}</p>
                </div>
              </div>
            </div>

            {/* Author info */}
            {service.user && (
              <div className="bg-neutral-50 rounded-lg p-4 border border-neutral-200 mb-6">
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-neutral-300 flex items-center justify-center overflow-hidden mr-3">
                    {service.user.profile_picture_path ? (
                      <img
                        src={service.user.profile_picture_path}
                        alt={`${service.user.first_name} ${service.user.last_name}`}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <User className="h-6 w-6 text-neutral-500" />
                    )}
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-neutral-700">Créé par</h3>
                    <p className="text-neutral-900">{service.user.first_name} {service.user.last_name}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Metadata */}
            <div className="text-xs text-neutral-500 mt-6 pt-6 border-t border-neutral-200">
              <p>Créé le {formatDate(service.created_at)}</p>
              <p>Dernière mise à jour le {formatDate(service.updated_at)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Sidebar */}
      <div className="lg:col-span-1">
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden sticky top-6">
          <div className="px-6 py-4 border-b border-neutral-200">
            <h3 className="text-lg font-semibold text-neutral-900">Actions</h3>
          </div>

          <div className="p-6 space-y-4">
            {/* Boutons d'édition et de suppression - uniquement pour les professionnels propriétaires du service */}
            {user.is_professional === true &&
             service.user_id && user.id === service.user_id && (
              <>
                <Button
                  variant="outline"
                  leftIcon={<Edit className="h-5 w-5" />}
                  onClick={() => onEdit(service)}
                  fullWidth
                >
                  Modifier le service
                </Button>

                <Button
                  variant="danger"
                  leftIcon={<Trash2 className="h-5 w-5" />}
                  onClick={() => onDelete(service.id)}
                  fullWidth
                >
                  Supprimer le service
                </Button>
              </>
            )}

            <Button
              variant="ghost"
              leftIcon={<Share2 className="h-5 w-5" />}
              onClick={() => onShare(service.id)}
              fullWidth
            >
              Partager le service
            </Button>

            <div className="pt-4 border-t border-neutral-200">
              <h4 className="text-sm font-medium text-neutral-700 mb-2">Statistiques</h4>
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-neutral-50 p-3 rounded-lg">
                  <div className="flex items-center text-sm text-neutral-500 mb-1">
                    <Eye className="h-4 w-4 mr-1" />
                    <span>Vues</span>
                  </div>
                  <p className="text-lg font-semibold text-neutral-900">{service.views || 0}</p>
                </div>
                <div className="bg-neutral-50 p-3 rounded-lg">
                  <div className="flex items-center text-sm text-neutral-500 mb-1">
                    <ThumbsUp className="h-4 w-4 mr-1" />
                    <span>J'aime</span>
                  </div>
                  <p className="text-lg font-semibold text-neutral-900">{service.likes || 0}</p>
                </div>
              </div>
            </div>

            <div className="pt-4 border-t border-neutral-200">
              <h4 className="text-sm font-medium text-neutral-700 mb-2">Dates</h4>
              <div className="space-y-2">
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 text-neutral-500 mr-2" />
                  <span className="text-neutral-700">Créé le {formatDate(service.created_at)}</span>
                </div>
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 text-neutral-500 mr-2" />
                  <span className="text-neutral-700">Mis à jour le {formatDate(service.updated_at)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceOfferDetails;
