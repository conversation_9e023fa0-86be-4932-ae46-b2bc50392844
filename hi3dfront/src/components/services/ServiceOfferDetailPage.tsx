import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import DashboardLayout from '../dashboard/DashboardLayout';
import ServiceOfferDetails from './ServiceOfferDetails';
import Alert from '../ui/Alert';
import type { ServiceOffer } from './types';
import { API_BASE_URL } from '../../config';

interface ServiceOfferDetailPageProps {}

const ServiceOfferDetailPage: React.FC<ServiceOfferDetailPageProps> = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [service, setService] = useState<ServiceOffer | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');

  useEffect(() => {
    const fetchServiceDetails = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log("Récupération du service avec ID:", id);
        const response = await fetch(`${API_BASE_URL}/api/service-offers/${id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          console.error("Erreur lors de la récupération du service:", response.status);
          throw new Error(`Erreur lors de la récupération du service (${response.status})`);
        }

        const data = await response.json();
        console.log("Données du service récupéré:", data);

        // Formater le service
        const formattedService = {
          id: data.id,
          user_id : data.user_id,
          title: data.title,
          description: data.description,
          price: data.price,
          price_unit: data.price_unit || 'par projet',
          execution_time: data.execution_time,
          concepts: data.concepts,
          revisions: data.revisions,
          categories: typeof data.categories === 'string' ? JSON.parse(data.categories) : (data.categories || []),
          is_private: data.is_private !== false,
          status: data.status || 'published',
          created_at: data.created_at,
          updated_at: data.updated_at,
          user: data.user ? {
            id: data.user.id,
            first_name: data.user.first_name,
            last_name: data.user.last_name,
            profile_picture_path: data.user.profile_picture_path,
          } : null,
          files: data.files || [],
          imageUrl: data.files && data.files.length > 0
            ? `${API_BASE_URL}/storage/${data.files[0].path}`
            : 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          likes: data.likes || 0,
          views: data.views || 0,
        };

        console.log("Service formaté:", formattedService);
        console.log("Image URL:", formattedService.imageUrl);
        console.log("Files:", data.files);
        setService(formattedService);
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de charger les détails du service');
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchServiceDetails();
    }
  }, [id, token]);

  // Gérer la modification d'un service
  const handleEditService = () => {
    navigate(`/dashboard/services/edit/${id}`);
  };

  // Gérer la suppression d'un service
  const handleDeleteService = async () => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer ce service ?')) {
      return;
    }

    try {
      const response = await fetch(`${API_BASE_URL}/api/service-offers/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression du service');
      }

      // Rediriger vers la liste des services
      navigate('/dashboard/services');
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de supprimer le service');
    }
  };

  // Gérer le partage d'un service
  const handleShareService = () => {
    const url = `${window.location.origin}/service/${id}`;
    navigator.clipboard.writeText(url);
    alert('Lien copié dans le presse-papier');
  };

  return (
    <DashboardLayout
      title="Détails du service"
      subtitle="Consultez les détails de votre service professionnel"
    >
      {error && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => setError(null)}
          className="mb-6"
        >
          {error}
        </Alert>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : service ? (
        <ServiceOfferDetails
          service={service}
          onEdit={handleEditService}
          onDelete={handleDeleteService}
          onShare={handleShareService}
        />
      ) : (
        <div className="text-center py-12">
          <h3 className="text-lg font-medium text-gray-900">Service non trouvé</h3>
          <p className="mt-2 text-sm text-gray-500">
            Le service que vous recherchez n'existe pas ou a été supprimé.
          </p>
          <button
            type="button"
            onClick={() => navigate('/dashboard/services')}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Retour à la liste des services
          </button>
        </div>
      )}
    </DashboardLayout>
  );
};

export default ServiceOfferDetailPage;
