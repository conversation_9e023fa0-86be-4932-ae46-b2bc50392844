import React, { useState, useRef, useEffect } from 'react';
import { Send } from 'lucide-react';
import { API_BASE_URL } from './../config';
import { useNotifications } from './notifications/NotificationContext';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  email_verified_at: string;
  is_professional: boolean;
  created_at: string;
  updated_at: string;
  profile_completed: boolean;
}

interface FileData {
  id: number;
  original_name: string;
  filename: string;
  mime_type: string;
  size: number;
  human_size: string;
  extension: string;
  storage_type: string;
  status: string;
  download_url: string;
  local_path?: string;
  swisstransfer_url?: string;
  swisstransfer_download_url?: string;
  expires_at: string | null;
  is_expired: boolean;
  created_at: string;
  updated_at: string;
  localUrl?: string;
}

interface Message {
  id: number;
  open_offer_id: number;
  sender_id: number;
  receiver_id: number;
  message_text: string;
  created_at: string;
  updated_at: string;
  sender: User;
  receiver: User;
  files?: FileData[];
}

interface OfferDiscussionPanelProps {
  offerId?: number;
  offerTitle?: string;
  clientId?: number;
  clientName?: string;
  clientAvatar?: string;
  professionalId?: number;
  professionalName?: string;
  professionalAvatar?: string;
  isClient?: boolean;
  onBack?: () => void;
  selectedFiles: File[];
  setSelectedFiles: React.Dispatch<React.SetStateAction<File[]>>;
}

const MessageContent: React.FC<OfferDiscussionPanelProps> = ({
  offerId,
  offerTitle,
  clientId,
  clientName = "Client",
  clientAvatar,
  professionalId,
  professionalName = "Professionnel",
  professionalAvatar,
  isClient,
  selectedFiles,
  setSelectedFiles,
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { addOfferNotification } = useNotifications();
  const [lastMessageId, setLastMessageId] = useState<number | null>(null);
  const [pendingMessages, setPendingMessages] = useState<any[]>([]);

  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const token = localStorage.getItem('token');

  const getImageUrl = (file: FileData): string => {
    if (file.download_url) {
      return file.download_url;
    }
    
    if (file.storage_type === 'local' && file.local_path) {
      return `${API_BASE_URL}/storage/${file.local_path}`;
    }
    
    if (file.storage_type === 'swisstransfer' && file.swisstransfer_download_url) {
      return file.swisstransfer_download_url;
    }
    
    return '';
  };

  const fetchMessages = async (sinceId: number | null = null) => {
    if (!token || !offerId) return;
    setLoading(true);
    try {
      let url = `${API_BASE_URL}/api/open-offers/${offerId}/messages`;
      const params = new URLSearchParams();
      if (!isClient) {
        params.append('professional_id', String(currentUser.id));
      }
      if (sinceId !== null) {
        params.append('since_id', String(sinceId));
      }
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des messages');
      }
      const data = await response.json();
      if (data.messages && data.messages.length > 0) {
        setMessages(data.messages);
        setLastMessageId(data.messages[data.messages.length - 1].id);
        setPendingMessages((prev) => prev.filter((pending) => {
          return !data.messages.some((msg: any) =>
            msg.message_text === pending.message_text &&
            msg.sender_id === pending.sender_id &&
            !pending.error
          );
        }));
      }
    } catch (err) {
      console.error('Erreur:', err);
      setError('Impossible de charger les messages');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMessages();
    const intervalId = setInterval(() => {
      fetchMessages(lastMessageId);
    }, 5000);
    return () => clearInterval(intervalId);
  }, [offerId, token, isClient, clientId, clientName, clientAvatar, professionalId, professionalName, professionalAvatar]);

  useEffect(() => {
    if (isClient && professionalId !== undefined && professionalId !== null) {
      setError(null);
    }
  }, [professionalId, isClient]);

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, pendingMessages]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setSelectedFiles((prev) => [
        ...prev,
        ...Array.from(files).filter(
          (file) => !prev.some((f) => f.name === file.name && f.size === file.size)
        ),
      ]);
      e.target.value = '';
    }
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() && selectedFiles.length === 0) return;
    if (!token || !offerId) return;
    if (isClient && (professionalId === undefined || professionalId === null)) {
      setError("Veuillez sélectionner un professionnel avant d'envoyer un message.");
      return;
    }
    setSending(true);
    setError(null);

    const messageToSend = newMessage;
    const filesToSend = [...selectedFiles];

    const optimisticId = 'pending-' + Date.now();
    const optimisticMessage = {
      id: optimisticId,
      open_offer_id: offerId,
      sender_id: currentUser.id,
      receiver_id: isClient ? professionalId : clientId,
      message_text: messageToSend,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      sender: currentUser,
      receiver: {},
      files: filesToSend.map((file) => ({
        id: 'pending-file-' + file.name + file.size,
        original_name: file.name,
        mime_type: file.type,
        isPending: true,
        localUrl: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
        extension: file.name.split('.').pop(),
      })),
      isPending: true,
      error: null,
    };
    setPendingMessages((prev) => [...prev, optimisticMessage]);
    setNewMessage('');
    setSelectedFiles([]);

    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const body: { message_text: string; receiver_id?: number } = { message_text: messageToSend };
      if (isClient && professionalId !== undefined) {
        body.receiver_id = professionalId;
      }
      const response = await fetch(`${API_BASE_URL}/api/open-offers/${offerId}/messages`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });
      const data = await response.json();
      if (!data.message || !data.message.id) {
        throw new Error('Erreur lors de la création du message');
      }
      const messageId = data.message.id;
      
      if (filesToSend.length > 0) {
        const formData = new FormData();
        filesToSend.forEach((file: File) => formData.append('files[]', file));
        formData.append('message_id', messageId);
        const uploadResponse = await fetch(`${API_BASE_URL}/api/files/upload`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`,
          },
          body: formData,
        });
        const uploadData = await uploadResponse.json();
        if (!uploadData.success) {
          throw new Error('Erreur lors de l\'upload des fichiers: ' + (uploadData.message || ''));
        }
      }
      
      await fetchMessages();
      setPendingMessages((prev) => prev.filter((m) => m.id !== optimisticId));
      addOfferNotification('offer_message', {
        offer_id: offerId,
        offer_title: offerTitle||"",
        message: messageToSend,
        ...(isClient
          ? { client_id: user.id, client_name: clientName, client_avatar: clientAvatar }
          : { professional_id: user.id, professional_name: professionalName, professional_avatar: professionalAvatar })
      });
    } catch (err) {
      setPendingMessages((prev) => prev.map((m) => m.id === optimisticId ? { ...m, error: 'Échec de l\'envoi', isPending: false } : m));
      setError('Envoi échoué.');
    } finally {
      setSending(false);
    }
  };

  let filteredMessages = messages;
  if (isClient && professionalId) {
    filteredMessages = messages.filter(
      (msg) =>
        (msg.sender_id === professionalId && msg.receiver_id === clientId) ||
        (msg.sender_id === clientId && msg.receiver_id === professionalId)
    );
  }

  const allMessages = [...messages, ...pendingMessages];
  const filteredAllMessages = isClient && professionalId
    ? allMessages.filter(
        (msg) =>
          (msg.sender_id === professionalId && msg.receiver_id === clientId) ||
          (msg.sender_id === clientId && msg.receiver_id === professionalId)
      )
    : allMessages;

  if (loading) {
    return (
      <div className="flex-1 pt-8 pr-4 pl-4 mr-8 ml-8 bg-[#F5F5F5]" style={{ maxHeight: '500px', display: 'flex', flexDirection: 'column' }}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 pt-8 pr-4 pl-4 mr-8 ml-8 bg-[#F5F5F5]" style={{ maxHeight: '500px', display: 'flex', flexDirection: 'column' }}>
      <div className="flex-1 overflow-y-auto mb-4 pr-2 custom-scrollbar" style={{ maxHeight: 'calc(500px - 120px)' }}>
        {filteredAllMessages.length === 0 ? (
          <div style={{ color: "#aaa", textAlign: "center", marginTop: 40 }}>
            Aucun message pour ce professionnel.
          </div>
        ) : (
          filteredAllMessages.map((msg) => {
            const isSent = msg.sender_id === currentUser.id;
            return (
              <div
                key={msg.id}
                style={{
                  display: "flex",
                  justifyContent: isSent ? "flex-end" : "flex-start",
                  marginBottom: 12,
                }}
              >
                <div
                  style={{
                    background: isSent ? "#fae4b0" : "#ffffffff",
                    color: "#222",
                    borderRadius: 16,
                    padding: "10px 16px",
                    maxWidth: "70%",
                    textAlign: "left",
                    boxShadow: "0 1px 4px rgba(160,89,207,0.04)",
                    position: "relative",
                  }}
                >
                  <div style={{ fontWeight: "bold", fontSize: 12 }}>
                    <span
                      style={{
                        fontSize: "12px",
                        fontFamily: "'Inter', sans-serif",
                        fontWeight: 300,
                        color: "#0D0C22",
                        float: "right"
                      }}
                    >
                      {new Date(msg.created_at).toLocaleTimeString()}
                    </span>
                    {msg.sender?.first_name || "Utilisateur"}
                  </div>
                  <div
                    style={{
                      fontSize: "14px",
                      fontFamily: "'Inter', sans-serif",
                      fontWeight: 400,
                      color: "#0D0C22",
                      float: "right",
                      marginBottom: 4
                    }}
                  >
                    {msg.message_text}
                  </div>
                  
                  {msg.files && msg.files.length > 0 && (
                    <div style={{ marginTop: 8, display: 'flex', flexDirection: 'column', gap: 8 }}>
                      {msg.files.map((file: FileData) => {
                        const isImage = file.mime_type?.startsWith('image/');
                        const getFileIcon = () => {
                          if (/pdf$/.test(file.original_name)) {
                            return (
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#a259cf"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">PDF</text></svg>
                            );
                          }
                          if (/docx?$/.test(file.original_name)) {
                            return (
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#4a90e2"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">DOC</text></svg>
                            );
                          }
                          if (/xlsx?$/.test(file.original_name)) {
                            return (
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#27ae60"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">XLS</text></svg>
                            );
                          }
                          if (/txt$/.test(file.original_name)) {
                            return (
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#f5a623"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">TXT</text></svg>
                            );
                          }
                          if (/zip$|rar$/.test(file.original_name)) {
                            return (
                              <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#b07bfa"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">ZIP</text></svg>
                            );
                          }
                          return (
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" fill="#f3f0fa"/><path d="M7 7h10v10H7z" fill="#bbb"/><text x="12" y="17" textAnchor="middle" fontSize="8" fill="#fff" fontFamily="Arial" fontWeight="bold">FILE</text></svg>
                          );
                        };

                        if (isImage) {
                          const imageUrl = file.localUrl || getImageUrl(file);
                          return (
                            <div key={file.id} style={{ 
                              marginBottom: 8,
                              borderRadius: 16,
                              overflow: 'hidden',
                              background: '#fff',
                              border: '1px solid #e5d5fa',
                              boxShadow: '0 2px 8px rgba(160,89,207,0.08)'
                            }}>
                              <img
                                src={imageUrl}
                                alt={file.original_name}
                                style={{
                                  width: '100%',
                                  maxHeight: 180,
                                  borderRadius: 12,
                                  border: '1px solid #e5d5fa',
                                  cursor: 'pointer',
                                  objectFit: 'cover'
                                }}
                                onClick={() => window.open(imageUrl, '_blank')}
                                onError={(e) => {
                                  console.error('Erreur de chargement de l\'image:', file.original_name);
                                  e.currentTarget.style.display = 'none';
                                }}
                              />
                              <div style={{ 
                                padding: '8px 12px',
                                background: '#faf5ff',
                                borderTop: '1px solid #e5d5fa'
                              }}>
                                <div style={{ 
                                  fontSize: 12, 
                                  color: '#666',
                                  fontWeight: 500,
                                  textAlign: 'center'
                                }}>
                                  {file.original_name}
                                </div>
                              </div>
                            </div>
                          );
                        }

                        return (
                          <a
                            key={file.id}
                            href={file.download_url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              background: '#fff',
                              border: '1px solid #e5d5fa',
                              borderRadius: 12,
                              padding: '8px 12px',
                              textDecoration: 'none',
                              color: '#222',
                              fontSize: 12,
                              maxWidth: 250,
                              minWidth: 0,
                              marginBottom: 4,
                            }}
                          >
                            <span style={{ marginRight: 10 }}>{getFileIcon()}</span>
                            <span style={{ 
                              whiteSpace: 'nowrap', 
                              overflow: 'hidden', 
                              textOverflow: 'ellipsis',
                              flex: 1
                            }}>
                              {file.original_name}
                            </span>
                          </a>
                        );
                      })}
                    </div>
                  )}
                  
                  <div style={{ fontSize: 11, color: '#aaa', textAlign: 'right' }}>
                    {msg.isPending && (
                      <span style={{ fontSize: 11, color: '#bbb', marginLeft: 8 }}>
                        Envoi en cours...
                      </span>
                    )}
                    {msg.error && (
                      <span style={{ fontSize: 11, color: 'red', marginLeft: 8 }}>
                        {msg.error}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {error && (
        <div style={{ color: 'red', marginBottom: 8, padding: '8px 12px', background: '#fee', borderRadius: 8 }}>
          {error}
        </div>
      )}

      {selectedFiles.length > 0 && (
        <div
          style={{
            marginBottom: 12,
            background: "#f8f4ff",
            borderRadius: 12,
            padding: "10px 12px",
            display: "flex",
            flexWrap: "wrap",
            gap: 10,
            minHeight: 48,
          }}
        >
          {selectedFiles.map((file, idx) => {
            const isImage = file.type.startsWith("image/");

            const getIcon = () => {
              if (/pdf$/.test(file.name)) {
                return (
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                    <rect
                      x="3"
                      y="3"
                      width="18"
                      height="18"
                      rx="2"
                      fill="#f3f0fa"
                    />
                    <path d="M7 7h10v10H7z" fill="#a259cf" />
                    <text
                      x="12"
                      y="17"
                      textAnchor="middle"
                      fontSize="9"
                      fill="#fff"
                      fontFamily="Arial"
                      fontWeight="bold"
                    >
                      PDF
                    </text>
                  </svg>
                );
              }
              if (/docx?$/.test(file.name)) {
                return (
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                    <rect
                      x="3"
                      y="3"
                      width="18"
                      height="18"
                      rx="2"
                      fill="#f3f0fa"
                    />
                    <path d="M7 7h10v10H7z" fill="#4a90e2" />
                    <text
                      x="12"
                      y="17"
                      textAnchor="middle"
                      fontSize="9"
                      fill="#fff"
                      fontFamily="Arial"
                      fontWeight="bold"
                    >
                      DOC
                    </text>
                  </svg>
                );
              }
              if (/xlsx?$/.test(file.name)) {
                return (
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                    <rect
                      x="3"
                      y="3"
                      width="18"
                      height="18"
                      rx="2"
                      fill="#f3f0fa"
                    />
                    <path d="M7 7h10v10H7z" fill="#27ae60" />
                    <text
                      x="12"
                      y="17"
                      textAnchor="middle"
                      fontSize="9"
                      fill="#fff"
                      fontFamily="Arial"
                      fontWeight="bold"
                    >
                      XLS
                    </text>
                  </svg>
                );
              }
              if (/txt$/.test(file.name)) {
                return (
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                    <rect
                      x="3"
                      y="3"
                      width="18"
                      height="18"
                      rx="2"
                      fill="#f3f0fa"
                    />
                    <path d="M7 7h10v10H7z" fill="#f5a623" />
                    <text
                      x="12"
                      y="17"
                      textAnchor="middle"
                      fontSize="9"
                      fill="#fff"
                      fontFamily="Arial"
                      fontWeight="bold"
                    >
                      TXT
                    </text>
                  </svg>
                );
              }
              if (/zip$|rar$/.test(file.name)) {
                return (
                  <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                    <rect
                      x="3"
                      y="3"
                      width="18"
                      height="18"
                      rx="2"
                      fill="#f3f0fa"
                    />
                    <path d="M7 7h10v10H7z" fill="#b07bfa" />
                    <text
                      x="12"
                      y="17"
                      textAnchor="middle"
                      fontSize="9"
                      fill="#fff"
                      fontFamily="Arial"
                      fontWeight="bold"
                    >
                      ZIP
                    </text>
                  </svg>
                );
              }
              return (
                <svg width="28" height="28" viewBox="0 0 24 24" fill="none">
                  <rect
                    x="3"
                    y="3"
                    width="18"
                    height="18"
                    rx="2"
                    fill="#f3f0fa"
                  />
                  <path d="M7 7h10v10H7z" fill="#bbb" />
                  <text
                    x="12"
                    y="17"
                    textAnchor="middle"
                    fontSize="9"
                    fill="#fff"
                    fontFamily="Arial"
                    fontWeight="bold"
                  >
                    FILE
                  </text>
                </svg>
              );
            };

            if (isImage) {
              return (
                <div
                  key={idx}
                  style={{
                    position: "relative",
                    borderRadius: 10,
                    overflow: "hidden",
                    background: "#fff",
                    border: "1px solid #e5d5fa",
                    width: 70,
                    height: 70,
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    boxShadow: "0 1px 4px rgba(160,89,207,0.06)",
                  }}
                >
                  <img
                    src={URL.createObjectURL(file)}
                    alt={file.name}
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                      display: "block",
                    }}
                  />
                  <button
                    type="button"
                    onClick={() => handleRemoveFile(idx)}
                    style={{
                      position: "absolute",
                      top: 2,
                      right: 2,
                      background: "rgba(255,255,255,0.8)",
                      border: "none",
                      color: "#a259cf",
                      cursor: "pointer",
                      fontWeight: "bold",
                      fontSize: 16,
                      lineHeight: 1,
                      borderRadius: "50%",
                      width: 20,
                      height: 20,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      padding: 0,
                    }}
                    aria-label="Supprimer le fichier"
                  >
                    ×
                  </button>
                </div>
              );
            }

            return (
              <div
                key={idx}
                style={{
                  background: "#fff",
                  border: "1px solid #e5d5fa",
                  borderRadius: 10,
                  width: 70,
                  height: 70,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  position: "relative",
                  boxShadow: "0 1px 4px rgba(160,89,207,0.06)",
                }}
              >
                <span style={{ marginRight: 0 }}>{getIcon()}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveFile(idx)}
                  style={{
                    position: "absolute",
                    top: 2,
                    right: 2,
                    background: "rgba(255,255,255,0.8)",
                    border: "none",
                    color: "#a259cf",
                    cursor: "pointer",
                    fontWeight: "bold",
                    fontSize: 16,
                    lineHeight: 1,
                    borderRadius: "50%",
                    width: 20,
                    height: 20,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    padding: 0,
                  }}
                  aria-label="Supprimer le fichier"
                >
                  ×
                </button>
              </div>
            );
          })}
        </div>
      )}

      <div style={{ display: "flex", alignItems: "center", marginTop: 'auto' }}>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            background: "#fff",
            border: "1.5px solid #e5d5fa",
            borderRadius: "20px",
            padding: "8px 18px",
            flex: 1,
            boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.31)",
            height: "72px",
          }}
        >
          <label
            style={{
              display: "flex",
              alignItems: "center",
              cursor: "pointer",
              marginRight: 12,
            }}
          >
            <input
              type="file"
              multiple
              style={{ display: "none" }}
              onChange={handleFileChange}
            />
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#a259cf"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M21.44 11.05l-9.19 9.19a5 5 0 01-7.07-7.07l9.19-9.19a3 3 0 014.24 4.24l-9.2 9.19a1 1 0 01-1.41-1.41l9.2-9.19" />
            </svg>
          </label>
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter") handleSendMessage();
            }}
            placeholder="Write your message..."
            style={{
              flex: 1,
              border: "none",
              outline: "none",
              background: "transparent",
              fontSize: 15,
              height: "100%",
              padding: "0 12px",
            }}
          />
          <button
            onClick={handleSendMessage}
            disabled={
              sending || (!newMessage.trim() && selectedFiles.length === 0)
            }
            style={{
              width: 46,
              height: 46,
              borderRadius: "50%",
              background: "#000000ff",
              border: "none",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
              marginLeft: 8,
            }}
            aria-label="Send message"
          >
            <Send size={18} stroke="#ffffffff" strokeWidth={2.2} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default MessageContent;