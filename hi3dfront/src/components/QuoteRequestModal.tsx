// components/QuoteRequestPopup.tsx
"use client";
import { useState,useEffect  } from "react";
import { Plus } from "lucide-react";
import { API_BASE_URL } from "../config";
import { useNavigate } from "react-router-dom";

// ⬇️ Ton Modal custom (avec onMouseDown sur le backdrop)
import Modal from "./Modal";
// ⬇️ Tu gardes exactement le même composant interne que tu utilises déjà
// import OpenOfferModal from "./ProjectRequestModal";
import OpenOfferModal from "./OpenOfferModal";

interface QuoteRequestModalProps {
  token: string | undefined | null;
  user: any;
  pro: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function QuoteRequestPopup({
  token,
  user,
  pro,
  open,
  onOpenChange,
}: QuoteRequestModalProps) {
  const navigate = useNavigate();

  const [offers, setOffers] = useState<any[]>([]);
  const [selectedOfferId, setSelectedOfferId] = useState<number | null>(null);
  const [showSelectOffer, setShowSelectOffer] = useState(false);
  const [loadingInvite, setLoadingInvitation] = useState<boolean>(false);

  const [openCreateOffer, setOpenCreateOffer] = useState(false);

  const getImageUrl = (
    imagePath: string | undefined,
    defaultImage: string = "https://randomuser.me/api/portraits/men/32.jpg"
  ) => {
    if (!imagePath) return defaultImage;
    if (imagePath.startsWith("http")) return imagePath;
    if (imagePath.startsWith("/")) return `${API_BASE_URL}${imagePath}`;
    return `${API_BASE_URL}/${imagePath}`;
  };

  const recupOffer = async() =>{
    if (!token) return;
    const ctrl = new AbortController();

    try {
      const res = await fetch(`${API_BASE_URL}/api/client/open-offers`, {
        method: "GET",
        headers: { Authorization: `Bearer ${token}` },
        signal: ctrl.signal,
      });

      if (res.status === 401) {
        // Non authentifié → on peut rediriger vers login
        navigate("/login");
        return;
      }

      const data = await res.json().catch(() => ({}));
      const list = Array.isArray(data?.client_open_offers)
        ? (data.client_open_offers)
        : [];

      console.log("Liste de offres : ",list)
      // Dédupe si besoin (au cas où tu ajoutes à la main juste après création)
      const uniqueById = new Map<number, any>();
      for (const o of list) uniqueById.set(o.id, o);
      setOffers(Array.from(uniqueById.values()));
    } catch (err: any) {
      if (err?.name !== "AbortError") {
        console.error("fetch client open-offers error:", err);
      }
    }
  }

  useEffect(() => {
    recupOffer();
  },[token]);

  
  // Envoi d'invitation (utilise selectedOfferId si aucun id passé)
  const sendInvitation = async (idOffer?: number | null) => {
    const finalId = idOffer ?? selectedOfferId;
    if (!finalId) return alert("Sélectionne une offre.");

    if (!token) return alert("Vous devez vous connecter pour inviter un pro.");
    setLoadingInvitation(true);
    try {
      const res = await fetch(`${API_BASE_URL}/api/open-offers/${finalId}/invite`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ professional_id: pro?.user_id }),
      });

      const result = await res.json();
      if (res.ok) {
        alert("Invitation envoyée avec succès !");
        setShowSelectOffer(false);
      } else {
        alert("Erreur : " + (result?.message ?? "invitation échouée"));
      }
    } catch (err) {
      console.error(err);
      alert("Une erreur est survenue.");
    } finally {
      setLoadingInvitation(false);
    }
  };

  // Créer une offre ouverte
  const handleCreateOpenOffer = () => {
    // setOpenCreateOffer(true);
    if (token) {
      if (user && !user.is_professional) {
        setOpenCreateOffer(true); // 👉 ouvre la popup interne de création
      } else {
        alert(
          "Accès restreint : cette fonctionnalité est uniquement disponible pour les clients. Veuillez utiliser un compte client pour continuer."
        );
      }
    } else {
      navigate("/login");
    }
  };

  const handleInvitePro = () => {
    if (token) {
      if (user && !user.is_professional) {
        setShowSelectOffer(true);
      } else {
        alert(
          "Accès restreint : cette fonctionnalité est uniquement disponible pour les clients. Veuillez utiliser un compte client pour continuer."
        );
      }
    } else {
      navigate("/login");
    }
  };

  return (
    <>
      {/* ✅ Popup principal: remplace le Dialog par ton Modal */}
      <Modal
        open={open}
        onClose={() => onOpenChange(false)}
        title={"Let's make a deal"}            // on gère un header custom ci-dessous
        maxWidthClass="max-w-2xl"
      >
        <div className="bg-white">
          {/* Header custom avec image */}
          <div className="mb-1 text-center">
            <div className="w-full md:h-80 overflow-hidden rounded-b-2xl">
              <img
                src={getImageUrl?.(pro?.cover_photo, "/img/popup.png")}
                alt={`${pro?.first_name ?? ""} ${pro?.last_name ?? ""}`.trim()}
                className="w-full h-full object-cover block"
                onError={(e) => {
                  (e.currentTarget as HTMLImageElement).onerror = null;
                  (e.currentTarget as HTMLImageElement).src = "/img/popup.png";
                }}
              />
            </div>
            <h2
              className="text-3xl md:text-4xl tracking-tight text-center mt-4"
              style={{
                fontSize: "28px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 400,
                color: "#040404ff",
              }}
            >
              Request a quote from
            </h2>
          </div>

          <h3
            className="text-xl md:text-2xl text-center"
            style={{
              fontSize: "20px",
              fontFamily: "'Inter', sans-serif",
              fontWeight: 500,
              color: "#040404ff",
            }}
          >
            {`${pro?.first_name ?? "Jack"} ${pro?.last_name ?? "and Moris Render"}`}
          </h3>

          <p
            className="max-w-xl mx-auto leading-relaxed mb-1 text-center"
            style={{
              fontSize: "16px",
              fontFamily: "'Inter', sans-serif",
              fontWeight: 400,
              color: "#040404ff",
            }}
          >
            Main advantage of using the application is: it helps you save time by
            centralizing all your project information in one place.
          </p>

          {/* CTA buttons */}
          <div className="space-y-4 max-w-xl mx-auto mt-4">
            <button
              type="button"
              onClick={handleCreateOpenOffer}
              className="w-full h-10 rounded-full bg-black text-white font-medium hover:bg-gray-900 transition"
              style={{
                fontSize: "15px",
                fontFamily: "'Inter', sans-serif",
                fontWeight: 300,
              }}
            >
              Create a new quote request
            </button>

            {/* Liste d'offres existantes */}
            {offers?.map((offer: any) => (
              <button
                key={offer.id}
                disabled={loadingInvite}
                type="button"
                onClick={() => sendInvitation(offer.id)}
                className="w-full h-10 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium transition flex items-center justify-center gap-3"
                style={{
                  fontSize: "15px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                }}
              >
                <Plus className="w-5 h-5" />{" "}
                {loadingInvite ? "Envoi..." : `Invite to an open offer : ${offer.title}`}
              </button>
            ))}

            {/* (Optionnel) bouton pour choisir une offre existante dans une seconde popup */}
            {!!offers?.length && (
              <button
                type="button"
                onClick={handleInvitePro}
                className="w-full h-10 rounded-full bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium transition"
                style={{
                  fontSize: "15px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 300,
                }}
              >
                Select an existing offer…
              </button>
            )}
          </div>
        </div>
      </Modal>

      {/* ✅ Popup 2 : Sélection d’une offre existante (utilise aussi ton Modal) */}
      <Modal
        open={showSelectOffer}
        onClose={() => setShowSelectOffer(false)}
        title="Sélectionner une offre"
        maxWidthClass="max-w-md"
      >
        <div className="space-y-4">
          <div className="space-y-2 max-h-60 overflow-y-auto pr-1">
            {offers?.map((offer: any) => (
              <label key={offer.id} className="flex items-center gap-2 text-sm">
                <input
                  type="radio"
                  value={offer.id}
                  checked={selectedOfferId === offer.id}
                  onChange={() => setSelectedOfferId(offer.id)}
                />
                <span>{offer.title}</span>
              </label>
            ))}
            {!offers?.length && <div className="text-sm text-gray-500">Aucune offre disponible.</div>}
          </div>

          <button
            onClick={() => sendInvitation(null)}
            className="w-full h-10 rounded-md bg-green-600 text-white hover:bg-green-700 transition"
            disabled={loadingInvite}
          >
            {loadingInvite ? "Envoi..." : "Envoyer l'invitation"}
          </button>
        </div>
      </Modal>

      {/* ✅ Popup 3 : Création d’une offre (tu gardes TON composant existant) */}
      {/* <OpenOfferModal
        open={openCreateOffer}
        onClose={() => setOpenCreateOffer(false)}
        // Si ton composant accepte onSubmit/onSaved, branche ici :
        onSaved={(values: any) => {
          // ➜ si tu veux persister directement ici, fais l'appel API
          // et ensuite:
          // setOffers((prev) => [{ id: newId, title: values.name, ... }, ...prev]);
          console.log("created offer:", values);
        }}
      /> */}

      <OpenOfferModal
        open={openCreateOffer}
        onClose={() => setOpenCreateOffer(false)}
        token={token}               // <-- pour l’Authorization (sinon localStorage)
        user={user}                 // <-- pour contrôle de propriété en édition
        inviteProfessionalId={pro?.user_id} // <-- auto-invite après création (optionnel)
        initialData={{
          // pour éditer : id, user_id, et champs existants
          // id: existing.id, user_id: existing.user_id, title: existing.title, ...
        }}
        onSaved={(saved) => {
          console.log("saved", saved);
          // ex : rafraîchir la liste, fermer, naviguer, etc.
        }}
        // portal={false}              // si tu l’ouvres au-dessus d’une autre popup custom
      />

    </>
  );
}
