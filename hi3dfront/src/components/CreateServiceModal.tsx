import React, { useState, useEffect } from 'react';
import { API_BASE_URL } from '../config';

interface Service {
  id: number;
  title: string;
  author: string;
  imageUrl: string;
  description: string;
  executionTime: string;
  concepts: string;
  revisions: string;
  categories: string[];
  isPrivate: boolean;
  likes: number;
  views: number;
  price: number;
}

interface CreateServiceModalProps {
  onClose: () => void;
  onAddService: (service: Service) => void;
  existingService?: Service | null;
}

const CreateServiceModal: React.FC<CreateServiceModalProps> = ({ onClose, onAddService, existingService }) => {
  const [serviceName, setServiceName] = useState('');
  const [serviceDescription, setServiceDescription] = useState('');
  const [executionTime, setExecutionTime] = useState('');
  const [concepts, setConcepts] = useState('');
  const [revisions, setRevisions] = useState('');
  const [price, setPrice] = useState('');
  const [image, setImage] = useState<File | null>(null);
  const [author, setAuthor] = useState('');
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState(1);

  const categories = [
    'Modélisation 3D',
    'Animation 3D',
    'Rendu 3D',
    'Texturing et Shading',
    'Effets visuels (VFX)',
    'Conception de personnages 3D',
    'Environnements 3D',
    'Réalité virtuelle (VR)',
    'Réalité augmentée (AR)',
    'Simulations physiques',
    'Rigging 3D',
    'Lighting 3D',
    'Compositing 3D',
    'Design de produits 3D',
    'Architecture 3D',
    'Jeux vidéo 3D',
    'Cinéma 4D',
    'Blender',
    'ZBrush',
    'Substance Painter'
  ];

  useEffect(() => {
    if (existingService) {
      setServiceName(existingService.title);
      setServiceDescription(existingService.description);
      setExecutionTime(existingService.executionTime);
      setConcepts(existingService.concepts);
      setRevisions(existingService.revisions);
      setImage(null);
      setAuthor(existingService.author);
      setSelectedCategories(existingService.categories);
      setPrice(existingService.price.toString());
    }
  }, [existingService]);

  // const handleSubmit = (e: React.FormEvent) => {
  //   e.preventDefault();
  //   const newService: Service = {
  //     id: existingService ? existingService.id : Date.now(),
  //     title: serviceName,
  //     author,
  //     imageUrl: image ? URL.createObjectURL(image) : '',
  //     description: serviceDescription,
  //     executionTime,
  //     concepts,
  //     revisions,
  //     categories: selectedCategories,
  //     isPrivate: false,
  //     likes: 0,
  //     views: 0,
  //   };
  //   onAddService(newService);
  //   onClose();
  // };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const token = localStorage.getItem('token');
    if (!token) {
      console.error('Token manquant');
      return;
    }

    // const formData = new FormData();
    // formData.append('title', serviceName);
    // formData.append('description', serviceDescription);
    // formData.append('categories', JSON.stringify(selectedCategories));
    // formData.append('execution_time', executionTime);
    // formData.append('concepts', concepts);
    // formData.append('revisions', revisions);
    // formData.append('is_private', 'false');
    // formData.append('status', 'published');
    // if (image) formData.append('image', image);
    const serviceData = {
      title: serviceName,
      description: serviceDescription,
      categories: selectedCategories,
      execution_time: executionTime,
      concepts,
      revisions,
      is_private: false,
      status: 'published',
      price,
    };

    try {
      const response = await fetch(
        existingService
          ? `${API_BASE_URL}/api/service-offers/${existingService.id}`
          : `${API_BASE_URL}/api/service-offers`,
        {
          method: existingService ? 'PUT' : 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          // body: formData,
          body: JSON.stringify(serviceData),
        }
      );

      console.log('Donnée envoyé', JSON.stringify(serviceData));
      if (response.ok) {
        const data = await response.json();
        console.log('Donnée Reçu', JSON.stringify(data));
        const newService: Service = {
          id: data.id,
          title: data.title,
          author: 'Auteur par défaut', // Vous pouvez utiliser data.service_offer.user.name si disponible data.service_offer.imageUrl || 
          imageUrl: 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
          description: data.description,
          executionTime: data.execution_time,
          concepts: data.concepts,
          revisions: data.revisions,
          categories: data.categories,//JSON.parse(data.categories),
          isPrivate: data.is_private,
          likes: 0,
          views: 0,
          price: data.price,
        };
        onAddService(newService);
        onClose();
        console.log('Service enregistré avec succès', data.message);
      } else {
        const errorData = await response.json();

        console.error("Erreur du serveur :", errorData);
        if (errorData.errors) {
            // Transformer les erreurs en une seule chaîne de texte
            const errorMessage = Object.entries(errorData.errors)
                .map(([key, messages]) =>(messages as string[]).join("\n"))
                .join("\n\n");

            alert(errorMessage); // Afficher toutes les erreurs dans une seule alerte
        } else {
            alert(errorData.message|| "Une erreur est survenu lors de l'enregistrement, verifier que tous les champs soit remplit.Merci!")
        }
        // console.error('Erreur lors de la création ou de la modification du service');
      }
    } catch (error) {
      console.error('Erreur lors de la requête API', error);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setImage(e.target.files[0]);
    }
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategories(prev =>
      prev.includes(category) ? prev.filter(c => c !== category) : [...prev, category]
    );
  };

    const nextStep = () => {
    setCurrentStep(prev => prev + 1);
  };

  const prevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg w-full max-w-2xl shadow-lg relative">
        <button className="absolute top-2 right-2 text-gray-600" onClick={onClose}>
          ✖
        </button>
        <h2 className="text-2xl font-bold mb-4">{existingService ? 'Modifier le service' : 'Nouveau service'}</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
            {currentStep === 1 && (
            <>
          <div>
            <label className="block mb-1 font-semibold">Image du service</label>
            <input
              type="file"
              onChange={handleImageChange}
              className="w-full border px-4 py-2 rounded-lg"
              accept="image/*"
              required
            />
            {image && <img src={URL.createObjectURL(image)} alt="Aperçu" className="mt-2 w-32 h-32 object-cover rounded-md" />}
          </div>
          <div>
            <label className="block mb-1 font-semibold">Nom du service</label>
            <input
              type="text"
              value={serviceName}
              onChange={(e) => setServiceName(e.target.value)}
              className="w-full border px-4 py-2 rounded-lg"
              placeholder="Entrez le nom du service"
              required
            />
          </div>
          <div>
            <label className="block mb-1 font-semibold">Description</label>
            <textarea
              value={serviceDescription}
              onChange={(e) => setServiceDescription(e.target.value)}
              className="w-full border px-4 py-2 rounded-lg"
              placeholder="Décrivez votre service"
              rows={4}
              required
            ></textarea>
          </div>
          <div>
            <label className="block mb-1 font-semibold">Heure d'exécution</label>
            <select
              value={executionTime}
              onChange={(e) => setExecutionTime(e.target.value)}
              className="w-full border px-4 py-2 rounded-lg"
              required
            >
              <option value="">Sélectionnez une option</option>
              <option value="Moins d'une semaine">Moins d'une semaine</option>
              <option value="D'ici 1 à 2 semaines">D'ici 1 à 2 semaines</option>
              <option value="Dans le mois">Dans le mois</option>
              <option value="Dans les 2 mois">Dans les 2 mois</option>
              <option value="Dans les 3 mois">Dans les 3 mois</option>
            </select>
          </div>

          <div >
          <label className="block mb-1 font-semibold">Prix</label>
            <input
              type="text"
              placeholder="Prix"
              value={price} // Si `experience` peut être nul, on utilise une chaîne vide
              onChange={(e) => setPrice(e.target.value)} // Convertir en nombre
              className="w-full border px-4 py-2 rounded-lg"
              min="0"
              step="1"
            />
          </div>
          </>
        )}
        {currentStep === 2 && (
                    <>
          <div className="flex gap-4">
            <div className="w-1/2">
              <label className="block mb-1 font-semibold">Nombre de concepts</label>
              <select
                value={concepts}
                onChange={(e) => setConcepts(e.target.value)}
                className="w-full border px-4 py-2 rounded-lg"
                required
              >
                <option value="">Sélectionnez une option</option>
                {Array.from({ length: 10 }, (_, i) => i + 1).map((num) => (
                  <option key={num} value={`${num} concept${num > 1 ? 's' : ''}`}>
                    {num} concept{num > 1 ? 's' : ''}
                  </option>
                ))}
              </select>
            </div>
            <div className="w-1/2">
              <label className="block mb-1 font-semibold">Nombre de révisions</label>
              <select
                value={revisions}
                onChange={(e) => setRevisions(e.target.value)}
                className="w-full border px-4 py-2 rounded-lg"
                required
              >
                <option value="">Sélectionnez une option</option>
                {Array.from({ length: 10 }, (_, i) => i + 1).map((num) => (
                  <option key={num} value={`${num} révision${num > 1 ? 's' : ''}`}>
                    {num} révision{num > 1 ? 's' : ''}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <label className="block font-medium">Catégorie</label>
            <div className="grid grid-cols-2 gap-4">
                {categories.map(category => (
                    <div key={category} className="flex items-center">
                        <input
                            type="checkbox"
                            id={category}
                            checked={selectedCategories.includes(category)}
                            onChange={() => handleCategoryChange(category)}
                            className="mr-2"
                        />
                        <label htmlFor={category}>{category}</label>
                    </div>
                ))}
            </div>
          </div>
          </>
        )}
        <div className="flex justify-between">
            {currentStep > 1 && (
                <button
                    className="bg-gray-500 text-white py-2 px-4 rounded-lg"
                    onClick={prevStep}
                >
                    Précédent
                </button>
            )}
            {currentStep < 2 && (
                <button
                    className="bg-blue-600 text-white py-2 px-4 rounded-lg"
                    onClick={nextStep}
                >
                    Suivant
                </button>
            )}
            {currentStep === 2 && (
                <button
                    type="submit"
                    className="bg-blue-600 text-white py-2 px-4 rounded-lg"
                    onClick={() => console.log('Service Créer')}
                >
                    {existingService ? 'Mettre à jour le service' : 'Créer un service'}
                </button>
            )}
        </div>
          
        </form>
      </div>
    </div>
  );
};

export default CreateServiceModal;

