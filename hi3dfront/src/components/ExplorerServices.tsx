import { useState, useEffect } from "react";
import { API_BASE_URL } from '../config';
import { useNavigate } from 'react-router-dom';

// interface Achievement {
//   id: number;
//   freelance_profile_id: number;
//   title: string;
//   organization: string;
//   date_obtained: string;
//   description: string;
//   file_path: string | null;
//   achievement_url: string | null;
//   created_at: string;
//   updated_at: string;
// }

interface ExplorerServicesProps {
  professionalId: number;
}

const ExplorerServices = ({ professionalId }: ExplorerServicesProps) => {
  const [services, setService] = useState<any | []>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const getUrlProlfil = (path : string)  => {
        return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
  };

  useEffect(() => {
    const fetchAchievements = async () => {
      try {
        setLoading(true);
        // const response = await fetch(`${API_BASE_URL}/api/professionals/${professionalId}/service-offers`);
        
        // if (!response.ok) {
        //   throw new Error('Erreur lors de la récupération des réalisations');
        // }
        
        // const data = await response.json();

        // console.log("data : ",data)
        
        // if (data.success && data) {
        //   setService(data);
        // } else {
        //   setService([]);
        // }

         const response = await fetch(`${API_BASE_URL}/api/professionals/${professionalId}/service-offers`);

    if (!response.ok) {
      throw new Error('Erreur lors de la récupération des réalisations');
    }

    const data = await response.json();

    console.log("data :", data);

    if (Array.isArray(data)) {
      const formatted = data.map((item) => ({
        id: item.id,
        title: item.title,
        description: item.description || 'Projet réalisé avec passion et expertise technique.',
        // image_url: item.files[0].path 
        //   ? `${API_BASE_URL}/storage/${item.files[0].path}`
        //   : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
        
        image_url : Array.isArray(item.files) && item.files.length > 0
          ? `${API_BASE_URL}/storage/${item.files[0].path}`
          : 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',     
        file_urls: Array.isArray(item.files)
          ? item.files.map((file: any) => `${API_BASE_URL}/storage/${file.path}`)
          : [],
        category: item.categories ? item.categories.join(" - ") : "",
        client_name: item.execution_time,
        professional_name: item.user.first_name+' '+item.user.last_name, // Si tu n’as pas cette info dans l’API
        professional_id: item.user.id || 1,
        date_create : item.created_at,
        price : item.price,
        user_id : item.user.id,
        avatar: item.user.professional_details.avatar? getUrlProlfil(String(item.user.professional_details.avatar)) :'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
      }));

      console.log("Data Formater :", formatted);
      setService(formatted);
    } else {
      setService([]);
    }
        
        setLoading(false);
      } catch (error) {
        console.error("Erreur lors de la récupération des réalisations:", error);
        setError("Impossible de charger les réalisations. Veuillez réessayer plus tard.");
        setLoading(false);
      }
    };

    if (professionalId) {
      fetchAchievements();
    }
  }, [professionalId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-10">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative my-4">
        <strong className="font-bold">Erreur!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  if (services.length === 0) {
    return (
      <div className="text-center py-10 text-gray-500">
        <p>Aucune Service n'a été publiée par ce professionnel.</p>
      </div>
    );
  }

  return (
    <div className="py-6">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">Services</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {services.map((service:any) => (
          <div
                key={service.id}
                className="bg-white rounded-lg overflow-hidden shadow-sm border border-neutral-200 transition-shadow hover:shadow-md cursor-pointer"
                // onClick={() => navigate(`/details-search`
                // )}
                onClick={() => {
                  navigate('/details-search', {
                    state: { service }
                  });
                }}
              >
              <div className="aspect-video overflow-hidden">
                <img
                  src={service.image_url|| "https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"}
                  alt={service.title}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.currentTarget.onerror = null; // empêche les boucles infinies
                    e.currentTarget.src = 'https://images.unsplash.com/photo-1626544827763-d516dce335e2?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'; // chemin de l'image par défaut
                  }}
                />
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-semibold">{service.title}</h3>
                    <p className="text-neutral-600 text-sm">{service.category}</p>
                  </div>
                </div>

                <p className="mt-2 text-sm text-neutral-600 line-clamp-2">{service.description}</p>

                <div className="mt-3 flex items-center justify-between">
                  <div className="text-sm">
                    <span className="text-neutral-500">Par </span>
                    <span
                      className="font-medium text-primary-600 hover:underline"
                    >
                      {service.professional_name}
                    </span>
                  </div>
                  <span className="text-sm text-neutral-500">€{service.price}</span>
                </div>
              </div>
            </div>
        ))}
      </div>
    </div>
  );
};

export default ExplorerServices;
