// components/Modal.tsx
"use client";
import React, { useEffect, useRef } from "react";
import { createPortal } from "react-dom";

export type ModalProps = {
  open: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  maxWidthClass?: string;
  portal?: boolean;            // <-- NEW (default: true)
  container?: Element | null;  // <-- NEW (optional portal target)
};

export default function Modal({
  open,
  onClose,
  title,
  children,
  maxWidthClass = "max-w-3xl",
  portal = true,
  container,
}: ModalProps) {
  const dialogRef = useRef<HTMLDivElement>(null);
  const lastFocused = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (!open) return;
    const onKey = (e: KeyboardEvent) => e.key === "Escape" && onClose();
    document.addEventListener("keydown", onKey);
    return () => document.removeEventListener("keydown", onKey);
  }, [open, onClose]);

  useEffect(() => {
    if (open) {
      lastFocused.current = document.activeElement as HTMLElement;
      const focusable = dialogRef.current?.querySelector<HTMLElement>(
        'button, [href], input, textarea, select, [tabindex]:not([tabindex="-1"])'
      );
      focusable?.focus();
    } else {
      lastFocused.current?.focus?.();
    }
  }, [open]);

  if (!open) return null;

  const content = (
    <div
      className="fixed inset-0 z-[100]"
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
    >
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-[1px] z-10"
        onMouseDown={onClose}
      />

      {/* Dialog */}
      <div className="fixed inset-0 flex items-start md:items-center justify-center p-4 md:p-6 z-20">
        <div
          ref={dialogRef}
          tabIndex={-1}
          className={`w-full ${maxWidthClass} rounded-2xl bg-white shadow-xl outline-none animate-[modalIn_.12s_ease-out] pointer-events-auto`}
          onMouseDown={(e) => e.stopPropagation()}
        >
          {(title) && (
            <div className="flex items-center justify-between px-10 pt-6">
              <h2 id="modal-title" className="md:text-3xl"
                style={{
                  fontSize: "28px",
                  fontFamily: "'Inter', sans-serif",
                  fontWeight: 400,
               
                }}
              >
                {title}
              </h2>
              <button
                type="button"
                onClick={onClose}
                aria-label="Close modal"
                className="grid place-items-center rounded-[10px] bg-[#0000007A] p-3 text-white shadow-[-10px_10px_30px_0px_rgba(0,0,0,0.15)] focus:outline-none hover:bg-[#00000099] transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="10"
                  height="10"
                  viewBox="0 0 26.8701 26.8701"
                  className="fill-white"
                >
                  <title>Close</title>
                  <polygon points="26.87 2.121 24.749 0 13.435 11.314 2.121 0 0 2.121 11.314 13.435 0 24.749 2.121 26.87 13.435 15.556 24.749 26.87 26.87 24.749 15.556 13.435 26.87 2.121" />
                </svg>
              </button>


            </div>
          )}
          <div className="px-10 pb-6">{children}</div>
        </div>
      </div>
    </div>
  );

  // ⬇️ Ici on choisit portal ou inline
  return portal ? createPortal(content, container ?? document.body) : content;
}
