"use client";
import React, { useEffect, useMemo, useState } from "react";
import Modal from "./Modal";
// -> adapte ce chemin si besoin :
import { API_BASE_URL } from "./../config";

type BudgetKey =
  | "$ 5-100"
  | "$ 100-250"
  | "$ 250-500"
  | "$ 500-1000"
  | "$ 1'000-2'500"
  | "$ 2'500-5'000"
  | "$ 5'000-10'000"
  | "$ 10'000-25'000"
  | "$ 25'000 +"
  | "OTHER";

const BUDGETS: Exclude<BudgetKey, "OTHER">[] = [
  "$ 5-100",
  "$ 100-250",
  "$ 250-500",
  "$ 500-1000",
  "$ 1'000-2'500",
  "$ 2'500-5'000",
  "$ 5'000-10'000",
  "$ 10'000-25'000",
  "$ 25'000 +",
];

export type OpenOfferMinimal = {
  id?: number;
  title: string;
  budget: string;        // garde une string (ex: "500€ - 1000€" ou "$ 500-1000")
  deadline: string;      // yyyy-mm-dd
  description: string;
};

export default function OpenOfferModal({
  open,
  onClose,
  initialData,
  onSaved,
  title = "Discribe your project",
  createUrl = `${API_BASE_URL}/api/offers`,
  updateUrl = (id: number) => `${API_BASE_URL}/api/offers/${id}`,
  portal = true,
}: {
  open: boolean;
  onClose: () => void;
  initialData?: Partial<OpenOfferMinimal>;
  onSaved?: (payload: OpenOfferMinimal) => void;
  title?: string;
  /** Permet d’ajuster l’endpoint sans toucher le composant */
  createUrl?: string;
  updateUrl?: (id: number) => string;
  portal?: boolean;
}) {
  const isEdit = !!initialData?.id;

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Champs
  const [offerTitle, setOfferTitle] = useState(initialData?.title ?? "");
  const [deadline, setDeadline] = useState<string>(() => {
    // normalise en yyyy-mm-dd si initialData.deadline existe
    if (!initialData?.deadline) return "";
    const d = new Date(initialData.deadline);
    if (isNaN(d.getTime())) return "";
    return d.toISOString().split("T")[0];
  });
  const [description, setDescription] = useState(initialData?.description ?? "");

  // Budget : soit un radio pré-défini, soit "OTHER" + champ libre
  const deducedPreset = useMemo<BudgetKey>(() => {
    const raw = (initialData?.budget ?? "").trim();
    if (!raw) return "$ 5-100";
    const match = BUDGETS.find((b) => b === raw);
    return (match as BudgetKey) || "OTHER";
  }, [initialData?.budget]);

  const [budgetPreset, setBudgetPreset] = useState<BudgetKey>(deducedPreset);
  const [budgetCustom, setBudgetCustom] = useState<string>(() =>
    deducedPreset === "OTHER" ? (initialData?.budget ?? "") : ""
  );

  const budgetValue = budgetPreset === "OTHER" ? budgetCustom : budgetPreset;

  useEffect(() => {
    if (!open) return;
    setError(null);
    setSuccess(null);
  }, [open]);

  // Validation
  const validate = () => {
    if (!offerTitle.trim()) return "Le titre de l’offre est requis";
    if (!budgetValue.trim()) return "Le budget est requis";
    if (!description.trim()) return "La description est requise";
    if (!deadline) return "La date limite est requise";
    const d = new Date(deadline);
    const today = new Date();
    // enlève l'heure pour comparer à J
    d.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);
    if (d < today) return "La date limite doit être dans le futur";
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const err = validate();
    if (err) {
      setError(err);
      return;
    }
    setSaving(true);
    setError(null);
    setSuccess(null);
    try {
      const token = localStorage.getItem("token");
      if (!token) throw new Error("Unauthenticated");

      const payload: OpenOfferMinimal = {
        id: initialData?.id,
        title: offerTitle.trim(),
        budget: budgetValue.trim(),
        deadline,
        description: description.trim(),
      };

      const url = isEdit ? updateUrl(initialData!.id!) : createUrl;
      const method = isEdit ? "PUT" : "POST";

      const res = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const j = await res.json().catch(() => ({}));
        throw new Error(j?.message || "Échec de l’enregistrement de l’offre");
      }
      const data = await res.json().catch(() => ({}));

      // on tente de récupérer l’offre renvoyée, sinon on retombe sur notre payload
      const saved: OpenOfferMinimal = data?.offer ?? data ?? payload;

      setSuccess(isEdit ? "Offre mise à jour avec succès" : "Offre créée avec succès");
      onSaved?.(saved);
      onClose(); // ferme après succès; commente si tu veux garder ouvert
    } catch (e: any) {
      setError(e?.message || "Impossible d’enregistrer l’offre");
    } finally {
      setSaving(false);
    }
  };

  return (
    <Modal open={open} onClose={onClose} title={title} maxWidthClass="max-w-3xl" portal={portal}>
      <form onSubmit={handleSubmit} className="mt-2">
        <div className="max-h-[70vh] overflow-y-auto pr-2 space-y-6 px-9">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
              {error}
            </div>
          )}
          {success && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
              {success}
            </div>
          )}

          {/* Titre */}
          <div className="space-y-2">
            <label
              htmlFor="offer-title"
              className="block"
              style={{ fontSize: "14px", fontFamily: "'Inter', sans-serif", fontWeight: 500 }}
            >
              Titre de l’offre *
            </label>
            <input
              id="offer-title"
              type="text"
              placeholder="Ex: Création d'un personnage 3D pour jeu vidéo"
              autoComplete="off"
              className="w-full rounded-xl px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
              style={{
                borderRadius: "20px",
                border: "1px solid #1f1f1fff",
                backgroundColor: "#f6f7f8",
                fontFamily: "'Inter', sans-serif",
              }}
              value={offerTitle}
              onChange={(e) => setOfferTitle(e.target.value)}
              required
            />
          </div>

          {/* Budget (radio 5 colonnes + Autre) */}
          <fieldset className="space-y-3">
            <legend
              className="text-sm font-medium"
              style={{ fontSize: "14px", fontFamily: "'Inter', sans-serif", fontWeight: 500 }}
            >
              Budget *
            </legend>

            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-x-4 gap-y-3">
              {BUDGETS.map((b) => (
                <label
                  key={b}
                  className="inline-flex items-center gap-2 text-sm cursor-pointer p-2 rounded-lg hover:bg-gray-50 transition-colors"
                  style={{ fontFamily: "'Inter', sans-serif" }}
                >
                  <input
                    type="radio"
                    name="budget"
                    value={b}
                    checked={budgetPreset === b}
                    onChange={() => {
                      setBudgetPreset(b);
                      setBudgetCustom("");
                    }}
                    className="size-4 accent-black"
                    required={budgetPreset !== "OTHER" ? undefined : false}
                  />
                  <span>{b}</span>
                </label>
              ))}

              {/* Option Autre */}
              <label
                className="inline-flex items-center gap-2 text-sm cursor-pointer p-2 rounded-lg hover:bg-gray-50 transition-colors"
                style={{ fontFamily: "'Inter', sans-serif" }}
              >
                <input
                  type="radio"
                  name="budget"
                  value="OTHER"
                  checked={budgetPreset === "OTHER"}
                  onChange={() => setBudgetPreset("OTHER")}
                  className="size-4 accent-black"
                />
                <span>Autre</span>
              </label>
            </div>

            {budgetPreset === "OTHER" && (
              <div className="mt-2">
                <input
                  type="text"
                  placeholder="Ex: 500€ - 1000€"
                  className="w-full rounded-xl px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
                  style={{
                    borderRadius: "20px",
                    border: "1px solid #1f1f1fff",
                    backgroundColor: "#f6f7f8",
                    fontFamily: "'Inter', sans-serif",
                  }}
                  value={budgetCustom}
                  onChange={(e) => setBudgetCustom(e.target.value)}
                  required
                />
              </div>
            )}
          </fieldset>

          {/* Deadline */}
          <div className="space-y-2">
            <label
              htmlFor="offer-deadline"
              className="block"
              style={{ fontSize: "14px", fontFamily: "'Inter', sans-serif", fontWeight: 500 }}
            >
              Date limite *
            </label>
            <input
              id="offer-deadline"
              type="date"
              className="w-full rounded-xl px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500"
              style={{
                borderRadius: "20px",
                border: "1px solid #1f1f1fff",
                backgroundColor: "#f6f7f8",
                fontFamily: "'Inter', sans-serif",
              }}
              value={deadline}
              onChange={(e) => setDeadline(e.target.value)}
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <label
              htmlFor="offer-desc"
              className="block"
              style={{ fontSize: "14px", fontFamily: "'Inter', sans-serif", fontWeight: 500 }}
            >
              Description *
            </label>
            <textarea
              id="offer-desc"
              rows={6}
              className="w-full rounded-xl px-4 py-3 outline-none focus:ring-2 focus:ring-blue-500 placeholder:text-gray-400"
              style={{
                borderRadius: "20px",
                border: "1px solid #1f1f1fff",
                backgroundColor: "#f6f7f8",
                fontFamily: "'Inter', sans-serif",
              }}
              placeholder="Décrivez votre projet en détail…"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              required
            />
          </div>
        </div>

        {/* Footer */}
        <div className="pt-5 px-9">
          <button
            type="submit"
            disabled={saving}
            className="w-full rounded-xl bg-[#006eff] px-6 py-3 text-white font-semibold tracking-wide disabled:opacity-70"
            style={{ borderRadius: "20px", fontSize: "20px", fontWeight: 500, fontFamily: "'Inter', sans-serif", height: "58px" }}
          >
            {saving ? (isEdit ? "Mise à jour…" : "Création…") : isEdit ? "Mettre à jour l’offre" : "Créer l’offre"}
          </button>
        </div>
      </form>
    </Modal>
  );
}
