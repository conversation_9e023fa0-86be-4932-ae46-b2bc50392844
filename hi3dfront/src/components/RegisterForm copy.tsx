import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserType } from '../types/user';
import { UserCircle2 } from 'lucide-react';

export default function RegisterFormOld() {
  const [userType, setUserType] = useState<UserType | null>(null);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmation, setConfirmation] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [loading, setLoading] = useState(false); // État pour gérer le chargement
  const navigate = useNavigate();

  const handleContinue = async () => {
    if (!userType) {
      setError('Veuillez choisir un type de profil.');
      return;
    }

    setLoading(true); // Activer le chargement
    setError(''); // Réinitialiser les erreurs
    setSuccess(''); // Réinitialiser les messages de succès

    try {
      const payload = {
        first_name: firstName,
        last_name: lastName,
        email: email,
        password: password,
        password_confirmation: confirmation,
        is_professional: userType === 'professional',
      };

      console.log('Données envoyées à l\'API :', payload); // Log des données envoyées

      const response = await fetch('http://127.0.0.1:8000/api/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      console.log('Réponse de l\'API :', response); // Log de la réponse brute

      const data = await response.json();
      console.log('Données de la réponse :', data); // Log des données de la réponse

      if (response.ok) {
        setSuccess(data.message);
        navigate(`/create-profile/${userType}`);
      } else {
        setError(data.message || 'Une erreur est survenue lors de l\'inscription.');
      }
    } catch (err) {
      console.error('Erreur lors de la requête :', err); // Log des erreurs
      setError('Une erreur est survenue lors de la connexion au serveur.');
    } finally {
      setLoading(false); // Désactiver le chargement, que la requête réussisse ou échoue
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full">
        <div className="text-center mb-8">
          <UserCircle2 className="w-16 h-16 mx-auto text-indigo-600 mb-4" />
          <h1 className="text-2xl font-bold text-gray-900">Créer un compte</h1>
          <p className="text-gray-600 mt-2">Choisissez votre type de profil pour commencer</p>
        </div>

        <div className="space-y-4">
          <input
            type="text"
            placeholder="Prénom"
            value={firstName}
            onChange={(e) => setFirstName(e.target.value)}
            className="w-full p-4 rounded-lg border-2 border-gray-200 hover:border-indigo-200"
          />
          <input
            type="text"
            placeholder="Nom"
            value={lastName}
            onChange={(e) => setLastName(e.target.value)}
            className="w-full p-4 rounded-lg border-2 border-gray-200 hover:border-indigo-200"
          />
          <input
            type="email"
            placeholder="Email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="w-full p-4 rounded-lg border-2 border-gray-200 hover:border-indigo-200"
          />
          <input
            type="password"
            placeholder="Mot de passe"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full p-4 rounded-lg border-2 border-gray-200 hover:border-indigo-200"
          />
          <input
            type="password"
            placeholder="Confirmation du mot de passe"
            value={confirmation}
            onChange={(e) => setConfirmation(e.target.value)}
            className="w-full p-4 rounded-lg border-2 border-gray-200 hover:border-indigo-200"
          />

          <button
            onClick={() => setUserType('professional')}
            className={`w-full p-4 rounded-lg border-2 transition-all ${
              userType === 'professional'
                ? 'border-indigo-600 bg-indigo-50'
                : 'border-gray-200 hover:border-indigo-200'
            }`}
          >
            <h3 className="font-semibold text-gray-900">Professionnel Indépendant</h3>
            <p className="text-sm text-gray-600 mt-1">
              Proposez vos services et trouvez de nouveaux clients
            </p>
          </button>

          <button
            onClick={() => setUserType('client')}
            className={`w-full p-4 rounded-lg border-2 transition-all ${
              userType === 'client'
                ? 'border-indigo-600 bg-indigo-50'
                : 'border-gray-200 hover:border-indigo-200'
            }`}
          >
            <h3 className="font-semibold text-gray-900">Entreprise / Particulier</h3>
            <p className="text-sm text-gray-600 mt-1">
              Trouvez les meilleurs professionnels pour vos projets
            </p>
          </button>
        </div>

        {error && <p className="text-red-500 mt-4">{error}</p>}
        {success && <p className="text-green-500 mt-4">{success}</p>}

        <button
          onClick={handleContinue}
          disabled={!userType || !firstName || !lastName || !email || !password || loading} // Désactiver pendant le chargement
          className="w-full mt-8 bg-indigo-600 text-white py-3 rounded-lg font-medium
            disabled:bg-gray-300 disabled:cursor-not-allowed
            hover:bg-indigo-700 transition-colors flex items-center justify-center"
        >
          {loading ? (
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div> // Spinner
          ) : (
            'Continuer mmmmmmmm'
          )}
        </button>
      </div>
    </div>
  );
}