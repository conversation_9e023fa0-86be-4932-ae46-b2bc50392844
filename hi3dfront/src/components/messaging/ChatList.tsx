import React from 'react';
import { Search } from 'lucide-react';
import Avatar from '../ui/Avatar';

export interface ChatPreview {
  id: number;
  title: string;
  avatar?: string;
  lastMessage?: string;
  timestamp?: string;
  unreadCount?: number;
  status?: 'online' | 'offline' | 'away';
  isActive?: boolean;
}

interface ChatListProps {
  chats: ChatPreview[];
  onChatSelect: (chatId: number) => void;
  activeChat?: number;
  onSearch?: (query: string) => void;
}

const ChatList: React.FC<ChatListProps> = ({
  chats,
  onChatSelect,
  activeChat,
  onSearch,
}) => {
  // Format timestamp
  const formatTime = (dateString?: string) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    const now = new Date();
    const isToday = date.toDateString() === now.toDateString();
    
    if (isToday) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    const yesterday = new Date(now);
    yesterday.setDate(now.getDate() - 1);
    const isYesterday = date.toDateString() === yesterday.toDateString();
    
    if (isYesterday) {
      return 'Hier';
    }
    
    return date.toLocaleDateString([], { day: 'numeric', month: 'short' });
  };

  return (
    <div className="h-full flex flex-col border-r border-neutral-200 bg-white">
      <div className="p-4">
        <h2 className="text-xl font-bold text-neutral-900 mb-4">Messages</h2>
        
        {onSearch && (
          <div className="relative mb-4">
            <input
              type="text"
              placeholder="Rechercher..."
              className="w-full pl-10 pr-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              onChange={(e) => onSearch(e.target.value)}
            />
            <Search className="absolute left-3 top-2.5 h-5 w-5 text-neutral-400" />
          </div>
        )}
      </div>
      
      <div className="flex-1 overflow-y-auto">
        {chats.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-neutral-500 p-4 text-center">
            <p>Aucune conversation pour le moment</p>
            <p className="text-sm mt-1">Commencez à discuter avec des professionnels</p>
          </div>
        ) : (
          <ul className="divide-y divide-neutral-200">
            {chats.map((chat) => (
              <li
                key={chat.id}
                onClick={() => onChatSelect(chat.id)}
                className={`px-4 py-3 cursor-pointer hover:bg-neutral-50 transition-colors ${
                  chat.id === activeChat ? 'bg-neutral-100' : ''
                }`}
              >
                <div className="flex items-center">
                  <Avatar
                    size="md"
                    src={chat.avatar}
                    fallback={chat.title.charAt(0)}
                    status={chat.status}
                  />
                  
                  <div className="ml-3 flex-1 min-w-0">
                    <div className="flex justify-between items-baseline">
                      <h3 className="font-medium text-neutral-900 truncate">
                        {chat.title}
                      </h3>
                      {chat.timestamp && (
                        <span className="text-xs text-neutral-500 ml-2 flex-shrink-0">
                          {formatTime(chat.timestamp)}
                        </span>
                      )}
                    </div>
                    
                    <div className="flex justify-between items-center mt-1">
                      <p className="text-sm text-neutral-600 truncate">
                        {chat.lastMessage || 'Aucun message'}
                      </p>
                      
                      {chat.unreadCount && chat.unreadCount > 0 ? (
                        <span className="ml-2 flex-shrink-0 bg-primary-500 text-white text-xs font-medium rounded-full h-5 min-w-5 flex items-center justify-center px-1.5">
                          {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                        </span>
                      ) : null}
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default ChatList;
