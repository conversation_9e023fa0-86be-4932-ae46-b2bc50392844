import React, { useState, useEffect } from "react";
import Header from "./Header";
import Footer from "./Footer";
import HeroProfile from "./HeroProfile";
import { API_BASE_URL } from "../config";
import { useNavigate, useLocation } from "react-router-dom";
import { profileService } from "../services/profileService";
import SwitchServiceWorkAbout from "./SwitchServiceWorkAbout";
import GalleryServicePro from "./GalleryServicePro";
import GalleryWorkPro from "./GalleryWorkPro";
import AboutPro from "./AboutPro";

interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  is_professional: boolean;
}

interface PortfolioItem {
  id?: number;
  path?: string; 
  name?: string;
  type?: string;
  created_at?: string;
}

interface FreelanceProfile {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  phone: string;
  email?: string;
  address: string;
  city: string;
  country: string;
  skills: string[] | null;
  languages: string[] | null;
  availability_status: string | null;
  services_offered: string[];
  hourly_rate: string;
  completion_percentage: number;
  created_at: string;
  updated_at: string;
  avatar?: string;
  cover_photo?: string;
  profile_picture_path?: string;
  rating?: number;
  review_count?: number;
  bio?: string;
  title?: string;
  portfolio?: PortfolioItem[];
  user: User;
  likes_count?: number;
  views_count?: number;
}

const EditPortfolioPro: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [achievements, setAchievements] = useState<any[]>([]);
  const [services, setServices] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState<"services" | "work" | "about">("services");

  const [loading, setLoading] = useState<boolean>(true);
  const [profiles, setProfiles] = useState<any | null>(null);
  const [professional, setProfessional] = useState<FreelanceProfile | null>(null);

  const token = localStorage.getItem("token");
  const user = JSON.parse(localStorage.getItem("user") || "{}");

  const handleTabChange = (tab: "services" | "work" | "about") => {
    setActiveTab(tab);
  };

  const fetchProfileData = async () => {
    try {
      if (!token) {
        navigate("/login");
        return;
      }
      setLoading(true);
      const { profile } = await profileService.getProfile();
      setProfiles(profile)
    } catch (err) {
      console.error("Error fetching profile data:", err);
    } finally {
      setLoading(false);
    }
  };

  const fetchAchievements = async () => {
    try {
      setLoading(true);
      if (!token) {
        navigate("/login");
        return;
      }

      const response = await fetch(`${API_BASE_URL}/api/achievements`, {
        method: "GET",
        headers: { Authorization: `Bearer ${token}` },
      });

      if (!response.ok) {
        throw new Error("Erreur lors de la récupération des réalisations");
      }

      const data = await response.json();
      setAchievements(data.achievements || []);
      setLoading(false);
    } catch (error) {
      console.error("Erreur lors de la récupération des réalisations", error);
      setLoading(false);
    }
  };

  const fetchServices = async () => {
    try {
      setLoading(true);
      if (!token) {
        navigate("/login");
        return;
      }

      const userId = user.id;

      const response = await fetch(
        `${API_BASE_URL}/api/professionals/${userId}/service-offers`,
        {
          method: "GET",
          headers: { Authorization: `Bearer ${token}` },
        }
      );

      if (!response.ok) {
        throw new Error("Erreur lors de la récupération des service offert");
      }

      const data = await response.json();
      setServices(Array.isArray(data) ? data : (data?.data ?? [data]));
      console.log("Services : ", data)
      setLoading(false);
    } catch (error) {
      console.error("Erreur lors de la récupération des services", error);
      setLoading(false);
    }
  };

  const refreshAll = async () => {
    await fetchAchievements();
  };

  useEffect(() => {
    (async () => {
      await fetchAchievements();
      await fetchServices();
      await fetchProfileData();
    })();
  }, [token]);

  useEffect(() => {
    if (location.state && location.state.activeTab) {
      setActiveTab(location.state.activeTab);
    }
  }, [location.state]);

  useEffect(() => {
    if (profiles?.id) {
      fetchProfessional(profiles.id);
    }
  }, [profiles?.id]);

  const fetchProfessional = async (id : any) => {
          try {
            setLoading(true);
            let response = await fetch(`${API_BASE_URL}/api/professionals/${id}`, {
              method: 'GET',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'Content-Type': 'application/json',
              },
            });
            if (!response.ok) {
              response = await fetch(`${API_BASE_URL}/api/users/${id}`, {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${localStorage.getItem('token')}`,
                  'Content-Type': 'application/json',
                },
              });
            }
            if (!response.ok) {
              throw new Error('Impossible de récupérer les détails du professionnel');
            }
            const data = await response.json();
            if (data.professional) {
              let skills = [] as any[];
              if (data.professional.skills) {
                if (Array.isArray(data.professional.skills)) {
                  skills = data.professional.skills;
                } else if (typeof data.professional.skills === 'string') {
                  try {
                    skills = JSON.parse(data.professional.skills);
                  } catch (e) {
                    skills = [data.professional.skills];
                  }
                }
              }
              let portfolio = [] as any[];
              if (data.professional.portfolio) {
                if (Array.isArray(data.professional.portfolio)) {
                  portfolio = data.professional.portfolio;
                } else if (typeof data.professional.portfolio === 'string') {
                  try {
                    portfolio = JSON.parse(data.professional.portfolio);
                  } catch (e) {
                    portfolio = [];
                  }
                }
              }
              setProfessional({
                ...data.professional,
                skills: skills,
                portfolio: portfolio.length > 0 ? portfolio : [
                  { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                  { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                  { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
                ],
                likes_count: data.professional.likes_count,
                views_count: data.professional.views_count,
              });
            } else if (data.user && data.profile_data) {
              let portfolio = [] as any[];
              if (data.profile_data.portfolio) {
                if (Array.isArray(data.profile_data.portfolio)) {
                  portfolio = data.profile_data.portfolio;
                } else if (typeof data.profile_data.portfolio === 'string') {
                  try {
                    portfolio = JSON.parse(data.profile_data.portfolio);
                  } catch (e) {
                    portfolio = [];
                  }
                }
              }
              setProfessional({
                ...data.profile_data,
                user: data.user,
                portfolio: portfolio.length > 0 ? portfolio : [
                  { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                  { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                  { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
                ],
                likes_count: data.user.likes_count || 0,
                views_count: data.user.views_count || 0,
              });
            } else if (data.user) {
              setProfessional({
                id: data.user.id,
                user_id: data.user.id,
                first_name: data.user.first_name,
                last_name: data.user.last_name,
                phone: '',
                address: '',
                city: data.user.city || 'Paris',
                country: data.user.country || 'France',
                skills: data.user.skills || ['Animation 3D', 'Modélisation 3D', 'Rigging'],
                languages: ['Français', 'Anglais'],
                availability_status: data.user.availability_status || 'available',
                services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
                hourly_rate: data.user.hourly_rate || '45',
                completion_percentage: data.completion_percentage || 100,
                created_at: data.user.created_at,
                updated_at: data.user.updated_at,
                avatar: data.user.avatar,
                profile_picture_path: data.user.profile_picture_path,
                rating: data.user.rating || 4.8,
                review_count: data.user.review_count || 27,
                bio: data.user.bio || 'Artiste 3D passionné... ',
                title: data.user.title || 'Artiste 3D',
                portfolio: data.user.portfolio || [
                  { title: 'Projet 3D 1', image: 'https://picsum.photos/seed/101/300/200', description: 'Modélisation 3D pour un jeu vidéo' },
                  { title: 'Animation 3D', image: 'https://picsum.photos/seed/102/300/200', description: 'Animation de personnage' },
                  { title: 'Rendu architectural', image: 'https://picsum.photos/seed/103/300/200', description: 'Visualisation architecturale' },
                ],
                likes_count: data.user.likes_count || 0,
                views_count: data.user.views_count || 0,
                user: data.user,
              });
            } else {
              setProfessional({
                id: parseInt(id || '1'),
                user_id: parseInt(id || '1'),
                first_name: 'Thomas',
                last_name: 'Martin',
                phone: '+33 6 12 34 56 78',
                address: '123 Rue de la Création',
                city: 'Paris',
                country: 'France',
                skills: ['Animation 3D', 'Modélisation 3D', 'Rigging', 'Texturing', 'Rendu 3D'],
                languages: ['Français', 'Anglais'],
                availability_status: 'available',
                services_offered: ['Animation 3D', 'Modélisation 3D', 'Rigging'],
                hourly_rate: '45',
                completion_percentage: 100,
                created_at: '2023-01-01',
                updated_at: '2023-01-01',
                avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
                profile_picture_path: 'https://images.unsplash.com/photo-1717092068554-675b396555be?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTF8fDNkJTIwYXJ0aXN0JTIwYmxlbmRlcnxlbnwwfHwwfHx8MA%3D%3D',
                rating: 4.8,
                review_count: 27,
                portfolio: [
                  { name: 'Projet 3D 1', path: 'https://picsum.photos/seed/101/300/200', type: 'Modélisation 3D pour un jeu vidéo' },
                  { name: 'Animation 3D', path: 'https://picsum.photos/seed/102/300/200', type: 'Animation de personnage' },
                  { name: 'Rendu architectural', path: 'https://picsum.photos/seed/103/300/200', type: 'Visualisation architecturale' },
                ],
                likes_count: 0,
                views_count: 0,
                bio: 'Artiste 3D passionné...',
                title: 'Artiste 3D',
                user: {
                  id: parseInt(id || '1'),
                  first_name: 'Thomas',
                  last_name: 'Martin',
                  email: '<EMAIL>',
                  is_professional: true,
                },
              });
            }
          } catch (err) {
            console.error('Erreur lors de la récupération du professionnel:', err);
          } finally {
            setLoading(false);
          }
        };

   if (loading) {
      return (
        <div className="min-h-screen bg-white">
          <Header />
          <div className="flex justify-center items-center h-96">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
          <Footer />
        </div>
      );
    }

  if (!professional) {
      return (
        <div className="min-h-screen bg-white">
          <Header />
          <div className="flex justify-center items-center h-96">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
          </div>
          <Footer />
        </div>
      );
    }

  return (
    <>
    
      <Header />

      {(professional) && (
        <>
        <HeroProfile pro_detail={professional} isPro = {true}/>
        <SwitchServiceWorkAbout
          activeTab={activeTab}
          onTabChange={handleTabChange}
        />

        {activeTab === "services" && <GalleryServicePro itemsPro={services} onRefresh={fetchServices}/>}
        {activeTab === "work" && <GalleryWorkPro itemsPro={achievements} onRefresh={refreshAll}/>}
        {activeTab === "about" && <AboutPro pro_detail={professional} isPro = {true}/>}
        </>
      )}

      <Footer />
    </>
  );
};

export default EditPortfolioPro;