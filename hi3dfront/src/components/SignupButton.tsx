import React from 'react';
import Button from './ui/Button';
import { useNavigate } from 'react-router-dom';

const SignupButton: React.FC = () => {
  const navigate = useNavigate();

  const handleDubscribe = () => {
      navigate('/register');
  };

  return(
  <div style={{ display: 'flex', justifyContent: 'center', marginTop: 250, marginBottom: 140 }}>
    <Button
      size="lg"
      style={{ background: 'rgb(33, 33, 33)', color: '#fff', borderRadius: 32, fontWeight: 600, fontSize: 14, width: 207, height: 50 }}
      onClick={handleDubscribe}
    >
      Sign up to continue
    </Button>
  </div>
);
};

export default SignupButton; 