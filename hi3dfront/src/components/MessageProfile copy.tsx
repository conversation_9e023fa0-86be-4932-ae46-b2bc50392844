import React, { useState,useEffect } from 'react';
import { API_BASE_URL } from './../config';
import { useParams, useNavigate } from 'react-router-dom';

const MessageProfile = ({ application, offerDetail }: { application?: any; offerDetail?: any }) => {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const token = localStorage.getItem('token');
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  const isClient = currentUser.role === 'client' || !currentUser.is_professional;

  const getUrlProlfil = (path : string)  => {
          return path? `${API_BASE_URL}${path}`:'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
      };

  
  const currentApp = application?.application ?? application;

  useEffect(() => {
    console.log("🔄 Application nettoyée :", currentApp);
  }, [currentApp]);

  console.log("Detail offre : ",offerDetail)

  console.log("isClient : ",isClient)

  const hasAlreadyApplied = () => {
        // return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
        return offerDetail?.applications.some((application: any) => {
            return (
                application.freelance_profile?.user?.id === currentUser.id &&
                application.status === 'accepted'
            );
        });
    };

  const hasInvited = () => {
        // return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
        return offerDetail?.applications.some((application: any) => {
            return (
                application.freelance_profile?.user?.id === currentUser.id &&
                application.status === 'invited'
            );
        });
    };

  const hasAlreadyRefused = () => {
        // return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
        return offerDetail?.applications.some((application: any) => {
            return (
                application.freelance_profile?.user?.id === currentUser.id &&
                application.status === 'rejected'
            );
        });
    };

  const hasAlreadyPostule = () => {
        // return offer.applications.some((application: any) => application.freelance_profile?.user?.id === user.id);
        return offerDetail?.applications.some((application: any) => {
            return (
                application.freelance_profile?.user?.id === currentUser.id &&
                application.status === 'pending'
            );
        });
    };

  
  const handleDeclineOffer = async () => {
      // if (!token || !id) return;
      if (!token || !offerDetail || !offerDetail.applications) {
        setError("Données insuffisantes pour refuser l'offre. Il faut être invité pour pouvoir refuser. Merci");
        return;
      }
  
      // Chercher l'application liée à l'utilisateur connecté
      const application = offerDetail.applications.find((app:any) =>
        app.freelance_profile?.user?.id === currentUser.id
      );
  
      if (!application) {
        setError("Aucune candidature ou invitation trouvée pour vous. Il faut être invité pour pouvoir refuser. Merci");
        return;
      }
  
      const applicationId = application.id;
  
      try {
        const response = await fetch(`${API_BASE_URL}/api/offer-applications/${applicationId}/decline`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
  
        if (!response.ok) {
          throw new Error('Erreur lors du refus de l\'offre');
        }
  
        // Afficher un message de succès
        setSuccessMessage('Vous avez indiqué que vous n\'êtes pas disponible pour cette offre.');
  
        // Rediriger vers le tableau de bord après un court délai
        setTimeout(() => {
          window.location.reload();
        }, 2000);
  
        // // Envoyer une notification au client
        // if (offer) {
        //   addOfferNotification('offer_not_available', {
        //     offer_id: offer.id,
        //     offer_title: offer.title,
        //     professional_id: currentUser.id,
        //     professional_name: currentUser.name || 'Professionnel',
        //     professional_avatar: currentUser.avatar,
        //     client_id: offer.client?.id || 0,
        //     client_name: offer.client?.name || 'Client',
        //     client_avatar: offer.client?.avatar,
        //   });
        // }
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de refuser l\'offre. Veuillez réessayer plus tard.');
      }
    };

    const handleAcceptOffer = async () => {
        // if (!token || !id) return;
        if (!token || !offerDetail || !offerDetail.applications) {
          setError("Données insuffisantes pour accepter l'offre. Il faut être invité pour pouvoir accépté sinon vous pouvez postuler. Merci");
          return;
        }
    
        // Chercher l'application liée à l'utilisateur connecté
        const application = offerDetail.applications.find((app:any) =>
          app.freelance_profile?.user?.id === currentUser.id
        );
    
        if (!application) {
          setError("Aucune candidature ou invitation trouvée pour vous. Il faut être invité pour pouvoir accépté sinon vous pouvez postuler. Merci");
          return;
        }
    
        const applicationId = application.id;
    
        try {
          const response = await fetch(`${API_BASE_URL}/api/offer-applications/${applicationId}/accept`, {
            method: 'PUT',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });
    
    
          if (!response.ok) {
            const errorBody = await response.json();
            console.error('Erreur API:', errorBody);
            throw new Error(errorBody.message || 'Erreur lors de l\'acceptation de l\'offre');
          }
    
          console.log("Reponse : ",response)
          alert("L'offre accepter avec succès");
    
          setTimeout(() => {
          window.location.reload();
        }, 2000);

        } catch (err) {
          console.error('Erreur:', err);
          setError('Impossible d\'accepter l\'offre. Veuillez réessayer plus tard.');
        }
      };

      const handleApplyToProject = async(projectId: number) => {
            // navigate(`/dashboard/open-offers/${projectId}/apply`);
            const token = localStorage.getItem("token");
                try {
                    const response = await fetch(`${API_BASE_URL}/api/open-offers/${projectId}/apply`, {
                        method: 'POST',
                        headers: {
                            Authorization: `Bearer ${token}`,
                            'Content-Type': 'application/json',
                        },
                    });
        
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.message || 'Une erreur est survenue');
                    }
                    alert(`Vous avez marqué l'offre comme intéressé.`);
                    setTimeout(() => {
                      window.location.reload();
                    }, 2000);
                } catch (err) {
                    alert(err instanceof Error ? err.message : "Une erreur est survenue.");
                }
          };

  if(isClient===false && offerDetail){
    return(
      <div className="flex flex-col items-center min-h-[500px] py-6 w-[290px]">
    {/* Carte de profil */}
    <div className="w-full bg-white rounded-xl shadow-sm pt-1 pb-6 px-1 flex flex-col items-center mb-10 border border-[#f0f0f0]">
      {/* Bandeau d'images (pas d'arrondi en haut) */}
      <div className="w-full h-[90px] flex overflow-hidden relative" style={{ borderTopLeftRadius: '1rem', borderTopRightRadius: '1rem' }}>
        <img 
          src={`${API_BASE_URL}${offerDetail.user?.cover_photo}`||"https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=facearea&w=200&q=80"}
          alt="bandeau1" 
          className="object-cover w-full h-full"
          onError={(e) => {
            e.currentTarget.src = "https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=facearea&w=200&q=80";
          }} 
        />
        {/* <img src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=facearea&w=200&q=80" alt="bandeau2" className="object-cover w-1/3 h-full" />
        <img src="https://images.unsplash.com/photo-1500534314209-a25ddb2bd429?auto=format&fit=facearea&w=200&q=80" alt="bandeau3" className="object-cover w-1/3 h-full" /> */}
      </div>
      {/* Avatar + badge PRO */}
      <div className="relative flex flex-col  items-start w-full">
        <div className="absolute left-3 -top-7 z-20">
          <div className="relative">
            <img src={getUrlProlfil(offerDetail.user?.avatar)||"https://randomuser.me/api/portraits/men/32.jpg"}
              alt={`${offerDetail.user?.first_name} ${offerDetail.user?.last_name}`} 
              className="w-16 h-16 rounded-full border-4 border-white shadow"
              onError={(e) => {
                e.currentTarget.src = "https://randomuser.me/api/portraits/men/32.jpg";
              }} 
            />
            <span className="absolute bottom-1 right-0 bg-blue-600 text-white text-[8px] font-bold px-2 py-[1px] rounded-full text-xs border-2 border-white" style={{fontSize:'9px'}}>CUSTOMER</span>
          </div>
        </div>
        <div className="h-8" /> {/* espace pour l'avatar superposé */}
      </div>
      {/* Nom et sous-titre */}
      <div className="mt-2 text-[16px] font-semibold text-gray-900 px-3 text-left w-full">{offerDetail.user?.first_name} {offerDetail.user?.last_name}</div>
      {/* <div className="text-[13px] text-gray-500 text-left px-3 w-full">{freelance?.title || "No title provided"}</div> */}
      {/* Localisation */}
      {/* <div className="flex items-center text-[12px] px-3 text-gray-400 mt-1 mb-2 w-full">
        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 11c1.104 0 2-.896 2-2s-.896-2-2-2-2 .896-2 2 .896 2 2 2zm0 0c-3.866 0-7 3.134-7 7 0 1.657 1.343 3 3 3h8c1.657 0 3-1.343 3-3 0-3.866-3.134-7-7-7z" /></svg>
        {freelance?.address || "Unknown address"}, {freelance?.city || "Unknown city"} - {freelance?.country || "Unknown country"}
      </div> */}
      {/* Badge Freelance */}
      <span className="bg-purple-100 text-purple-500 text-[13px] font-medium px-4 py-1 rounded-full mt-1">CUSTOMER</span>
    </div>
    {/* Boutons (inchangés) */}

    {hasAlreadyApplied() ? (
          <p className="text-green-700 font-semibold">✅ Vous êtes déjà intéressé par cette offre.</p>
      ) : hasAlreadyPostule () ?(
        <p className="text-yellow-600 font-medium bg-yellow-100 px-3 py-2 rounded-md shadow-sm w-fit">
        ⏳ Votre demande est en attente d’acceptation du client.
        </p>
      ): hasAlreadyRefused () ? (
        <p className="text-red-700 font-semibold bg-red-100 px-3 py-2 rounded-md shadow-sm w-fit">
        ❌ Vous avez déjà refusé cette offre.
        </p>
      ) : hasInvited () ? (
          <>
            <button 
              className="w-full py-3 rounded-full bg-white text-black border border-black font-semibold text-base transition hover:bg-gray-100 mb-2"
              onClick={handleDeclineOffer}
            >
              Pas disponible
            </button> 

            <button 
              className="w-full py-3 rounded-full bg-black text-white font-semibold text-base mb-4 transition hover:bg-gray-900"
              onClick={handleAcceptOffer}
            >
              Interessé
            </button>
          </>
      ) : (
      <>
      <button 
        className="w-full py-3 rounded-full bg-black text-white font-semibold text-base mb-4 transition hover:bg-gray-900"
        onClick={()=> handleApplyToProject(Number(offerDetail.id))}
      >
        Postuler
      </button>
      </>
    )}

    {/*

     {offerDetail.status === 'open'  &&(
      <button 
        className="w-full py-3 rounded-full bg-black text-white font-semibold text-base mb-4 transition hover:bg-gray-900"
        onClick={()=> handleAcceptApplication(currentApp.id)}
      >
        Interessé
      </button>
    )}

    
     {currentApp.status==='accepted' && offerDetail.status === 'open'  &&(
    <button 
      className="w-full py-3 rounded-full bg-black text-white font-semibold text-base mb-4 transition hover:bg-gray-900"
      onClick={()=> handleSelectProfessional(currentApp.id)}
    >
      Approve the offer
    </button>
    )}
    <button 
      className="w-full py-3 rounded-full bg-white text-black border border-black font-semibold text-base transition hover:bg-gray-100"
      onClick={() => navigate(`/professionals/${freelance.id}`)}
    >
      View profil
    </button>  */}
  </div>
    );

  }

  const handleAcceptApplication = async (applicationId: number) => {
          const token = localStorage.getItem("token");
          try {
              const response = await fetch(`${API_BASE_URL}/api/offer-applications/${applicationId}/status`, {
                  method: 'PATCH',
                  headers: {
                      Authorization: `Bearer ${token}`,
                      'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({ status: 'accepted' }),
              });
              if (!response.ok) throw new Error("Erreur lors de l'acceptation");
              window.location.reload();
          } catch (error) {
              alert("Échec de l'acceptation de la candidature.");
          }
      };

  const handleSelectProfessional = async (applicationId: number) => {
      if (!token || !offerDetail.id) return;
      console.log("Contenu : ",JSON.stringify({ application_id: applicationId }))
      try {
        const response = await fetch(`${API_BASE_URL}/api/open-offers/${offerDetail.id}/assign`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ application_id: applicationId }),
        });
  
        if (!response.ok) {
          console.log("Retour : ",response)
          throw new Error('Erreur lors de la sélection du professionnel');
        }
        // Afficher un message de succès
        setSuccessMessage('Vous avez sélectionné ce professionnel pour votre projet. Il en sera informé.');
        window.location.reload();
      } catch (err) {
        console.error('Erreur:', err);
        setError('Impossible de sélectionner le professionnel. Veuillez réessayer plus tard.');
      }
    };

  if (!currentApp || !currentApp.freelance_profile) {
    return (
      <div className="flex flex-col items-center min-h-[500px] py-6 w-[290px] justify-center text-gray-400">
        <div className="w-24 h-24 mb-4 flex items-center justify-center bg-gray-100 rounded-full text-4xl opacity-50">👤</div>
        <p className="text-sm">No profile selected</p>
        <p className="text-xs mt-2">Click on a candidate to see their profile</p>
      </div>
    );
  }

  const freelance = currentApp.freelance_profile;
  

  return(
  <div className="flex flex-col items-center min-h-[500px] py-6 w-[290px]">
    {/* Carte de profil */}
    <div className="w-full bg-white rounded-xl shadow-sm pt-1 pb-6 px-1 flex flex-col items-center mb-10 border border-[#f0f0f0]">
      {/* Bandeau d'images (pas d'arrondi en haut) */}
      <div className="w-full h-[90px] flex overflow-hidden relative" style={{ borderTopLeftRadius: '1rem', borderTopRightRadius: '1rem' }}>
        <img 
          src={`${API_BASE_URL}${freelance.cover_photo}`||"https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=facearea&w=200&q=80"}
          alt="bandeau1" 
          className="object-cover w-full h-full" 
        />
        {/* <img src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=facearea&w=200&q=80" alt="bandeau2" className="object-cover w-1/3 h-full" />
        <img src="https://images.unsplash.com/photo-1500534314209-a25ddb2bd429?auto=format&fit=facearea&w=200&q=80" alt="bandeau3" className="object-cover w-1/3 h-full" /> */}
      </div>
      {/* Avatar + badge PRO */}
      <div className="relative flex flex-col  items-start w-full">
        <div className="absolute left-3 -top-7 z-20">
          <div className="relative">
            <img src={getUrlProlfil(freelance.avatar)||"https://randomuser.me/api/portraits/men/32.jpg"}
              alt={`${freelance?.first_name} ${freelance?.last_name}`} 
              className="w-16 h-16 rounded-full border-4 border-white shadow" 
            />
            <span className="absolute bottom-1 right-0 bg-blue-600 text-white text-[8px] font-bold px-2 py-[1px] rounded-full text-xs border-2 border-white" style={{fontSize:'9px'}}>PRO</span>
          </div>
        </div>
        <div className="h-8" /> {/* espace pour l'avatar superposé */}
      </div>
      {/* Nom et sous-titre */}
      <div className="mt-2 text-[16px] font-semibold text-gray-900 px-3 text-left w-full">{freelance?.first_name} {freelance?.last_name}</div>
      <div className="text-[13px] text-gray-500 text-left px-3 w-full">{freelance?.title || "No title provided"}</div>
      {/* Localisation */}
      <div className="flex items-center text-[12px] px-3 text-gray-400 mt-1 mb-2 w-full">
        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 11c1.104 0 2-.896 2-2s-.896-2-2-2-2 .896-2 2 .896 2 2 2zm0 0c-3.866 0-7 3.134-7 7 0 1.657 1.343 3 3 3h8c1.657 0 3-1.343 3-3 0-3.866-3.134-7-7-7z" /></svg>
        {freelance?.address || "Unknown address"}, {freelance?.city || "Unknown city"} - {freelance?.country || "Unknown country"}
      </div>
      {/* Badge Freelance */}
      <span className="bg-purple-100 text-purple-500 text-[13px] font-medium px-4 py-1 rounded-full mt-1">PRO</span>
    </div>
    {/* Boutons (inchangés) */}

    {currentApp.status==='pending' && offerDetail.status === 'open'  &&(
    <button 
      className="w-full py-3 rounded-full bg-black text-white font-semibold text-base mb-4 transition hover:bg-gray-900"
      onClick={()=> handleAcceptApplication(currentApp.id)}
    >
      Accept demande
    </button>
    )}

    
    {currentApp.status==='accepted' && offerDetail.status === 'open'  &&(
    <button 
      className="w-full py-3 rounded-full bg-black text-white font-semibold text-base mb-4 transition hover:bg-gray-900"
      onClick={()=> handleSelectProfessional(currentApp.id)}
    >
      Approve the offer
    </button>
    )}
    <button 
      className="w-full py-3 rounded-full bg-white text-black border border-black font-semibold text-base transition hover:bg-gray-100"
      onClick={() => navigate(`/professionals/${freelance.id}`)}
    >
      View profil
    </button>
  </div>
);
};

export default MessageProfile;

                          