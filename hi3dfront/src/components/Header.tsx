import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { User, <PERSON>ting<PERSON>, <PERSON><PERSON><PERSON>, LogOut, FilePlus, List } from "lucide-react";
import ProfileWizard from "./ProfileWizard";
import Dropdown from "./ui/Dropdown";
import MobileMenu from "./ui/MobileMenu";
import SearchBar from "./SearchBar";
import LoginForm from "./auth/LoginForm";
import RegisterForm from "./auth/RegisterForm";
import ForgotPasswordForm from "./auth/ForgotPasswordForm";
import ModalFilter from "./ModalFilter";
import ModalMobileSearch from "./ModalMobileSearch";

interface NavLink {
  label: string;
  href: string;
  children?: Array<{ label: string; href: string }>;
}
interface HeaderProps {
  onSearchGlobal?: (term: string, type: string) => void;
}

function Header({ onSearchGlobal }: HeaderProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const user = JSON.parse(localStorage.getItem("user") || "{}");
  const token = localStorage.getItem("token");
  const isAuthenticated = !!token && !!user && !!user.id;
  const navigate = useNavigate();
  const location = useLocation();

  const navLinks: NavLink[] = [];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  function handleLogout() {
    localStorage.removeItem("user");
    localStorage.removeItem("token");
    localStorage.removeItem("userProfile");
    navigate("/");
  }

  function handleHomeClick() {
    navigate("/");
  }

  function handleLoginClick() {
    setIsLoginModalOpen(true);
  }

  function handleRegisterClick() {
    navigate("/register");
  }

  const userDropdownItems = [
    {
      label: `${user.first_name || ""} ${user.last_name || ""}`,
      href: user.is_professional
      ? "/edit-portfolio"
      : "/client-profil",
      divider: true,
    },
    {
      label: "Tableau de bord",
      href: "/dashboard",
      icon: <BarChart className="w-4 h-4" />,
    },
    {
      label: "Mon profil",
      href: user.is_professional
        ? "/dashboard/profile"
        : "/dashboard/profile-client-dashboard",
      icon: <User className="w-4 h-4" />,
    },
    {
      label: "Mes offres",
      href: user.is_professional
        ?"/dashboard/open-offers"
        :"/dashboard/projects",
      icon: <List className="w-4 h-4" />,
    },
    {
      label: "Créer une offre",
      href: "/dashboard/projects?create=true",
      icon: <FilePlus className="w-4 h-4" />,
    },
    {
      label: "Paramètres",
      href: "/settings",
      icon: <Settings className="w-4 h-4" />,
    },
    {
      label: "Se déconnecter",
      onClick: handleLogout,
      icon: <LogOut className="w-4 h-4" />,
    },
  ];

  return (
    <header
      className={`sticky top-0 z-40 w-full ${isScrolled ? "py-3" : "py-5"
        } bg-transparent`}
    >
      <div className="w-full flex items-center justify-between h-[60px] px-[10px] md:px-[40px]">
        {/* ---- Partie gauche ---- */}
        <div className="flex items-center gap-4">
          <div
            onClick={handleHomeClick}
            className="flex-shrink-0 cursor-pointer"
          >
            <img
              src="/img/logo-Hi3d.svg"
              alt="Hi3D Logo"
              className="h-5 w-14"
            />
          </div>

          {/* Desktop ≥ 1025px → bouton menu + search bar */}
          <div className="hidden lg:flex items-center gap-2">
            <button
              className="flex items-center justify-center rounded-lg p-3 bg-gray-100 w-[65px] h-[52px]"
              onClick={() => setIsFilterOpen(true)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 512 512"
                width="28"
                height="28"
              >
                <path d="M256 48C141.31 48 48 141.31 48 256s93.31 208 208 208 208-93.31 208-208S370.69 48 256 48zm32 304h-64a16 16 0 010-32h64a16 16 0 010 32zm48-64H176a16 16 0 010-32h160a16 16 0 010 32zm32-64H144a16 16 0 010-32h224a16 16 0 010 32z"></path>
              </svg>
            </button>
            <ModalFilter
              isOpen={isFilterOpen}
              onClose={() => setIsFilterOpen(false)}
              onApply={(filters) => {
                const { country, language, minPrice, maxPrice } = filters;
                const state = {
                  location: country,
                  language: language,
                  minPrice: minPrice,
                  maxPrice: maxPrice,
                  // Si pays ou langue, forcer le type artistes, sinon si prix → services
                  type: (country || language) ? "Artist 3D" : (minPrice > 0 || maxPrice > 0 ? "Services" : undefined),
                } as any;

                const isOnSearchPage = location.pathname === "/search-global";
                if (isOnSearchPage) {
                  navigate("/search-global", { state, replace: true });
                } else {
                  navigate("/search-global", { state });
                }
              }}
            />
            <SearchBar
              width={500}
              height={52}
              iconSize={30}
              isGlobal={true}
              onSearchGlobal={onSearchGlobal}
              showTypeDropdown={true}
            />
          </div>
        </div>

        {/* ---- Partie droite ---- */}
        <div className="flex items-center gap-3">
          {isAuthenticated ? (
            /* Barre d'icônes pour utilisateur connecté */
            <div
              className="bg-[#F6F7F8] text-black flex items-center justify-between px-6 text-sm font-medium cursor-pointer"
              style={{
                width: "350px",
                height: "52px",
                borderRadius: "12px",
                boxShadow: "0px 0px 10px 0px rgba(0, 0, 0, 0.19)",
              }}
            >
              {/* Conteneur pour les icônes avec espacement égal */}
              <div className="flex items-center justify-between w-full">
                {/* Icône 1 */}
                <div
                  className="flex items-center justify-center hover:bg-gray-300 rounded-[18px] transition-colors duration-200"
                  style={{ width: "54px", height: "42px" }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 29 29"
                    fill="none"
                  >
                    <g clipPath="url(#clip0_201_86853)">
                      <path
                        d="M5.0105 17.7471C5.0105 18.7432 5.67847 19.3995 6.73316 19.3995H13.073V24.8721C13.073 26.6651 13.823 28.1534 14.116 28.1534C14.3972 28.1534 15.1472 26.6651 15.1472 24.8721V19.3995H21.4871C22.5417 19.3995 23.2097 18.7432 23.2097 17.7471C23.2097 15.2745 21.2293 12.6612 17.9363 11.4659L17.5496 6.08691C19.2605 5.11426 20.6667 4.01269 21.2761 3.22754C21.5808 2.8291 21.7332 2.43066 21.7332 2.0791C21.7332 1.36426 21.1824 0.836914 20.3621 0.836914H7.86988C7.03784 0.836914 6.49878 1.36426 6.49878 2.0791C6.49878 2.43066 6.63941 2.8291 6.94409 3.22754C7.55347 4.01269 8.95972 5.11426 10.6707 6.08691L10.2839 11.4659C6.99097 12.6612 5.0105 15.2745 5.0105 17.7471Z"
                        fill="black"
                        fillOpacity="0.85"
                      ></path>
                    </g>
                    <defs>
                      <clipPath id="clip0_201_86853">
                        <rect
                          width="18.1992"
                          height="28.043"
                          fill="white"
                          transform="translate(5.0105 0.110352)"
                        ></rect>
                      </clipPath>
                    </defs>
                  </svg>
                </div>

                {/* Icône 2 */}
                <div
                  className="flex items-center justify-center hover:bg-gray-300 rounded-[18px] transition-colors duration-200"
                  style={{ width: "54px", height: "42px" }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="25"
                    viewBox="0 0 29 29"
                    fill="none"
                  >
                    <g clipPath="url(#clip0_201_81471)">
                      <path
                        d="M4.81152 21.9981H23.3858C24.4873 21.9981 25.1553 21.4355 25.1553 20.5801C25.1553 19.4082 23.96 18.3535 22.9522 17.3105C22.1787 16.502 21.9678 14.8379 21.874 13.4902C21.792 8.99024 20.5967 5.89649 17.4795 4.77149C17.0342 3.23633 15.8272 2.0293 14.0928 2.0293C12.3701 2.0293 11.1514 3.23633 10.7178 4.77149C7.60058 5.89649 6.40527 8.99024 6.32324 13.4902C6.22949 14.8379 6.01855 16.502 5.24511 17.3105C4.22558 18.3535 3.04199 19.4082 3.04199 20.5801C3.04199 21.4355 3.69824 21.9981 4.81152 21.9981ZM14.0928 26.6973C16.085 26.6973 17.5381 25.2441 17.6904 23.6035H10.5068C10.6592 25.2441 12.1123 26.6973 14.0928 26.6973Z"
                        fill="black"
                        fillOpacity="0.85"
                      ></path>
                    </g>
                    <defs>
                      <clipPath id="clip0_201_81471">
                        <rect
                          width="22.1133"
                          height="24.668"
                          fill="white"
                          transform="translate(3.04199 2.0293)"
                        ></rect>
                      </clipPath>
                    </defs>
                  </svg>
                </div>

                {/* Icône 3 */}
                <div
                  className="flex items-center justify-center hover:bg-gray-300 rounded-[18px] transition-colors duration-200 cursor-pointer"
                  style={{ width: "54px", height: "42px" }}
                  onClick={() => navigate("/dashboard/profile")}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="17"
                    height="17"
                    viewBox="0 0 30 31"
                    fill="none"
                  >
                    <g clipPath="url(#clip0_201_5255)">
                      <path
                        d="M26.9723 8.27646H15.8495L12.1419 4.56885H2.87287C1.33711 4.56885 0.0921631 5.81379 0.0921631 7.34956V24.0338C0.0921631 25.5696 1.33711 26.8145 2.87287 26.8145H26.9723C28.5081 26.8145 29.7531 25.5696 29.7531 24.0338V11.0572C29.7531 9.52141 28.5081 8.27646 26.9723 8.27646Z"
                        fill="black"
                      ></path>
                    </g>
                    <defs>
                      <clipPath id="clip0_201_5255">
                        <rect
                          width="29.6609"
                          height="29.6609"
                          fill="white"
                          transform="translate(0.0922852 0.861328)"
                        ></rect>
                      </clipPath>
                    </defs>
                  </svg>
                </div>

                {/* Icône 4 avec dropdown */}
                <div className="flex items-center justify-center hover:bg-gray-300 rounded-[18px] transition-colors duration-200">
                  <Dropdown
                    trigger={
                      <div
                        className="flex items-center justify-center cursor-pointer"
                        style={{ width: "54px", height: "42px" }}
                      >
                        <svg
                          width="15"
                          height="18"
                          viewBox="0 0 448 512"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path d="M224 256c70.7 0 128-57.3 128-128S294.7 0 224 0 96 57.3 96 128s57.3 128 128 128zm89.6 32h-16.7c-22.2 10.2-46.9 16-72.9 16s-50.6-5.8-72.9-16h-16.7C60.2 288 0 348.2 0 422.4V464c0 26.5 21.5 48 48 48h352c26.5 0 48-21.5 48-48v-41.6c0-74.2-60.2-134.4-134.4-134.4z"></path>
                        </svg>
                      </div>
                    }
                    items={userDropdownItems}
                    align="right"
                  />
                </div>

                {/* Icône 5 */}
                <div
                  className="flex items-center justify-center hover:bg-gray-300 rounded-[18px] transition-colors duration-200"
                  style={{ width: "54px", height: "42px" }}
                >
                  <svg
                    width="18"
                    height="18"
                    viewBox="0 0 448 512"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M432 416H16a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16zm0-128H16a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16zm0-128H16a16 16 0 0 0-16 16v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16v-32a16 16 0 0 0-16-16zm0-128H16A16 16 0 0 0 0 48v32a16 16 0 0 0 16 16h416a16 16 0 0 0 16-16V48a16 16 0 0 0-16-16z"></path>
                  </svg>
                </div>
              </div>
            </div>
          ) : (
            <>
              {/* Desktop ≥ 1025px */}
              <div className="hidden lg:flex items-center gap-4">
                <button
                  onClick={handleLoginClick}
                  className="bg-blue-600 hover:bg-black text-white rounded-full px-6 py-2 transition-colors"
                  style={{
                    fontSize: "16px",
                    fontFamily: "'Inter', sans-serif",
                    fontWeight: 400,
                  }}
                >
                  Log in
                </button>
              </div>

              {/* Mobile < 1025px */}
              <div className="lg:hidden flex items-center gap-2">
                <div className="flex items-center justify-center w-[50px] h-[38px] bg-[#F5F5F5] rounded-full">
                  <svg
                    onClick={() => setIsSearchOpen(true)}
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 63 63"
                    fill="none"
                  >
                    <circle
                      cx="31.2031"
                      cy="31.3984"
                      r="31"
                      fill="#212121"
                    ></circle>
                    <path
                      d="M29.8507 38.6895C31.2272 39.2717 32.6887 39.5669 34.1945 39.5669C35.7003 39.5669 37.1618 39.2717 38.5383 38.6895C39.8673 38.1274 41.0604 37.323 42.0848 36.2987C43.1091 35.2743 43.9135 34.0812 44.4756 32.7522C45.0578 31.3757 45.353 29.9143 45.353 28.4085C45.353 26.9026 45.0578 25.4412 44.4756 24.0647C43.9135 22.7357 43.1091 21.5426 42.0848 20.5182C41.0604 19.4939 39.8673 18.6895 38.5383 18.1274C37.1618 17.5452 35.7004 17.25 34.1945 17.25C32.6888 17.25 31.2273 17.5452 29.8507 18.1274C28.5218 18.6895 27.3287 19.4939 26.3043 20.5182C25.28 21.5426 24.4756 22.7357 23.9135 24.0647C23.3313 25.4412 23.0361 26.9026 23.0361 28.4085C23.0361 29.9142 23.3313 31.3757 23.9135 32.7522C24.211 33.4558 24.5772 34.1208 25.0073 34.7433L17.6435 42.1071C16.8558 42.8948 16.8558 44.1718 17.6435 44.9595C18.0373 45.3533 18.5535 45.5503 19.0697 45.5503C19.5859 45.5503 20.1021 45.3533 20.4959 44.9595L27.8597 37.5957C28.4822 38.0257 29.1472 38.3919 29.8507 38.6895ZM28.203 22.4169C29.8034 20.8166 31.9312 19.9352 34.1945 19.9352C36.4578 19.9352 38.5856 20.8166 40.186 22.4169C41.7864 24.0173 42.6678 26.1451 42.6678 28.4085C42.6678 30.6718 41.7864 32.7996 40.186 34.3999C38.5856 36.0003 36.4578 36.8817 34.1945 36.8817C31.9312 36.8817 29.8034 36.0003 28.203 34.3999C26.6027 32.7996 25.7213 30.6718 25.7213 28.4085C25.7213 26.1452 26.6027 24.0174 28.203 22.4169Z"
                      fill="white"
                    ></path>
                  </svg>
                </div>
                <button
                  onClick={handleLoginClick}
                  className="bg-blue-600 hover:bg-black text-white rounded-full px-4 py-2 text-sm"
                >
                  Log in
                </button>
              </div>

              {/* Modal de recherche mobile */}
              <ModalMobileSearch
                isOpen={isSearchOpen}
                onClose={() => setIsSearchOpen(false)}
              />
            </>
          )}
        </div>
      </div>

      {/* Menu mobile */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        links={navLinks}
        authButtons={
          !isAuthenticated && (
            <div className="flex flex-col space-y-3">
              <button
                onClick={handleLoginClick}
                className="w-full px-4 py-2 rounded-full bg-white text-neutral-900 font-medium"
              >
                Log in
              </button>
              <button
                onClick={handleRegisterClick}
                className="w-full px-4 py-2 rounded-full border border-white text-white font-medium"
              >
                Sign up
              </button>
            </div>
          )
        }
      />

      {/* Modals */}
      {isLoginModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative">
            <LoginForm
              onClose={() => setIsLoginModalOpen(false)}
              onSuccess={() => {
                setIsLoginModalOpen(false);
                navigate("/dashboard");
              }}
              onToggleForm={() => {
                setIsLoginModalOpen(false);
                navigate("/register");
              }}
              onOpenRegister={() => {
                setIsLoginModalOpen(false);
                setIsRegisterModalOpen(true);
              }}
              onOpenForgotPassword={() => {
                setIsLoginModalOpen(false);
                setShowForgotPassword(true);
              }}
            />
          </div>
        </div>
      )}

      {isRegisterModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative">
            <RegisterForm
              onClose={() => setIsRegisterModalOpen(false)}
              onToggleForm={() => {
                setIsRegisterModalOpen(false);
                setIsLoginModalOpen(true);
              }}
            />
          </div>
        </div>
      )}

      {showForgotPassword && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="relative">
            <ForgotPasswordForm
              onClose={() => setShowForgotPassword(false)}
              onBackToLogin={() => {
                setShowForgotPassword(false);
                setIsLoginModalOpen(true);
              }}
            />
          </div>
        </div>
      )}

      {isModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-11/12 sm:w-3/4 max-w-lg relative">
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute top-3 right-3 text-neutral-500 hover:text-neutral-800"
            >
              ✖
            </button>
            <ProfileWizard />
          </div>
        </div>
      )}
    </header>
  );
}

export default Header;
