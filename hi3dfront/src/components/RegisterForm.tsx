import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { UserType } from '../types/user';
import { API_BASE_URL } from '../config';
import Header from './Header';
import Footer from './Footer';

export default function RegisterForm() {
  const [userType, setUserType] = useState<UserType | null>(null);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmation, setConfirmation] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isLoading, setIsLoading] = useState(false); // État pour gérer le chargement
  const navigate = useNavigate();

  const handleContinue = async () => {
    setError('');
    setSuccess('');
    setIsLoading(true); // Activer le chargement

    if (!userType) {
      setError('Veuillez choisir un type de profil.');
      setIsLoading(false); // Désactiver le chargement
      return;
    }

    // if (password !== confirmation) {
    //   alert("Les deux mots de passe doivent être identiques !");
    //   return;
    // }
    if (password !== confirmation) {
      setError('Les deux mots de passe doivent être identiques !');
      setIsLoading(false); // Désactiver le chargement
      return;
    }

    const payload = {
      first_name: firstName,
      last_name: lastName,
      email: email,
      password: password,
      password_confirmation: confirmation,
      is_professional: userType === 'professional',
    };

    console.log('Données envoyées à l\'API :', payload);

    try {
      const response = await fetch(`${API_BASE_URL}/api/register`, {
        method: 'POST',
        credentials: "include",
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(payload),
      });

      const responseText = await response.text();
      console.log('Réponse brute :', responseText);

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        throw new Error('La réponse du serveur n\'est pas au format JSON.');
      }

      console.log('Données retour de l\'API :', data);

      if (!response.ok) {
        setError(data.message || 'Une erreur est survenue lors de l\'inscription.');
      } else {
        setSuccess(data.message);
        setTimeout(() => {
          navigate('/login');
        }, 2000);
      }
    } catch (err) {
      console.error('Erreur lors de la requête :', err);
      setError('Une erreur est survenue lors de la connexion au serveur.');
    } finally {
      setIsLoading(false); // Désactiver le chargement après la requête
    }
  };

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-lg p-8 max-w-md w-full">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-900">Créer un compte</h1>
            <p className="text-gray-600 mt-2">Choisissez votre type de profil pour commencer</p>
          </div>

          <div className="space-y-4">
            <input
              type="text"
              placeholder="Prénom"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              className="w-full p-4 rounded-lg border-2 border-gray-200 hover:border-indigo-200"
            />
            <input
              type="text"
              placeholder="Nom"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              className="w-full p-4 rounded-lg border-2 border-gray-200 hover:border-indigo-200"
            />
            <input
              type="email"
              placeholder="Email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-4 rounded-lg border-2 border-gray-200 hover:border-indigo-200"
            />
            <input
              type="password"
              placeholder="Mot de passe"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-4 rounded-lg border-2 border-gray-200 hover:border-indigo-200"
            />
            <input
              type="password"
              placeholder="Confirmation du mot de passe"
              value={confirmation}
              onChange={(e) => setConfirmation(e.target.value)}
              className="w-full p-4 rounded-lg border-2 border-gray-200 hover:border-indigo-200"
            />

            <button
              onClick={() => setUserType('professional')}
              className={`w-full p-4 rounded-lg border-2 transition-all ${userType === 'professional' ? 'border-indigo-600 bg-indigo-50' : 'border-gray-200 hover:border-indigo-200'
                }`}
            >
              <h3 className="font-semibold text-gray-900">Professionnel Indépendant</h3>
            </button>

            <button
              onClick={() => setUserType('client')}
              className={`w-full p-4 rounded-lg border-2 transition-all ${userType === 'client' ? 'border-indigo-600 bg-indigo-50' : 'border-gray-200 hover:border-indigo-200'
                }`}
            >
              <h3 className="font-semibold text-gray-900">Entreprise / Particulier</h3>
            </button>
          </div>

          {error && <p className="text-red-500 mt-4">{error}</p>}
          {success && <p className="text-green-500 mt-4">{success}</p>}

          <button
            onClick={handleContinue}
            disabled={!userType || !firstName || !lastName || !email || !password || !confirmation || isLoading}
            className="w-full mt-8 bg-indigo-600 text-white py-3 rounded-lg font-medium disabled:bg-gray-300 disabled:cursor-not-allowed hover:bg-indigo-700 transition-colors"
          >
            {isLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
              </div>
            ) : (
              'Continuer'
            )}
          </button>
        </div>
      </div>
      <Footer />
    </>
  );
}