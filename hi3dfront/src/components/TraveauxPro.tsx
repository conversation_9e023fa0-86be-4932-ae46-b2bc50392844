import React, { useState,useEffect } from 'react';
import { MoreHorizontal } from 'lucide-react';
import CreateServiceModal from './CreateServiceModal';
import { API_BASE_URL } from '../config';

interface Service {
  id: number;
  title: string;
  author: string;
  imageUrl: string;
  description: string;
  executionTime: string;
  concepts: string;
  revisions: string;
  categories: string[];
  isPrivate: boolean;
  likes: number;
  views: number;
  price: number;
}

const TraveauxPro = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<Service | null>(null);

  useEffect(() => {
      const fetchServices = async () => {
        const token = localStorage.getItem('token');
        if (!token) {
          console.error('Token manquant');
          return;
        }
  
        try {
          const response = await fetch(`${API_BASE_URL}/api/service-offers`, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json',
            },
          });
  
          if (response.ok) {
            const data = await response.json();
            const formattedServices = data.service_offers.map((service: any) => ({
              id: service.id,
              title: service.title,
              author: `${service.user.first_name} ${service.user.last_name}`,
              imageUrl: "https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
              description: service.description,
              executionTime: service.execution_time,
              concepts: service.concepts,
              revisions: service.revisions,
              categories: JSON.parse(service.categories),
              isPrivate: service.is_private,
              likes: service.likes,
              views: service.views,
              price: service.price,
            }));
            setServices(formattedServices);
            console.log('Services récupérés depuis l\'API :', formattedServices);
          } else {
            console.error('Erreur lors de la récupération des services', response.statusText);
          }
        } catch (error) {
          console.error('Erreur lors de la récupération des services', error);
        }
      };
  
      fetchServices();
    }, []);

  // useEffect(() => {
  //   const storedServices = localStorage.getItem('services');
  //   if (storedServices) {
  //     const parsedServices = JSON.parse(storedServices);
  //     console.log('Services mémorisés chargés :', parsedServices);
  //     setServices(parsedServices);
  //   }
  // }, []);

  useEffect(() => {
    if (services.length > 0) {
      localStorage.setItem('services', JSON.stringify(services));
      console.log('Services mémorisés :', services);
    }
  }, [services]);

  const handleAddService = (service: Service) => {
    if (selectedService) {
      setServices((prevServices) =>
        prevServices.map((s) => (s.id === service.id ? service : s))
      );
    } else {
      setServices((prevServices) => [...prevServices, service]);
    }
    setSelectedService(null);
    setIsModalOpen(false);
  };

  const handleDelete = (id: number) => {
    const updatedServices = services.filter((service) => service.id !== id);
    setServices(updatedServices);
  };

  const handleEdit = (service: Service) => {
    setSelectedService(service);
    setIsModalOpen(true);
  };
  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Mes Services</h1>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {services.map((service) => (
          <div
            key={service.id}
            className="relative bg-white shadow-md rounded-md overflow-hidden"
          >
            <img
              src={service.imageUrl}
              alt={service.title}
              className="w-full h-48 object-cover"
            />
            <div className="p-4">
              <h2 className="text-lg font-semibold">{service.title}</h2>
              <p className="text-gray-500">{service.author}</p>
              <p className="text-gray-500">{service.description}</p>
            </div>
            <div className="absolute top-2 right-2 z-10">
              <div className="relative group">
                <button className="p-2 bg-white rounded-full shadow">
                  <MoreHorizontal className="w-5 h-5 text-gray-700" />
                </button>
                <div className="absolute right-0 mt-2 w-40 bg-white shadow-lg rounded-md hidden group-hover:block">
                  <button
                    className="w-full text-left px-4 py-2 hover:bg-gray-100"
                    onClick={() => handleEdit(service)}
                  >
                    Modifier
                  </button>
                  <button
                    className="w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600"
                    onClick={() => handleDelete(service.id)}
                  >
                    Supprimer
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {isModalOpen && (
        <CreateServiceModal
          onClose={() => setIsModalOpen(false)}
          onAddService={handleAddService}
          existingService={selectedService}
        />
      )}
    </div>
  );
};
export default TraveauxPro;

