import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { Mail, Lock } from "lucide-react";
import { ApiError, testApiConnection } from "../../services/api";
import authService from "../../services/authService";
import GoogleAuthService from "../../services/googleAuthService";
import GoogleAuthErrorNotification, { GoogleAuthError } from "./GoogleAuthErrorNotification";
import { API_BASE_URL } from "../../config";
import { useProfileWizard } from "../../context/ProfileWizardContext";
import { useToast } from "../../context/ToastContext";
import Button from "../ui/Button";
import FormInput from "../ui/FormInput";
import Checkbox from "../ui/Checkbox";

interface LoginFormProps {
  onToggleForm?: () => void;
  onSuccess?: () => void;
  onClose?: () => void;
  onOpenRegister?: () => void;
  onOpenForgotPassword?: () => void;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onToggleForm,
  onSuccess,
  onClose,
  onOpenRegister,
  onOpenForgotPassword,
}): React.ReactElement => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingGoogle, setIsLoadingGoogle] = useState(false);
  const [googleAuthError, setGoogleAuthError] = useState<GoogleAuthError | null>(null);
  const { openProfileWizard } = useProfileWizard();
  const { showToast } = useToast();
  const [formErrors, setFormErrors] = useState({
    email: "",
    password: "",
  });
  const [isMobile, setIsMobile] = useState(window.innerWidth < 767);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 766);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    if (searchParams.get("verified") === "true") {
      setSuccessMessage("Votre adresse e-mail a été vérifiée avec succès !");
    }

    // Gérer le callback Google OAuth
    handleGoogleCallback();
  }, [searchParams]);

  const handleGoogleCallback = async () => {
    const urlParams = new URLSearchParams(window.location.search);
    const googleAuth = urlParams.get('google_auth');

    if (googleAuth === 'success') {
      setIsLoadingGoogle(true);

      try {
        const token = urlParams.get('token');
        const userBase64 = urlParams.get('user');
        const message = urlParams.get('message');

        if (token && userBase64) {
          // Décoder les données utilisateur
          const user = JSON.parse(atob(userBase64));

          // Connexion réussie
          localStorage.setItem('token', token);
          localStorage.setItem('user', JSON.stringify(user));

          showToast("success", message || "Connexion réussie via Google");

          // Nettoyer les paramètres URL
          const url = new URL(window.location.href);
          url.searchParams.delete('google_auth');
          url.searchParams.delete('token');
          url.searchParams.delete('user');
          url.searchParams.delete('message');
          window.history.replaceState({}, document.title, url.toString());

          navigate("/dashboard");
          openProfileWizard();

          if (onSuccess) {
            onSuccess();
          }
        } else {
          throw new Error('Données d\'authentification manquantes');
        }
      } catch (error) {
        console.error('Erreur lors du traitement du callback Google:', error);
        setError('Erreur lors de l\'authentification Google');
        showToast("error", 'Erreur lors de l\'authentification Google');
      } finally {
        setIsLoadingGoogle(false);
      }
    } else if (googleAuth === 'error') {
      // Gestion des erreurs métier avec notification spécialisée
      const errorType = urlParams.get('error_type') as 'user_not_found' | 'profile_incomplete' | 'server_error' | 'unknown';
      const message = urlParams.get('message');
      const userExists = urlParams.get('user_exists') === 'true';
      const profileCompleted = urlParams.get('profile_completed') === 'true';

      setGoogleAuthError({
        error_type: errorType || 'unknown',
        message: message || 'Erreur lors de l\'authentification Google',
        user_exists: userExists,
        profile_completed: profileCompleted
      });

      // Nettoyer les paramètres URL
      const url = new URL(window.location.href);
      url.searchParams.delete('google_auth');
      url.searchParams.delete('error_type');
      url.searchParams.delete('message');
      url.searchParams.delete('user_exists');
      url.searchParams.delete('profile_completed');
      window.history.replaceState({}, document.title, url.toString());
    }
  };

  const validateForm = () => {
    let isValid = true;
    const errors = { email: "", password: "" };

    if (!email) {
      errors.email = "L'adresse email est obligatoire";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = "L'adresse email est invalide";
      isValid = false;
    }

    if (!password) {
      errors.password = "Le mot de passe est obligatoire";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleGoogleLogin = async () => {
    try {
      setIsLoadingGoogle(true);
      setError(''); // Réinitialiser les erreurs
      setGoogleAuthError(null); // Réinitialiser les erreurs Google

      await GoogleAuthService.initiateGoogleAuth();
    } catch (error) {
      console.error('Erreur lors de l\'initiation de l\'authentification Google:', error);
      setError('Erreur lors de l\'authentification Google. Veuillez réessayer.');
      showToast("error", 'Erreur lors de l\'authentification Google');
      setIsLoadingGoogle(false);
    }
  };

  const handleGoogleErrorClose = () => {
    setGoogleAuthError(null);
  };

  const handleGoToRegister = () => {
    setGoogleAuthError(null);
    if (onToggleForm) {
      onToggleForm();
    } else {
      navigate('/register');
    }
  };

  const handleGoToLogin = () => {
    setGoogleAuthError(null);
    // Déjà sur la page de connexion, juste fermer la notification
  };

  const handleRetryGoogleAuth = () => {
    setGoogleAuthError(null);
    handleGoogleLogin();
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsLoading(true);
    setError("");

    const isServerConnected = await testApiConnection();
    if (!isServerConnected) {
      const errorMessage =
        "Impossible de se connecter au serveur backend. Vérifiez que le serveur est en cours d'exécution à l'adresse " +
        API_BASE_URL;
      setError(errorMessage);
      showToast("error", errorMessage);
      setIsLoading(false);
      return;
    }

    try {
      const result = await authService.login({ email, password });

      if (result && typeof result === "object") {
        const typedResult = result as {
          user: any;
          token: string;
          message?: string;
        };

        if (typedResult.user && typedResult.token) {
          if (rememberMe) {
            localStorage.setItem("rememberedEmail", email);
          }

          showToast("success", "Connexion réussie !");
          navigate("/dashboard");
          openProfileWizard();

          if (onSuccess) {
            onSuccess();
          }
        } else if (typedResult.message) {
          setError(typedResult.message);
          showToast("error", typedResult.message);
        }
      }
    } catch (err) {
      let errorMessage = "Une erreur inconnue est survenue.";
      if ((err as ApiError).message) {
        const apiError = err as ApiError;
        errorMessage = apiError.message;

        if (apiError.status === 401) {
          errorMessage = "Email ou mot de passe incorrect";
        } else if (apiError.status === 403) {
          errorMessage =
            "Votre e-mail n'est pas vérifié. Veuillez vérifier votre boîte de réception.";
        }
      }
      setError(errorMessage);
      showToast("error", errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 p-4">
      <div
        className="bg-white rounded-lg overflow-hidden relative flex flex-col"
        style={{
          width: "100%",
          maxWidth: "920px",
          height: "auto",
          minHeight: "500px",
          maxHeight: "90vh",
          borderRadius: "30px",
        }}
      >
        {/* Bouton fermeture */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 md:top-6 md:right-6 z-10 flex items-center justify-center 
             transition-all duration-200"
          style={{
            backgroundColor: "rgba(0, 0, 0, 0.478)",
            color: "#FFFFFF",
            borderRadius: "10px",
            padding: "8px",
            boxShadow: "-10px 10px 30px rgba(0, 0, 0, 0.15)",
            border: "none",
            cursor: "pointer",
            width: "32px",
            height: "32px",
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = "rgba(0, 0, 0, 0.7)";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = "rgba(0, 0, 0, 0.478)";
          }}
          aria-label="Close modal"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="#FFFFFF"
          >
            <path
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
              stroke="currentColor"
              strokeWidth="0.5"
            />
          </svg>
        </button>

        {/* Conteneur scrollable */}
        <div className={`flex ${isMobile ? "flex-col" : "flex-row"} h-full overflow-y-auto`}>
          {/* Image en haut (mobile) */}
          {isMobile && (
            <div className="w-full h-40 bg-gray-100 flex-shrink-0">
              <img
                src="https://hi-3d.com/wp-content/uploads/2025/08/basel-lindenhofareal-miller-maranta-visualisierung-1.jpg"
                alt="Login visual"
                className="w-full h-full object-cover"
              />
            </div>
          )}

          {/* Formulaire */}
          <div className="w-full md:w-1/2 p-6 md:p-8 flex flex-col">
            <h2 className="text-2xl md:text-3xl mb-3 md:mb-4 text-center font-sans font-medium tracking-tight">
              LOG IN
            </h2>

            <p className="text-sm md:text-base mb-4 md:mb-6 text-center">
              Don't have an account?{" "}
              <button
                onClick={onOpenRegister || onToggleForm}
                className="text-blue-600 font-normal"
                // style={{ color: "#000000" }}
              >
                Sign Up
              </button>
            </p>

            <Button
              type="button"
              variant="outline"
              className="flex items-center justify-center gap-2 mb-4 md:mb-6 bg-[#F5F5F5] hover:bg-[#E0E0E0] py-2"
              style={{ borderRadius: "14px" }}
              disabled={isLoadingGoogle}
              aria-busy={isLoadingGoogle}
              onClick={handleGoogleLogin}
            >
              <img
                src="https://www.google.com/favicon.ico"
                alt="Google"
                className="w-4 h-4"
              />
              {isLoadingGoogle ? "Loading..." : "Continue with Google"}
              
            </Button>

            <div className="relative mb-4 md:mb-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-xs">
                <span className="px-2 bg-white text-gray-500">or</span>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="space-y-3 md:space-y-4">
              <FormInput
                type="email"
                id="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                icon={<Mail className="h-4 w-4 text-gray-400" />}
                error={formErrors.email}
                required
              />

              <FormInput
                type="password"
                id="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                icon={<Lock className="h-4 w-4 text-gray-400" />}
                error={formErrors.password}
                showPasswordToggle
                required
              />

              <div className="flex items-center justify-between text-xs md:text-sm">
                <Checkbox
                  id="remember-me"
                  label="Remember me"
                  checked={rememberMe}
                  onChange={() => setRememberMe(!rememberMe)}
                />

                <button
                  onClick={() => {
                    onClose?.();
                    onOpenForgotPassword?.();
                  }}
                  className="text-blue-600 hover:underline"
                >
                  Forgot password?
                </button>
              </div>

              <Button
                type="submit"
                variant="primary"
                className="w-full mt-3 md:mt-4 flex items-center justify-center gap-1 py-2 md:py-3"
                disabled={isLoading}
                aria-busy={isLoading}
              >
                {isLoading ? "Loading..." : <>Continue with email <span className="ml-1">&gt;</span></>}
              </Button>
            </form>

            <p className="text-xs md:text-sm text-gray-500 mt-3 mb-6 md:mb-8 text-center font-light">
              By signing up, you agree to our <br />
              <a href="/terms" className="text-gray-600 hover:underline font-normal">
                Terms of Service
              </a>{" "}
              &{" "}
              <a href="/privacy" className="text-gray-600 hover:underline font-normal">
                Privacy Policy
              </a>
            </p>
          </div>

          {/* Image à droite (desktop) */}
          {!isMobile && (
            <div className="w-1/2 bg-gray-100 flex-shrink-0">
              <img
                src="https://hi-3d.com/wp-content/uploads/2025/08/basel-lindenhofareal-miller-maranta-visualisierung-1.jpg"
                alt="Login visual"
                className="w-full h-full object-cover"
              />
            </div>
          )}
        </div>
      </div>

      {/* Notification d'erreur Google Auth */}
      {googleAuthError && (
        <GoogleAuthErrorNotification
          error={googleAuthError}
          onClose={handleGoogleErrorClose}
          onRetry={handleRetryGoogleAuth}
          onGoToRegister={handleGoToRegister}
          onGoToLogin={handleGoToLogin}
        />
      )}
    </div>
  );
};

export default LoginForm;
