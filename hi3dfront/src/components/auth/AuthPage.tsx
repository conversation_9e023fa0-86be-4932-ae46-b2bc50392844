import React, { useState } from "react";
import { useLocation } from "react-router-dom";
import Header from "../Header";
import Footer from "../Footer";
import LoginForm from "./LoginForm";
import RegisterForm from "./RegisterForm";
import ForgotPasswordForm from "./ForgotPasswordForm";
import ResetPasswordForm from "./ResetPasswordForm";

const AuthPage: React.FC = () => {
  const location = useLocation();
  const [isLogin, setIsLogin] = useState(location.pathname === "/login");

  const getAuthComponent = () => {
    const path = location.pathname;

    if (path === "/login" || path === "/register") {
      return isLogin ? (
        <LoginForm onToggleForm={() => setIsLogin(false)} />
      ) : (
        <RegisterForm onToggleForm={() => setIsLogin(true)} />
      );
    } else if (path === "/forgot-password") {
      return <ForgotPasswordForm />;
    } else if (path === "/reset-password") {
      return <ResetPasswordForm />;
    }

    // Default to login form
    return <LoginForm onToggleForm={() => setIsLogin(false)} />;
  };

  return (
    <>
      <Header />
      <div className="min-h-screen flex flex-col">
        <div className="flex-1 bg-gradient-to-br from-primary-50 to-secondary-50 py-12">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex flex-col md:flex-row items-center justify-between gap-12">
              {/* Left side - Branding and info */}
              <div className="w-full md:w-1/2 max-w-md mx-auto md:mx-0">
                <div className="text-center md:text-left">
                  <h1 className="text-3xl md:text-4xl font-bold text-primary-900 mb-4">
                    Bienvenue sur Hi 3D Artiste
                  </h1>
                  <p className="text-lg text-neutral-700 mb-6">
                    La plateforme qui connecte les professionnels de la 3D avec
                    des clients du monde entier.
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary-100 flex items-center justify-center">
                        <span className="text-primary-600 font-medium">1</span>
                      </div>
                      <p className="ml-3 text-neutral-600">
                        Créez votre profil en quelques minutes
                      </p>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary-100 flex items-center justify-center">
                        <span className="text-primary-600 font-medium">2</span>
                      </div>
                      <p className="ml-3 text-neutral-600">
                        Trouvez des professionnels qualifiés ou des projets
                        intéressants
                      </p>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-6 w-6 rounded-full bg-primary-100 flex items-center justify-center">
                        <span className="text-primary-600 font-medium">3</span>
                      </div>
                      <p className="ml-3 text-neutral-600">
                        Collaborez efficacement grâce à nos outils de
                        communication
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right side - Auth form */}
              <div className="w-full md:w-1/2 max-w-md mx-auto">
                <div className="bg-white rounded-xl shadow-xl p-8">
                  {getAuthComponent()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default AuthPage;
