import React from 'react';
import { AlertCircle, UserX, FileX } from 'lucide-react';

export interface GoogleAuthError {
  error_type: 'user_not_found' | 'profile_incomplete' | 'server_error' | 'unknown';
  message: string;
  user_exists?: boolean;
  profile_completed?: boolean;
}

interface GoogleAuthErrorNotificationProps {
  error: GoogleAuthError;
  onClose: () => void;
  onRetry?: () => void;
  onGoToRegister?: () => void;
  onGoToLogin?: () => void;
}

const GoogleAuthErrorNotification: React.FC<GoogleAuthErrorNotificationProps> = ({
  error,
  onClose,
  onRetry,
  onGoToRegister,
  onGoToLogin,
}) => {
  const getErrorIcon = () => {
    switch (error.error_type) {
      case 'user_not_found':
        return <UserX className="w-5 h-5 text-orange-500" />;
      case 'profile_incomplete':
        return <FileX className="w-5 h-5 text-yellow-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-red-500" />;
    }
  };

  const getErrorTitle = () => {
    switch (error.error_type) {
      case 'user_not_found':
        return 'Compte introuvable';
      case 'profile_incomplete':
        return 'Profil incomplet';
      case 'server_error':
        return 'Erreur serveur';
      default:
        return 'Erreur d\'authentification';
    }
  };

  const getErrorDescription = () => {
    switch (error.error_type) {
      case 'user_not_found':
        return 'Aucun compte n\'existe avec cette adresse email. Vous devez d\'abord créer un compte sur notre plateforme.';
      case 'profile_incomplete':
        return 'Votre compte existe mais votre inscription n\'est pas complète. Connectez-vous avec votre mot de passe pour terminer votre profil.';
      case 'server_error':
        return 'Une erreur technique s\'est produite. Veuillez réessayer dans quelques instants.';
      default:
        return error.message || 'Une erreur inattendue s\'est produite lors de l\'authentification Google.';
    }
  };

  const getActionButtons = () => {
    switch (error.error_type) {
      case 'user_not_found':
        return (
          <div className="flex gap-2 mt-4">
            {onGoToRegister && (
              <button
                onClick={onGoToRegister}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
              >
                Créer un compte
              </button>
            )}
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm font-medium"
            >
              Fermer
            </button>
          </div>
        );
      
      case 'profile_incomplete':
        return (
          <div className="flex gap-2 mt-4">
            {onGoToLogin && (
              <button
                onClick={onGoToLogin}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm font-medium"
              >
                Se connecter
              </button>
            )}
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm font-medium"
            >
              Fermer
            </button>
          </div>
        );
      
      case 'server_error':
        return (
          <div className="flex gap-2 mt-4">
            {onRetry && (
              <button
                onClick={onRetry}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm font-medium"
              >
                Réessayer
              </button>
            )}
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm font-medium"
            >
              Fermer
            </button>
          </div>
        );
      
      default:
        return (
          <div className="flex gap-2 mt-4">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm font-medium"
            >
              Fermer
            </button>
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div className="flex items-start gap-3">
          {getErrorIcon()}
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {getErrorTitle()}
            </h3>
            <p className="text-gray-600 text-sm leading-relaxed">
              {getErrorDescription()}
            </p>
            {getActionButtons()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GoogleAuthErrorNotification;
