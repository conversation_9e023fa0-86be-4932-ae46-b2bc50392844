/* Styles pour le composant LoginForm */

.login-form {
  width: 100%;
  max-width: 28rem;
  margin: 0 auto;
}

.login-form__header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-form__title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-neutral-900);
}

.login-form__subtitle {
  color: var(--color-neutral-600);
  margin-top: 0.5rem;
}

.login-form__alert {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 0.5rem;
  display: flex;
  align-items: flex-start;
}

.login-form__alert--success {
  background-color: var(--color-green-50);
  border: 1px solid var(--color-green-200);
}

.login-form__alert--error {
  background-color: var(--color-red-50);
  border: 1px solid var(--color-red-200);
}

.login-form__alert-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 0.5rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.login-form__alert-icon--success {
  color: var(--color-green-500);
}

.login-form__alert-icon--error {
  color: var(--color-red-500);
}

.login-form__alert-text {
  color: var(--color-green-700);
}

.login-form__alert-text--success {
  color: var(--color-green-700);
}

.login-form__alert-text--error {
  color: var(--color-red-700);
}

.login-form__form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.login-form__footer {
  margin-top: 2rem;
  text-align: center;
  font-size: 0.875rem;
  color: var(--color-neutral-600);
}

.login-form__link {
  font-weight: 500;
  color: var(--color-primary-600);
  text-decoration: none;
}

.login-form__link:hover {
  color: var(--color-primary-700);
}

.login-form__social-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.login-form__divider {
  margin: 1.5rem 0;
  display: flex;
  align-items: center;
  text-align: center;
  color: var(--color-neutral-500);
  font-size: 0.875rem;
}

.login-form__divider::before,
.login-form__divider::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid var(--color-neutral-200);
}

.login-form__divider::before {
  margin-right: 1rem;
}

.login-form__divider::after {
  margin-left: 1rem;
}

.login-form__remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
