import React, { useState } from 'react';
import { Search, Filter, Plus, Briefcase } from 'lucide-react';
import Button from '../ui/Button';
import OpenOfferCard from './OpenOfferCard';
import Badge from '../ui/Badge';

export interface OpenOffersListProps {
  offers: any[];
  isLoading?: boolean;
  error?: string;
  onOfferClick: (offerId: number) => void;
  onCreateOffer?: () => void;
  onFilterChange?: (filters: any) => void;
  onSearchChange?: (query: string) => void;
  isProfessional?: boolean;
  emptyMessage?: string;
}

const OpenOffersList: React.FC<OpenOffersListProps> = ({
  offers,
  isLoading = false,
  error,
  onOfferClick,
  onCreateOffer,
  onFilterChange,
  onSearchChange,
  isProfessional = false,
  emptyMessage = 'Aucune offre disponible',
}) => {
  // State for search and filters
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [skillFilter, setSkillFilter] = useState('all');
  const [showFilters, setShowFilters] = useState(false);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    if (onSearchChange) {
      onSearchChange(query);
    }
  };

  // Handle status filter change
  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStatusFilter(e.target.value);
  };

  // Handle skill filter change
  const handleSkillFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSkillFilter(e.target.value);
  };

  // Apply filters to offers
  const filteredOffers = offers.filter(offer => {
    // Apply search filter
    const matchesSearch =
      offer.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      offer.description.toLowerCase().includes(searchQuery.toLowerCase());

    // Apply status filter
    const matchesStatus = statusFilter === 'all' || offer.status === statusFilter;

    // Apply skill filter
    const matchesSkill = skillFilter === 'all' ||
      (offer.filters && offer.filters.skills && offer.filters.skills.includes(skillFilter));

    return matchesSearch && matchesStatus && matchesSkill;
  });

  // Get unique skills from offers
  const skillsArray = offers.flatMap(offer =>
    offer.filters && offer.filters.skills ? offer.filters.skills : []
  );
  const skills = Array.from(new Set(skillsArray));

  return (
    <div className="space-y-6">
      {/* Search and filters */}
      <div className="bg-white rounded-lg border border-neutral-200 shadow-sm overflow-hidden">
        <div className="p-4">
          <div className="flex flex-col md:flex-row md:items-center space-y-3 md:space-y-0 md:space-x-4">
            {/* Search input */}
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-neutral-400" />
              </div>
              <input
                type="text"
                placeholder="Rechercher une offre..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="block w-full pl-10 pr-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            {/* Filter button */}
            <Button
              variant="outline"
              leftIcon={<Filter className="h-5 w-5" />}
              onClick={() => setShowFilters(!showFilters)}
              className="md:w-auto"
            >
              Filtres
              {showFilters && <span className="ml-1 text-xs">▲</span>}
              {!showFilters && <span className="ml-1 text-xs">▼</span>}
            </Button>

            {/* Create offer button (for clients only) */}
            {onCreateOffer && !isProfessional && (
              <Button
                variant="primary"
                leftIcon={<Plus className="h-5 w-5" />}
                onClick={onCreateOffer}
                style={{
                  backgroundColor: '#2980b9',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  fontWeight: 'bold',
                  borderRadius: '0.5rem',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                }}
              >
                Créer une offre
              </Button>
            )}
          </div>

          {/* Expanded filters */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-neutral-200 grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Status filter */}
              <div>
                <label htmlFor="statusFilter" className="block text-sm font-medium text-neutral-700 mb-1">
                  Statut
                </label>
                <select
                  id="statusFilter"
                  value={statusFilter}
                  onChange={handleStatusFilterChange}
                  className="block w-full pl-3 pr-10 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">Tous les statuts</option>
                  <option value="pending">En attente</option>
                  <option value="open">Ouvert</option>
                  <option value="in_progress">En cours</option>
                  <option value="completed">Terminé</option>
                  <option value="closed">Fermé</option>
                  <option value="invited">Invité</option>
                </select>
              </div>

              {/* Skills filter */}
              <div>
                <label htmlFor="skillFilter" className="block text-sm font-medium text-neutral-700 mb-1">
                  Compétence
                </label>
                <select
                  id="skillFilter"
                  value={skillFilter}
                  onChange={handleSkillFilterChange}
                  className="block w-full pl-3 pr-10 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">Toutes les compétences</option>
                  {skills.map(skill => (
                    <option key={skill} value={skill}>
                      {skill}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Loading state */}
      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      )}

      {/* Error state */}
      {error && !isLoading && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-red-700">
          <p>{error}</p>
        </div>
      )}

      {/* Empty state */}
      {!isLoading && !error && filteredOffers.length === 0 && (
        <div className="bg-white rounded-lg border border-neutral-200 shadow-sm p-12 text-center">
          <div className="mx-auto w-16 h-16 bg-neutral-100 rounded-full flex items-center justify-center mb-4">
            <Briefcase className="h-8 w-8 text-neutral-400" />
          </div>
          <h3 className="text-lg font-medium text-neutral-700 mb-2">{emptyMessage}</h3>
          {onCreateOffer && !isProfessional && (
            <div className="mt-4">
              <Button
                variant="primary"
                onClick={onCreateOffer}
                style={{
                  backgroundColor: '#2980b9',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  fontWeight: 'bold',
                  borderRadius: '0.5rem',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
                }}
              >
                Créer une offre
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Offers list */}
      {!isLoading && !error && filteredOffers.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredOffers.map(offer => (
            <OpenOfferCard
              key={offer.id}
              offer={offer}
              onClick={() => onOfferClick(offer.id)}
              isProfessional={isProfessional}
            />
          ))}

          {/* Create offer card (for clients only) */}
          {onCreateOffer && !isProfessional && (
            <div
              className="bg-white rounded-lg border-2 border-dashed border-neutral-300 shadow-sm p-8 flex flex-col items-center justify-center cursor-pointer hover:border-primary-500 transition-colors"
              onClick={onCreateOffer}
            >
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mb-4">
                <Plus className="h-6 w-6 text-primary-600" />
              </div>
              <h3 className="text-lg font-medium text-neutral-700 mb-1">Créer une offre</h3>
              <p className="text-sm text-neutral-500 text-center">
                Publiez une nouvelle offre pour trouver des professionnels qualifiés
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default OpenOffersList;
