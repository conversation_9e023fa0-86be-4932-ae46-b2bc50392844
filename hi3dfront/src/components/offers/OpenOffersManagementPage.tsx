import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Plus, AlertCircle } from 'lucide-react';
import { API_BASE_URL } from '../../config';
import DashboardLayout from '../dashboard/DashboardLayout';
import OpenOffersList from './OpenOffersList';
import Button from '../ui/Button';
import Alert from '../ui/Alert';
// import OpenOfferModal, { OpenOfferMinimal } from '../ProjectRequestModal';
import OpenOfferModal from "../OpenOfferModal";

const OpenOffersManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [offers, setOffers] = useState<any[]>([]);
  const [selectedOffer, setSelectedOffer] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<string | null>(null);

  // Modal (ProjectRequestModal)
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [requestInitialData, setRequestInitialData] = useState<any | undefined>(undefined);

  // Vérifier les paramètres dans l'URL
  const searchParams = new URLSearchParams(window.location.search);
  const inviteProfessionalId = searchParams.get('invite');

  // Récupérer le token d'authentification
  const token = localStorage.getItem('token');
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const isProfessional = (user && (user.is_professional !== undefined ? !!user.is_professional : user.role === 'professional')) as boolean;
  console.log("isProfessional", isProfessional);
  console.log("user", user);
  console.log("token", token);
  console.log("id", id);

  // Utils: format/normalize API data
  const safeParseCategories = (categories: any) => {
    if (!categories) return [];
    if (Array.isArray(categories)) return categories;
    if (typeof categories === 'string') {
      try { return JSON.parse(categories); } catch { return []; }
    }
    return [];
  };
  const normalizeOffer = (offer: any) => ({
    id: offer.id,
    title: offer.title,
    description: offer.description,
    categories: safeParseCategories(offer.categories),
    budget: offer.budget,
    deadline: offer.deadline,
    company: offer.company,
    website: offer.website,
    recruitment_type: offer.recruitment_type || 'company',
    open_to_applications: offer.open_to_applications !== false,
    auto_invite: offer.auto_invite || false,
    status: offer.status,
    created_at: offer.created_at,
    updated_at: offer.updated_at,
    views_count: offer.views_count || 0,
    applications_count: offer.applications_count || 0,
    user_id: offer.user_id,
    client: offer.user ? {
      id: offer.user.id,
      name: `${offer.user.first_name} ${offer.user.last_name}`,
      avatar: offer.user.profile_picture_path,
    } : null,
    files: offer.files || [],
    filters: offer.filters || {
      languages: [],
      skills: [],
      location: '',
      experience_years: 0,
      availability_status: 'available',
    },
  });

  // Charger offres (robuste prod)
  useEffect(() => {
    const fetchOffers = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const endpoint = isProfessional
          ? `${API_BASE_URL}/api/open-offers`
          : `${API_BASE_URL}/api/client/open-offers`;

        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          let message = 'Erreur lors de la récupération des offres';
          try {
            const errData = await response.json();
            if (errData?.message) message = errData.message;
          } catch {}
          throw new Error(message);
        }

        const data = await response.json().catch(() => ({}));
        // Support de différents formats de payload backend
        const apiOffers =
          data.client_open_offers ||
          data.open_offers ||
          data.offers ||
          data.data ||
          [];

        const formattedOffers = Array.isArray(apiOffers)
          ? apiOffers.map((o: any) => normalizeOffer(o))
          : [];

        setOffers(formattedOffers);

        // Si un ID est fourni, sélectionner
        if (id) {
          const existing = formattedOffers.find((o: any) => String(o.id) === String(id));
          if (existing) {
            setSelectedOffer(existing);
          } else {
            // Récupération directe par ID
            try {
              const directRes = await fetch(`${API_BASE_URL}/api/open-offers/${id}`, {
                headers: { 'Authorization': `Bearer ${token}` },
              });
              if (directRes.ok) {
                const directData = await directRes.json().catch(() => ({}));
                const off = directData.open_offer || directData.offer || directData;
                if (off && off.id) setSelectedOffer(normalizeOffer(off));
              }
            } catch {}
          }
        }
      } catch (err: any) {
        console.error('Erreur:', err);
        setError(err?.message || 'Impossible de charger les offres');
        setOffers([]);
        setSelectedOffer(null);
      } finally {
        setIsLoading(false);
      }
    };
    fetchOffers();
  }, [token, id, isProfessional]);

  // Actions: ouvrir modal création
  const handleCreateOffer = () => {
    setRequestInitialData(undefined);
    setShowRequestModal(true);
  };

  // Actions: ouvrir modal édition
  const handleEditOffer = (offer: any) => {
    if (offer.user_id && user?.id && offer.user_id !== user.id) {
      setFormError("Vous n'êtes pas autorisé à modifier cette offre. Seul le propriétaire peut la modifier.");
      return;
    }
    setRequestInitialData({
      id: offer.id,
      title: offer.title,
      budget: offer.budget,
      deadline: offer.deadline,
      description: offer.description,
    });
    setShowRequestModal(true);
  };

  // Post-save handler
  const handleOfferSaved = (saved: any) => {
    setOffers(prev => {
      const exists = prev.some(o => String(o.id) === String(saved.id));
      if (exists) return prev.map(o => String(o.id) === String(saved.id) ? { ...o, ...saved } : o);
      const newObj = normalizeOffer(saved as any);
      return [...prev, newObj];
    });
    setShowRequestModal(false);
    if (!isProfessional && saved.id) {
      navigate(`/dashboard/client/offers/${saved.id}`);
    }
  };

  // Gérer le clic sur une offre (conserver la redirection existante)
  const handleOfferClick = (offerId: number) => {
    const u = JSON.parse(localStorage.getItem('user') || '{}');
    const isClient = (u && (u.is_professional !== undefined ? !u.is_professional : u.role === 'client')) as boolean;
    if (isClient) navigate(`/dashboard/client/offers/${offerId}`);
    else navigate(`/dashboard/open-offers/${offerId}`);
  };

  return (
    <DashboardLayout
      title={isProfessional ? "Offres disponibles" : "Mes offres ouvertes"}
      subtitle={isProfessional
        ? "Explorez les offres disponibles et trouvez votre prochaine opportunité"
        : "Gérez vos offres ouvertes et trouvez des professionnels qualifiés"}
      actions={
        !isProfessional && !selectedOffer ? (
          <Button
            variant="primary"
            leftIcon={<Plus className="h-5 w-5" />}
            onClick={handleCreateOffer}
            style={{
              backgroundColor: '#2980b9',
              color: 'white',
              padding: '0.75rem 1.5rem',
              fontWeight: 'bold',
              borderRadius: '0.5rem',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
            }}
          >
            Créer une offre
          </Button>
        ) : null
      }
    >
      {(error || formError) && (
        <Alert
          type="error"
          title="Erreur"
          onClose={() => {
            setError(null);
            setFormError(null);
          }}
          className="mb-6"
        >
          {error || formError}
        </Alert>
      )}

      {formSuccess && (
        <Alert
          type="success"
          title="Succès"
          onClose={() => setFormSuccess(null)}
          className="mb-6"
        >
          {formSuccess}
        </Alert>
      )}

      {selectedOffer ? (
        <div>
          {!isProfessional && (
            <div className="mb-4">
              <Button variant="primary" onClick={() => handleEditOffer(selectedOffer)}>Modifier cette offre</Button>
            </div>
          )}
        </div>
      ) : (
        <OpenOffersList
          offers={offers}
          isLoading={isLoading}
          error={error || undefined}
          onOfferClick={handleOfferClick}
          onCreateOffer={!isProfessional ? handleCreateOffer : undefined}
          isProfessional={isProfessional}
          emptyMessage={isProfessional
            ? 'Aucune offre disponible pour le moment'
            : "Vous n'avez pas encore créé d'offre"}
        />
      )}

      <OpenOfferModal
        open={showRequestModal}
        onClose={() => setShowRequestModal(false)}
        token={token}
        user={user}
        initialData={requestInitialData}
        onSaved={handleOfferSaved}
      />
    </DashboardLayout>
  );
};

export default OpenOffersManagementPage;
