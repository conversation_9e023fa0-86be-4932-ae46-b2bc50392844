import React from 'react';
import { useNavigate } from 'react-router-dom';

const MessageProfile = ({ application, offerDetail }: { application?: any; offerDetail?: any }) => {
  const navigate = useNavigate();
  
  // Données statiques pour les tests
  const staticProfessional = {
    id: 2,
    first_name: "<PERSON>",
    last_name: "<PERSON>",
    title: "Architecture Visualisation",
    avatar: "/default-avatar.png",
    city: "Paris",
    country: "France",
    languages: ["EN", "FR"],
    is_pro: true
  };
  
  const staticProject = {
    professional_name: "<PERSON> and <PERSON><PERSON> Render"
  };
  
  // Fonction pour obtenir l'URL de l'image
  const getImageUrl = (path: string) => {
    return path || 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
  };

  // Utiliser les données statiques
  const professional = staticProfessional;
  const project = staticProject;

  return (
    <div className="space-y-4 p-3 bg-[#F5F5F5]" style={{ maxHeight: '300px' }}>
      <div
        onClick={() => navigate(`/professionals/${professional?.id}`)}
        className="flex items-center cursor-pointer "
      >
        <img
          src={getImageUrl(professional?.avatar)}
          alt={`${professional?.first_name} ${professional?.last_name}`}
          className="w-28 h-28 rounded-full object-cover mr-3"
          onError={(e) => {
            e.currentTarget.onerror = null;
            e.currentTarget.src = 'https://images.unsplash.com/photo-1657921840978-c7bb981edd2b?q=80&w=1160&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D';
          }}
        />
        <div>
          
          <h4 
            className="text-sm font-semibold"
            style={{
              fontFamily: "'Inter', sans-serif",
              fontWeight: "500",
              fontSize: "16px",
              color: "#000000"
            }}
          >
            {project?.professional_name || "Jack and Moris Render"}
          </h4>
          <p
            style={{
              fontFamily: "'Inter', sans-serif",
              fontWeight: "500",
              fontSize: "16px",
              color: "#000000"
            }}
          >
            {professional?.title || 'Architecture visualisation'}
          </p>
        </div>
      </div>

      <div className="flex items-center text-sm text-gray-600">
        <span className="mr-3">
          <svg xmlns="http://www.w3.org/2000/svg" className="ionicon" viewBox="0 0 512 512" width="16" height="20">
            <circle cx="256" cy="192" r="32"></circle>
            <path d="M256 32c-88.22 0-160 68.65-160 153 0 40.17 18.31 93.59 54.42 158.78 29 52.34 62.55 99.67 80 123.22a31.75 31.75 0 0051.22 0c17.42-23.55 51-70.88 80-123.22C397.69 278.61 416 225.19 416 185c0-84.35-71.78-153-160-153zm0 224a64 64 0 1164-64 64.07 64.07 0 01-64 64z"></path>
          </svg>
        </span>
        <span
          style={{
            fontFamily: "'Inter', sans-serif",
            fontWeight: "400",
            fontSize: "14px",
            color: "#000000"
          }}
        >
          {professional?.city}, {professional?.country}
        </span>
      </div>

      <div className="flex items-center">
        <span
          className="bg-[#000000] text-white rounded-full px-1.5 py-0.5 inline-block mr-3"
          style={{
            fontSize: "8px",
            fontFamily: "'Inter', sans-serif",
            fontWeight: 300,
          }}
        >
          PRO
        </span>
        <span
          style={{
            fontFamily: "'Inter', sans-serif",
            fontWeight: "400",
            fontSize: "14px",
            color: "#000000"
          }}
        >
          Pro account
        </span>
      </div>

      <div className="flex items-center text-sm">
        <span className="mr-2 text-xl text-gray-700">
          <svg xmlns="http://www.w3.org/2000/svg" className="ionicon" viewBox="0 0 512 512" width="16" height="20">
            <path fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="32" d="M48 112h288M192 64v48M272 448l96-224 96 224M301.5 384h133M281.3 112S257 206 199 277 80 384 80 384"></path>
            <path d="M256 336s-35-27-72-75-56-85-56-85" fill="none" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="32"></path>
          </svg>
        </span>
        <ul className="flex space-x-2 list-none uppercase">
          {professional?.languages?.map((language, index) => (
            <li 
              key={index}
              style={{
                fontFamily: "'Inter', sans-serif",
                fontWeight: "400",
                fontSize: "14px",
                color: "#000000"
              }}
            >
              {language}
            </li>
          ))}
        </ul>
      </div>

      <button 
        className="h-[40px] w-[200px] bg-white py-2 rounded-full flex items-center justify-center hover:bg-gray-50 border border-gray-200"
        style={{
          fontFamily: "'Inter', sans-serif",
          fontWeight: "400",
          fontSize: "14px",
          color: "#ffffffff",
          background: "#000000"
        }}
      >
        
        Approuve his offer
      </button>
    </div>
  );
};

export default MessageProfile;