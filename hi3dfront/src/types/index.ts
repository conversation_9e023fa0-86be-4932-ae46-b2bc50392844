// Types globaux pour l'application

// Type pour l'utilisateur
export interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  is_professional: boolean;
  profile_completed?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Type pour les liens sociaux
export interface SocialLinks {
  linkedin?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  github?: string;
  dribbble?: string;
  behance?: string;
  website?: string;
}

// Type pour les préférences
export interface Preferences {
  notifications?: boolean;
  newsletter?: boolean;
  project_updates?: boolean;
  marketing_emails?: boolean;
}

// Type pour les éléments du portfolio
export interface PortfolioItem {
  id: string;
  path: string;
  name: string;
  description?: string;
  type: string;
  created_at: string;
}

// Type pour le profil professionnel
export interface ProfessionalProfile {
  id?: number;
  user_id?: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  bio?: string;
  avatar?: string;
  title?: string;
  profession?: string;
  expertise?: string[];
  years_of_experience?: number;
  hourly_rate?: number;
  description?: string;
  skills?: string[];
  portfolio?: PortfolioItem[];
  availability_status?: string;
  languages?: string[];
  services_offered?: string[];
  rating?: number;
  social_links?: SocialLinks;
  completion_percentage: number;
  created_at?: string;
  updated_at?: string;
  user?: User;
}

// Type pour le profil client
export interface ClientProfile {
  id?: number;
  user_id?: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  bio?: string;
  avatar?: string;
  type?: string;
  company_name?: string;
  industry?: string;
  company_size?: string;
  position?: string;
  website?: string;
  preferences?: Preferences;
  social_links?: SocialLinks;
  completion_percentage: number;
  created_at?: string;
  updated_at?: string;
  user?: User;
}

// Type pour l'expérience professionnelle
export interface Experience {
  id?: number;
  professional_profile_id?: number;
  title: string;
  company: string;
  start_date: string;
  end_date?: string;
  description?: string;
  created_at?: string;
  updated_at?: string;
  projects?: Project[];
}

// Type pour les réalisations
export interface Achievement {
  id?: number;
  professional_profile_id?: number;
  title: string;
  issuer: string;
  date: string;
  description?: string;
  certificate_url?: string;
  created_at?: string;
  updated_at?: string;
}

// Type pour les projets
export interface Project {
  id?: number;
  experience_id?: number;
  title: string;
  description?: string;
  technologies?: string[];
  url?: string;
  image?: string;
  created_at?: string;
  updated_at?: string;
}

// Type pour les projets du tableau de bord
export interface DashboardProject {
  id?: number;
  user_id?: number;
  title: string;
  description: string;
  category: string;
  budget: string;
  deadline: string;
  skills: string[];
  attachments?: Array<{
    name: string;
    path: string;
    type: string;
  }>;
  status: 'draft' | 'open' | 'in_progress' | 'completed' | 'cancelled';
  created_at?: string;
  updated_at?: string;
  client?: {
    id: number;
    name: string;
    avatar?: string;
  };
  professional?: {
    id: number;
    name: string;
    avatar?: string;
  };
}

// Type pour les offres ouvertes
export interface OpenOffer {
  id?: number;
  user_id?: number;
  title: string;
  description: string;
  category: string;
  budget: string;
  deadline: string;
  skills: string[];
  attachments?: Array<{
    name: string;
    path: string;
    type: string;
  }>;
  status: 'open' | 'closed' | 'completed' | 'cancelled';
  created_at?: string;
  updated_at?: string;
  client?: {
    id: number;
    name: string;
    avatar?: string;
  };
  applications_count?: number;
}

// Type pour les candidatures aux offres
export interface OfferApplication {
  id?: number;
  open_offer_id: number;
  user_id: number;
  cover_letter: string;
  proposed_budget?: string;
  proposed_deadline?: string;
  status: 'pending' | 'accepted' | 'rejected';
  created_at?: string;
  updated_at?: string;
  professional?: {
    id: number;
    first_name: string;
    last_name: string;
    avatar?: string;
    title?: string;
    rating?: number;
  };
}

// Type pour les messages
export interface Message {
  id?: number;
  open_offer_id: number;
  user_id: number;
  content: string;
  attachments?: Array<{
    name: string;
    path: string;
    type: string;
  }>;
  read_at?: string;
  created_at?: string;
  updated_at?: string;
  user?: {
    id: number;
    first_name: string;
    last_name: string;
    avatar?: string;
  };
}

// Type pour les activités
export interface Activity {
  id?: number;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  related_id?: number;
  related_type?: string;
  icon?: string;
}

// Type pour les statistiques du tableau de bord
export interface DashboardStats {
  activeProjects: number;
  earnings?: string;
  totalSpent?: string;
  rating?: number;
  completionRate?: number;
  connectedProfessionals?: number;
  projectsCompleted?: number;
}

// Type pour les données du tableau de bord
export interface DashboardData {
  user: User;
  stats: DashboardStats;
  projects: DashboardProject[];
  activities: Activity[];
  profile: ProfessionalProfile | ClientProfile;
  recommendedProfessionals?: ProfessionalProfile[];
}

// Type pour les erreurs API
export interface ApiError {
  status: number;
  message: string;
  errors?: Record<string, string[]>;
}

// Type pour la pagination
export interface Pagination {
  total: number;
  per_page: number;
  current_page: number;
  last_page: number;
  from: number;
  to: number;
}

// Type pour les réponses paginées
export interface PaginatedResponse<T> {
  data: T[];
  pagination: Pagination;
}

// Type pour les réponses API
export interface ApiResponse<T> {
  message?: string;
  data?: T;
  errors?: Record<string, string[]>;
}
