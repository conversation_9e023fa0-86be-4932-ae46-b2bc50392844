export type UserType = 'professional' | 'client';

export interface BaseProfile {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  phone: string;
}

export interface ProfessionalProfile extends BaseProfile {
  type: 'professional';
  profession: string;
  expertise: string[];
  experience: number;
  hourlyRate: number;
  description: string;
}

export interface ClientProfile extends BaseProfile {
  type: 'client';
  companyName?: string;
  industry?: string;
  isCompany: boolean;
  description: string;
}

export type UserProfile = ProfessionalProfile | ClientProfile;