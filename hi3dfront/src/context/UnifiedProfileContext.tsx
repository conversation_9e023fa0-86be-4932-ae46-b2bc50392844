import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { newProfileService, UnifiedProfileData } from '../services/newProfileService';
import { getAuthToken } from '../utils/auth';

interface ProfileContextType {
  profile: UnifiedProfileData | null;
  loading: boolean;
  error: string | null;
  completionPercentage: number;
  isProfileCompleted: boolean;
  updateProfile: (data: Record<string, any>) => Promise<UnifiedProfileData>;
  uploadAvatar: (file: File) => Promise<string>;
  uploadPortfolioItems: (files: File[]) => Promise<any[]>;
  deletePortfolioItem: (id: string) => Promise<any[]>;
  updateAvailability: (status: 'available' | 'unavailable' | 'busy') => Promise<string>;
  refreshProfile: () => Promise<void>;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export const UnifiedProfileProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [profile, setProfile] = useState<UnifiedProfileData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [completionPercentage, setCompletionPercentage] = useState<number>(0);
  const [isProfileCompleted, setIsProfileCompleted] = useState<boolean>(false);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  // Check if user is authenticated
  useEffect(() => {
    const token = getAuthToken();
    setIsAuthenticated(!!token);
  }, []);

  const fetchProfile = async () => {
    const token = getAuthToken();
    if (!token) {
      setProfile(null);
      setCompletionPercentage(0);
      setIsProfileCompleted(false);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { profile } = await newProfileService.getProfile();
      setProfile(profile);
      setCompletionPercentage(profile.completion_percentage);
      setIsProfileCompleted(profile.completion_percentage >= 70);
    } catch (err) {
      console.error('Error fetching profile:', err);
      setError('Failed to load profile data');
      setProfile(null);
      setCompletionPercentage(0);
      setIsProfileCompleted(false);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      fetchProfile();
    }
  }, [isAuthenticated]);

  const updateProfile = async (data: Record<string, any>) => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is authenticated
      const token = getAuthToken();
      if (!token) {
        setError('You must be logged in to update your profile');
        throw new Error('Authentication required');
      }

      const response = await newProfileService.updateProfile(data);
      setProfile(response.profile);
      setCompletionPercentage(response.profile.completion_percentage);
      setIsProfileCompleted(response.profile.completion_percentage >= 70);

      return response.profile;
    } catch (err) {
      console.error('Error updating profile:', err);
      setError('Failed to update profile');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const uploadAvatar = async (file: File) => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is authenticated
      const token = getAuthToken();
      if (!token) {
        setError('You must be logged in to upload an avatar');
        throw new Error('Authentication required');
      }

      const response = await newProfileService.uploadAvatar(file);

      // Refresh profile to get updated data
      await fetchProfile();

      return response.avatar_url;
    } catch (err) {
      console.error('Error uploading avatar:', err);
      setError('Failed to upload avatar');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const uploadPortfolioItems = async (files: File[]) => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is authenticated
      const token = getAuthToken();
      if (!token) {
        setError('You must be logged in to upload portfolio items');
        throw new Error('Authentication required');
      }

      const response = await newProfileService.uploadPortfolioItems(files);

      // Refresh profile to get updated data
      await fetchProfile();

      return response.portfolio;
    } catch (err) {
      console.error('Error uploading portfolio items:', err);
      setError('Failed to upload portfolio items');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const deletePortfolioItem = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is authenticated
      const token = getAuthToken();
      if (!token) {
        setError('You must be logged in to delete portfolio items');
        throw new Error('Authentication required');
      }

      const response = await newProfileService.deletePortfolioItem(id);

      // Refresh profile to get updated data
      await fetchProfile();

      return response.portfolio;
    } catch (err) {
      console.error('Error deleting portfolio item:', err);
      setError('Failed to delete portfolio item');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateAvailability = async (status: 'available' | 'unavailable' | 'busy') => {
    try {
      setLoading(true);
      setError(null);

      // Check if user is authenticated
      const token = getAuthToken();
      if (!token) {
        setError('You must be logged in to update availability');
        throw new Error('Authentication required');
      }

      const response = await newProfileService.updateAvailability(status);

      // Refresh profile to get updated data
      await fetchProfile();

      return response.availability_status;
    } catch (err) {
      console.error('Error updating availability:', err);
      setError('Failed to update availability');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const refreshProfile = async () => {
    // Check if user is authenticated
    const token = getAuthToken();
    if (token) {
      await fetchProfile();
    }
  };

  const value = {
    profile,
    loading,
    error,
    completionPercentage,
    isProfileCompleted,
    updateProfile,
    uploadAvatar,
    uploadPortfolioItems,
    deletePortfolioItem,
    updateAvailability,
    refreshProfile,
  };

  return (
    <ProfileContext.Provider value={value}>
      {children}
    </ProfileContext.Provider>
  );
};

export const useUnifiedProfile = () => {
  const context = useContext(ProfileContext);

  if (context === undefined) {
    throw new Error('useUnifiedProfile must be used within a UnifiedProfileProvider');
  }

  return context;
};
