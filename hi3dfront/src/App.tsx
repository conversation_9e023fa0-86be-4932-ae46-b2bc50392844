import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import HomePage from './components/HomePage';
import ContactPage from './components/ContactPage';
import OfferDetails from "./components/OfferDetails";
import ListeIndependant from './components/ListeIndependant';
import MessagingPage from './components/messaging/MessagingPage';
import MessagesPage from './components/messaging/MessagesPage';
import { AuthProvider } from './contexts/AuthContext';
import Dashboard from './components/dashboard/Dashboard';
import NotificationsPage from './components/notifications/NotificationsPage';
import { NotificationProvider } from './components/notifications/NotificationContext';
import ProjectManagementPage from './components/projects/ProjectManagementPage';
import ProjectApplicationForm from './components/projects/ProjectApplicationForm';
import AllProjectsPage from './components/projects/AllProjectsPage';
import ProjectDetailsPage from './components/projects/ProjectDetailsPage';
import ProfessionalProjectsPage from './components/projects/ProfessionalProjectsPage';
import OpenOffersManagementPage from './components/offers/OpenOffersManagementPage';
import OpenOfferDetailsPro from './components/offers/OpenOfferDetailsPro';
import ServiceOffersManagementPage from './components/services/ServiceOffersManagement';
import ServiceOffersExplore from './components/services/ServiceOffersExplore';
import ServiceOfferPublicDetails from './components/services/ServiceOfferPublicDetails';
import ServiceOfferCreatePage from './components/services/ServiceOfferCreatePage';
import ServiceOfferDetailPage from './components/services/ServiceOfferDetailPage';
import ClientServicesExplore from './components/services/ClientServicesExplore';
import ServiceReviewForm from './components/services/ServiceReviewForm';
import ServiceConversationsList from './components/services/ServiceConversationsList';
import ServiceConversation from './components/services/ServiceConversation';
import AchievementsManagement from './components/dashboard/AchievementsManagement';
import AllActivitiesPage from './components/activities/AllActivitiesPage';
import AllProfessionalsPage from './components/professionals/AllProfessionalsPage';
import ProfessionalDetails from './components/professionals/ProfessionalDetails';
import ExplorerPage from './components/explorer/ExplorerPage';
import InvoicesPage from './components/invoices/InvoicesPage';
import ProfileDashboard from './components/dashboard/ProfileDashboard';
import DashboardEditProfile from './components/dashboard/DashboardEditProfile';
import CreateProject from './components/dashboard/CreateProject';
import ProjectDetails from './components/dashboard/ProjectDetails';
import ClientProfilePage from './components/profile/ClientProfilePage';
import ClientProfileEditPage from './components/profile/ClientProfileEditPage';
// import ClientProfileDashboard from './components/dashboard/ClientProfileDashboard';
import ProfileClientDashboard from './components/dashboard/ProfileClientDashboard';
import ClientDashboardWrapper from './components/dashboard/ClientDashboardWrapper';
import PendingOffersPage from './components/projects/PendingOffersPage';
import ReceivedOffersPage from './components/projects/ReceivedOffersPage';
import OfferDiscussionPage from './components/projects/OfferDiscussionPage';
import ClientOfferDetailsPage from './components/projects/ClientOfferDetailsPage';
import OfferStatsPage from './components/projects/OfferStatsPage';
import DetailsProfilPro from './pages/DetailsProfilPro';

// Pages components
import AboutPage from './components/pages/AboutPage';
import OffersPage from './components/pages/OffersPage';
import ExplorePage from './components/pages/ExplorePage';
import DetailsSearch from './components/pages/DetailsSearch';

// Modern Profile Pages
import ProfilePage from './pages/ProfilePage';
import EditProfilePage from './pages/EditProfilePage';

// New Unified Profile Components
import UnifiedProfileView from './components/profile/UnifiedProfileView';
import UnifiedProfileEdit from './components/profile/UnifiedProfileEdit';

// Auth components
import AuthPage from './components/auth/AuthPage';
import SimpleLoginForm from './components/auth/SimpleLoginForm';

// Context providers
import { ProfileProvider } from "./components/ProfileContext";
import { ToastProvider } from './context/ToastContext';
import { ProfileWizardProvider } from './context/ProfileWizardContext';
import { UnifiedProfileProvider } from './context/UnifiedProfileContext';

// Accessibility components
import SkipToContent from './components/accessibility/SkipToContent';
import ErrorBoundary from './components/error/ErrorBoundary';
import EditPortfolio from './components/EditPortfolio';
import EditPortfolioPro from './components/EditPortfolioPro';
import ClientProfil from './components/ClientProfil';
// import StripeContainer from './components/stripe/StripeContainer';
import Favorite from './components/Favorite';
import Message from './components/Message';
import PrivateRoute from './components/PrivateRoute';
import ServiceDetail from "./pages/ServiceDetail";

import SearchGlobal from './components/SearchGlobal';

function App() {
  return (
    <ErrorBoundary>
      <ToastProvider>
        <AuthProvider>
          <ProfileWizardProvider>
            <ProfileProvider>
              <NotificationProvider>
                <Router>
                  <SkipToContent />
                  <div id="main-content">
                    <Routes>
              {/* Pages principales */}
              <Route path="/" element={<HomePage />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/offers" element={<OffersPage />} />
              <Route path="/explore" element={<ExplorePage />} />
              {/* <Route path="/details-search" element={<DetailsSearch />} /> */}
              <Route path="/details-search" element={<ServiceDetail />} />
              <Route path="/contact" element={<ContactPage />} />
              <Route path="/edit-portfolio" element={<EditPortfolioPro/>} />
              <Route path="/client-profil" element={<ClientProfil/>} />
              {/* Routes d'authentification */}
              <Route path="/register" element={<AuthPage />} />
              <Route path="/login" element={<AuthPage />} />
              <Route path="/simple-login" element={<SimpleLoginForm />} />
              <Route path="/forgot-password" element={<AuthPage />} />
              <Route path="/reset-password" element={<AuthPage />} />

              {/* Routes de profil */}
              <Route path="/profile" element={<ProfilePage />} />
              <Route path="/profile/edit" element={<EditProfilePage />} />

              {/* Routes de recherche */}
              <Route path="/search-global" element={<SearchGlobal />} />

              {/* Routes de projets et offres */}
              <Route path="/projects" element={<OpenOffersManagementPage />} />
              <Route path="/projects/:id" element={<OpenOffersManagementPage />} />
              <Route path="/projects/:id/apply" element={<ProjectApplicationForm />} />
              <Route path="/offre/:id" element={<OfferDetails />} />
              <Route path="/lists-independants" element={<ListeIndependant />} />

              {/* Routes de dashboard */}
              <Route path="/dashboard" element={
                <PrivateRoute>
                  <Dashboard />
                </PrivateRoute>
              } />
              <Route path="/dashboard/profile" element={<ProfileDashboard />} />
              <Route path="/dashboard/profile/edit" element={<DashboardEditProfile />} />
              <Route path="/dashboard/client-profile" element={<ClientProfilePage />} />
              <Route path="/dashboard/client-profile/edit" element={<ClientProfileEditPage />} />
              {/* <Route path="/dashboard/client-profile-dashboard" element={<ClientProfileDashboard />} /> */}
              <Route path="/dashboard/profile-client-dashboard" element={<ProfileClientDashboard />} />

              {/* New Unified Profile Routes */}
              <Route path="/dashboard/profile-new" element={<UnifiedProfileProvider><UnifiedProfileView /></UnifiedProfileProvider>} />
              <Route path="/dashboard/profile-new/edit" element={<UnifiedProfileProvider><UnifiedProfileEdit /></UnifiedProfileProvider>} />
              <Route path="/dashboard/create-project" element={<CreateProject />} />
              <Route path="/dashboard/create-project/:id" element={<CreateProject />} />
              <Route path="/dashboard/projects" element={<OpenOffersManagementPage />} />
              <Route path="/dashboard/project/:id" element={<ProjectDetails />} />
              <Route path="/dashboard/open-offers" element={<ProfessionalProjectsPage />} />
              <Route path="/dashboard/open-offers/:id" element={<OpenOffersManagementPage />} />
              <Route path="/dashboard/open-offers/:id/apply" element={<ProjectApplicationForm />} />
              <Route path="/dashboard/pending-offers" element={<PendingOffersPage />} />
              <Route path="/dashboard/received-offers" element={<ReceivedOffersPage />} />
              <Route path="/dashboard/publish-offer/:id" element={<PendingOffersPage />} />
              <Route path="/dashboard/offer/:id" element={<OfferDiscussionPage />} />
              <Route path="/dashboard/offers/:id" element={<Message />} />
              <Route path="/dashboard/client/offer/:id" element={<ClientOfferDetailsPage />} />
              <Route path="/dashboard/client/offers/:id" element={<Message />} />
              <Route path="/dashboard/offer-stats" element={<OfferStatsPage />} />

              {/* Routes de services */}
              <Route path="/services" element={<ServiceOffersExplore />} />
              <Route path="/services/:id" element={<ServiceOfferPublicDetails />} />
              <Route path="/services/:id/review" element={<ServiceReviewForm />} />
              <Route path="/dashboard/services/create" element={<ServiceOfferCreatePage />} />
              <Route path="/dashboard/services" element={<ServiceOffersManagementPage />} />
              <Route path="/dashboard/services/:id" element={<ServiceOfferDetailPage />} />
              <Route path="/dashboard/client/services" element={<ClientServicesExplore />} />
              <Route path="/dashboard/messages" element={<ServiceConversationsList />} />
              <Route path="/dashboard/messages/:id" element={<ServiceConversation />} />
              <Route path="/dashboard/achievements" element={<AchievementsManagement />} />
              <Route path="/dashboard/pro-offers/:id" element={<OpenOfferDetailsPro />} />

              {/* Routes de notifications et messages */}
              <Route path="/notifications" element={<NotificationsPage />} />
              <Route path="/discussions" element={<MessagingPage />} />
              {/* <Route path="/discussions/:id" element={<MessagingPage />} /> */}
              <Route path="/discussions/:id" element={<MessagesPage />} />
              <Route path="/message" element={<Message />} />

              {/* Autres routes */}
              <Route path="/favorite" element={<Favorite />} />
              <Route path="/all-projects" element={<AllProjectsPage />} />
              <Route path="/all-activities" element={<AllActivitiesPage />} />
              <Route path="/all-professionals" element={<AllProfessionalsPage />} />
              <Route path="/artist/:id" element={<ProfessionalDetails />} />
              <Route path="/professional/:id" element={<ProfessionalDetails />} />
              {/* <Route path="/professionals/:id" element={<DetailsProfilPro />} /> */}
              <Route path="/professionals/:id" element={<EditPortfolio />} />
              <Route path="/invoices" element={<InvoicesPage />} />
              <Route path="/explorer" element={<ExplorerPage />} />

              {/* Formulaire de paiement Stripe */}
              {/* <Route path="/stripe-payment"  element={<StripeContainer />} /> */}

              {/* Route par défaut */}
              <Route path="*" element={<Navigate to="/" replace />} />
                    </Routes>
                    </div>
                  </Router>
              </NotificationProvider>
            </ProfileProvider>
          </ProfileWizardProvider>
        </AuthProvider>
      </ToastProvider>
    </ErrorBoundary>
  );
}

export default App;