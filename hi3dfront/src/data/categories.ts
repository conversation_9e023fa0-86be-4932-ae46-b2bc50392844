/**
 * Fichier centralisé pour les catégories utilisées dans l'application
 * Cela permet d'avoir une source unique de vérité pour toutes les catégories
 */

// Structure d'une catégorie
export interface Category {
  id: number;
  value: string;
  label: string;
  description?: string;
  parent?: string;
  image_url?: string;
  count?: number;
}

// Catégories principales
export const MAIN_CATEGORIES: Category[] = [
  { id: 1, value: 'modeling', label: 'Modélisation 3D', description: 'Création de modèles 3D détaillés et optimisés' },
  { id: 2, value: 'animation', label: 'Animation', description: 'Animation de personnages et d\'objets' },
  { id: 3, value: 'architectural', label: 'Architecture 3D', description: 'Modélisation et visualisation architecturale' },
  { id: 4, value: 'product', label: 'Produit 3D', description: 'Modélisation de produits pour e-commerce et marketing' },
  { id: 5, value: 'character', label: 'Personnage 3D', description: 'Création et animation de personnages' },
  { id: 6, value: 'environment', label: 'Environnement 3D', description: 'Création d\'environnements et de paysages' },
  { id: 7, value: 'vr_ar', label: 'Réalité virtuelle/augmentée', description: 'Expériences VR et AR immersives' },
  { id: 8, value: 'game_art', label: 'Game Art', description: 'Assets et environnements pour jeux vidéo' },
];

// Sous-catégories d'Architecture 3D
export const ARCHITECTURE_3D_CATEGORIES: Category[] = [
  // Modélisation architecturale
  { id: 101, value: 'arch_residential', label: 'Modélisation de bâtiments résidentiels', parent: 'architectural' },
  { id: 102, value: 'arch_commercial', label: 'Modélisation de bâtiments commerciaux', parent: 'architectural' },
  { id: 103, value: 'arch_historical', label: 'Modélisation de bâtiments historiques', parent: 'architectural' },
  { id: 104, value: 'arch_urban', label: 'Modélisation urbaine', parent: 'architectural' },
  { id: 105, value: 'arch_landscape', label: 'Modélisation de paysages', parent: 'architectural' },
  
  // Visualisation architecturale
  { id: 201, value: 'viz_exterior', label: 'Rendus extérieurs', parent: 'architectural' },
  { id: 202, value: 'viz_interior', label: 'Rendus intérieurs', parent: 'architectural' },
  { id: 203, value: 'viz_aerial', label: 'Rendus aériens', parent: 'architectural' },
  { id: 204, value: 'viz_panoramic', label: 'Rendus panoramiques 360°', parent: 'architectural' },
  { id: 205, value: 'viz_floorplan', label: 'Plans d\'étage 3D', parent: 'architectural' },
  
  // Animation architecturale
  { id: 301, value: 'anim_virtual_tour', label: 'Visites virtuelles', parent: 'architectural' },
  { id: 302, value: 'anim_construction', label: 'Animations de construction', parent: 'architectural' },
  { id: 303, value: 'anim_process', label: 'Animations de processus', parent: 'architectural' },
  { id: 304, value: 'anim_presentation', label: 'Animations de présentation', parent: 'architectural' },
  
  // BIM (Building Information Modeling)
  { id: 401, value: 'bim_modeling', label: 'Modélisation BIM', parent: 'architectural' },
  { id: 402, value: 'bim_coordination', label: 'Coordination BIM', parent: 'architectural' },
  { id: 403, value: 'bim_documentation', label: 'Documentation BIM', parent: 'architectural' },
  { id: 404, value: 'bim_analysis', label: 'Analyse BIM', parent: 'architectural' },
  
  // Design d'intérieur
  { id: 501, value: 'interior_furniture', label: 'Modélisation de meubles', parent: 'architectural' },
  { id: 502, value: 'interior_space', label: 'Aménagement d\'espaces', parent: 'architectural' },
  { id: 503, value: 'interior_lighting', label: 'Éclairage intérieur', parent: 'architectural' },
  { id: 504, value: 'interior_decoration', label: 'Décoration d\'intérieur', parent: 'architectural' },
  
  // Paysagisme 3D
  { id: 601, value: 'landscape_garden', label: 'Modélisation de jardins', parent: 'architectural' },
  { id: 602, value: 'landscape_urban', label: 'Modélisation d\'espaces urbains', parent: 'architectural' },
  { id: 603, value: 'landscape_natural', label: 'Modélisation d\'environnements naturels', parent: 'architectural' },
  { id: 604, value: 'landscape_planning', label: 'Aménagement paysager', parent: 'architectural' },
  
  // Réalité virtuelle/augmentée pour l'architecture
  { id: 701, value: 'vr_arch_experience', label: 'Expériences VR architecturales', parent: 'architectural' },
  { id: 702, value: 'ar_arch_app', label: 'Applications AR pour l\'architecture', parent: 'architectural' },
  { id: 703, value: 'vr_space_configurator', label: 'Configurateurs d\'espaces interactifs', parent: 'architectural' },
  { id: 704, value: 'vr_immersive_tour', label: 'Visites immersives', parent: 'architectural' },
  
  // Impression 3D architecturale
  { id: 801, value: '3dprint_arch_model', label: 'Maquettes architecturales', parent: 'architectural' },
  { id: 802, value: '3dprint_prototype', label: 'Prototypes de conception', parent: 'architectural' },
  { id: 803, value: '3dprint_decorative', label: 'Éléments décoratifs', parent: 'architectural' },
  { id: 804, value: '3dprint_scale_model', label: 'Modèles à l\'échelle', parent: 'architectural' },
];

// Fonction utilitaire pour obtenir toutes les catégories
export const getAllCategories = (): Category[] => {
  return [...MAIN_CATEGORIES, ...ARCHITECTURE_3D_CATEGORIES];
};

// Fonction utilitaire pour obtenir les catégories par parent
export const getCategoriesByParent = (parentValue: string): Category[] => {
  return ARCHITECTURE_3D_CATEGORIES.filter(category => category.parent === parentValue);
};

// Fonction utilitaire pour convertir les catégories en options pour les composants de formulaire
export const getCategoryOptions = (categories: Category[] = MAIN_CATEGORIES) => {
  return categories.map(category => ({
    value: category.value,
    label: category.label,
  }));
};

// Fonction utilitaire pour obtenir une catégorie par sa valeur
export const getCategoryByValue = (value: string): Category | undefined => {
  return getAllCategories().find(category => category.value === value);
};

// Fonction utilitaire pour obtenir une catégorie par son ID
export const getCategoryById = (id: number): Category | undefined => {
  return getAllCategories().find(category => category.id === id);
};
