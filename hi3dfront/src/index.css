@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import './styles/global.css';

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}


@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

:root {
  /* Primary colors */
  --color-primary-50: 235, 245, 255;
  --color-primary-100: 214, 235, 255;
  --color-primary-200: 173, 214, 255;
  --color-primary-300: 133, 194, 255;
  --color-primary-400: 92, 173, 255;
  --color-primary-500: 51, 153, 255;
  --color-primary-600: 41, 128, 228;
  --color-primary-700: 31, 102, 204;
  --color-primary-800: 21, 77, 166;
  --color-primary-900: 12, 51, 128;

  /* Secondary colors */
  --color-secondary-50: 240, 253, 244;
  --color-secondary-100: 220, 252, 231;
  --color-secondary-200: 187, 247, 208;
  --color-secondary-300: 134, 239, 172;
  --color-secondary-400: 74, 222, 128;
  --color-secondary-500: 34, 197, 94;
  --color-secondary-600: 22, 163, 74;
  --color-secondary-700: 21, 128, 61;
  --color-secondary-800: 22, 101, 52;
  --color-secondary-900: 20, 83, 45;

  /* Neutral colors */
  --color-neutral-50: 250, 250, 250;
  --color-neutral-100: 245, 245, 245;
  --color-neutral-200: 229, 229, 229;
  --color-neutral-300: 212, 212, 212;
  --color-neutral-400: 163, 163, 163;
  --color-neutral-500: 115, 115, 115;
  --color-neutral-600: 82, 82, 82;
  --color-neutral-700: 64, 64, 64;
  --color-neutral-800: 38, 38, 38;
  --color-neutral-900: 23, 23, 23;

  /* Status colors */
  --color-success: 34, 197, 94;
  --color-warning: 245, 158, 11;
  --color-error: 239, 68, 68;
  --color-info: 59, 130, 246;
}

@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply font-sans text-neutral-800 bg-white m-0;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold;
  }

  h1 {
    @apply text-4xl md:text-5xl;
  }

  h2 {
    @apply text-3xl md:text-4xl;
  }

  h3 {
    @apply text-2xl md:text-3xl;
  }

  h4 {
    @apply text-xl md:text-2xl;
  }

  h5 {
    @apply text-lg md:text-xl;
  }

  h6 {
    @apply text-base md:text-lg;
  }

  a {
    @apply text-primary-600 hover:text-primary-700 transition-colors;
  }
}

@layer components {
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-full font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-black hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }

  .btn-outline {
    @apply btn border-primary-600 text-primary-600 hover:bg-primary-50 focus:ring-primary-500;
  }

  .btn-ghost {
    @apply btn bg-transparent hover:bg-neutral-100 text-neutral-700 focus:ring-neutral-500;
  }

  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow;
  }

  .form-input {
    @apply block w-full px-3 py-2 border border-neutral-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500;
  }

  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }

  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800;
  }

  .badge-neutral {
    @apply badge bg-neutral-100 text-neutral-800;
  }

  /* Bouton avec fond blanc */
  .btn-white {
    @apply btn bg-white text-black hover:bg-neutral-50 focus:ring-primary-500 border border-neutral-200 shadow-sm;
  }

  /* Ombre de texte pour améliorer la visibilité */
  .text-shadow-sm {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
}