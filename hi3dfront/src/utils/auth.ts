/**
 * Get the authentication token from local storage
 * @returns The authentication token or null if not found
 */
export function getAuthToken(): string | null {
  return localStorage.getItem('token');
}

/**
 * Set the authentication token in local storage
 * @param token The token to store
 */
export function setAuthToken(token: string): void {
  localStorage.setItem('token', token);
}

/**
 * Remove the authentication token from local storage
 */
export function removeAuthToken(): void {
  localStorage.removeItem('token');
}

/**
 * Check if the user is authenticated (has a token)
 * @returns True if authenticated, false otherwise
 */
export function isAuthenticated(): boolean {
  return !!getAuthToken();
}

/**
 * Parse JWT token to get user information
 * @param token JWT token
 * @returns Decoded token payload or null if invalid
 */
export function parseToken(token: string): any {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (e) {
    console.error('Error parsing token:', e);
    return null;
  }
}
