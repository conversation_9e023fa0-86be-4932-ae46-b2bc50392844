// Script de test pour vérifier le format des données d'inscription
console.log('🔍 Test du format des données d\'inscription\n');

// Simulation des données envoyées par le frontend
const testPayloads = [
  {
    name: 'Client Registration',
    payload: {
      first_name: '<PERSON>',
      last_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      password: 'Password123!',
      password_confirmation: 'Password123!',
      is_professional: false,
    }
  },
  {
    name: 'Professional Registration',
    payload: {
      first_name: '<PERSON>',
      last_name: '<PERSON>',
      email: '<EMAIL>',
      password: 'Password123!',
      password_confirmation: 'Password123!',
      is_professional: true,
    }
  }
];

console.log('📋 Formats de données testés:\n');

testPayloads.forEach((test, index) => {
  console.log(`${index + 1}. ${test.name}:`);
  console.log('   Payload JSON:');
  console.log('   ' + JSON.stringify(test.payload, null, 2).replace(/\n/g, '\n   '));
  
  // Vérifications
  const checks = [
    { name: 'first_name présent', valid: !!test.payload.first_name },
    { name: 'last_name présent', valid: !!test.payload.last_name },
    { name: 'email présent', valid: !!test.payload.email },
    { name: 'password présent', valid: !!test.payload.password },
    { name: 'password_confirmation présent', valid: !!test.payload.password_confirmation },
    { name: 'is_professional défini', valid: typeof test.payload.is_professional === 'boolean' },
    { name: 'mots de passe identiques', valid: test.payload.password === test.payload.password_confirmation },
    { name: 'email valide', valid: /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(test.payload.email) },
  ];
  
  console.log('   Vérifications:');
  checks.forEach(check => {
    const status = check.valid ? '✅' : '❌';
    console.log(`   ${status} ${check.name}`);
  });
  
  console.log('');
});

console.log('🚨 Problèmes potentiels côté backend:');
console.log('1. Validation des champs manquante ou incorrecte');
console.log('2. Contraintes de base de données (email unique, etc.)');
console.log('3. Problème de configuration de la base de données');
console.log('4. Erreur dans le contrôleur d\'inscription Laravel');
console.log('5. Problème de hachage du mot de passe');
console.log('6. Problème de validation des règles métier');

console.log('\n💡 Actions recommandées:');
console.log('1. Vérifier les logs du serveur Laravel');
console.log('2. Vérifier la configuration de la base de données');
console.log('3. Tester l\'inscription avec des données simples');
console.log('4. Vérifier les migrations de base de données');
console.log('5. Vérifier les règles de validation dans le contrôleur');

console.log('\n🔧 Test de requête cURL équivalente:');
const curlCommand = `curl -X POST https://dev-backend.hi-3d.com/api/register \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -d '${JSON.stringify(testPayloads[0].payload)}'`;

console.log(curlCommand);
