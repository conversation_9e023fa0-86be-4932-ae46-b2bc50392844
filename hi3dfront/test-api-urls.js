// Script de test pour vérifier les URLs d'API générées
const API_BASE_URL = 'https://dev-backend.hi-3d.com';

// Fonction de test pour simuler la logique de construction d'URL
function testUrlConstruction() {
  console.log('🔍 Test de construction des URLs d\'API\n');
  
  // Test de la fonction fetchApi (src/services/api.ts)
  function testFetchApi(endpoint) {
    const apiEndpoint = endpoint.startsWith('/api') ? endpoint : `/api${endpoint}`;
    const url = `${API_BASE_URL}${apiEndpoint}`;
    return url;
  }
  
  // Test de la fonction fetchApi utilitaire (src/utils/fetchApi.ts)
  function testUtilsFetchApi(endpoint) {
    const apiEndpoint = endpoint.startsWith('/api') ? endpoint : `/api${endpoint}`;
    const url = `${API_BASE_URL}${apiEndpoint}`;
    return url;
  }
  
  // Test des services
  function testService(endpoint) {
    const apiEndpoint = endpoint.startsWith('/api') ? endpoint : `/api${endpoint}`;
    const url = `${API_BASE_URL}${apiEndpoint}`;
    return url;
  }
  
  // Tests avec différents endpoints
  const testCases = [
    { name: 'Register (correct)', endpoint: '/register', expected: 'https://dev-backend.hi-3d.com/api/register' },
    { name: 'Login (correct)', endpoint: '/login', expected: 'https://dev-backend.hi-3d.com/api/login' },
    { name: 'Ping (correct)', endpoint: '/ping', expected: 'https://dev-backend.hi-3d.com/api/ping' },
    { name: 'Register (avec /api)', endpoint: '/api/register', expected: 'https://dev-backend.hi-3d.com/api/register' },
    { name: 'Login (avec /api)', endpoint: '/api/login', expected: 'https://dev-backend.hi-3d.com/api/login' },
  ];
  
  console.log('📋 Résultats des tests:\n');
  
  testCases.forEach(testCase => {
    const result = testFetchApi(testCase.endpoint);
    const isCorrect = result === testCase.expected;
    const status = isCorrect ? '✅' : '❌';
    
    console.log(`${status} ${testCase.name}:`);
    console.log(`   Endpoint: ${testCase.endpoint}`);
    console.log(`   Résultat: ${result}`);
    console.log(`   Attendu:  ${testCase.expected}`);
    console.log(`   Correct:  ${isCorrect}\n`);
  });
  
  // Test spécifique pour le problème original
  console.log('🚨 Test du problème original:\n');
  
  // Avant correction (problématique)
  const problematicUrl = `${API_BASE_URL}`;
  console.log(`❌ URL problématique (avant): ${problematicUrl}`);
  console.log(`   → Cause erreur 405 Method Not Allowed\n`);
  
  // Après correction
  const fixedUrl = testFetchApi('/ping');
  console.log(`✅ URL corrigée (après): ${fixedUrl}`);
  console.log(`   → Devrait fonctionner correctement\n`);
  
  console.log('📝 Résumé des corrections effectuées:');
  console.log('1. src/services/api.ts - fonction ping()');
  console.log('2. src/services/api.ts - fonction testApiConnection()');
  console.log('3. src/utils/fetchApi.ts - fonction fetchApi()');
  console.log('4. src/services/exploreService.ts - fonction fetchApi()');
  console.log('5. src/services/profileService.ts - fonction fetchApi()');
  console.log('6. src/services/dashboardService.ts - fonction fetchApi()');
}

// Exécuter les tests
testUrlConstruction();
