const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Vérification des erreurs TypeScript...');

try {
  // Exécuter la commande tsc pour vérifier les erreurs TypeScript
  const output = execSync('npx tsc --noEmit', { encoding: 'utf8' });
  console.log('Aucune erreur TypeScript trouvée !');
} catch (error) {
  console.error('Erreurs TypeScript trouvées :');
  console.error(error.stdout);
}

console.log('\nVérification terminée.');
