/**
 * Script de test simple pour vérifier que le frontend fonctionne correctement
 */

const fs = require('fs');
const path = require('path');

// Vérifier que Node.js est installé et fonctionne
console.log(`Node.js version: ${process.version}`);

// Vérifier que l'application React est accessible
const basePath = __dirname;
console.log(`React application found at: ${basePath}`);

// Vérifier que le fichier package.json existe
if (fs.existsSync(path.join(basePath, 'package.json'))) {
    console.log('package.json found');

    // Lire le contenu du fichier package.json
    const packageJson = JSON.parse(fs.readFileSync(path.join(basePath, 'package.json'), 'utf8'));
    console.log(`Application name: ${packageJson.name}`);
    console.log(`Application version: ${packageJson.version}`);
    console.log(`React version: ${packageJson.dependencies.react}`);
} else {
    console.log('Error: package.json not found');
}

// Vérifier que les dossiers importants existent
const directories = [
    'src',
    'public',
    'node_modules',
];

directories.forEach(directory => {
    if (fs.existsSync(path.join(basePath, directory))) {
        console.log(`Directory found: ${directory}`);
    } else {
        console.log(`Error: Directory not found: ${directory}`);
    }
});

// Vérifier que les fichiers importants existent
const files = [
    'src/App.tsx',
    'src/index.tsx',
    'src/index.css',
    'src/react-app-env.d.ts',
    'public/index.html',
];

files.forEach(file => {
    if (fs.existsSync(path.join(basePath, file))) {
        console.log(`File found: ${file}`);
    } else {
        console.log(`Error: File not found: ${file}`);
    }
});

// Vérifier que les composants importants existent
const components = [
    'src/components/dashboard/Dashboard.tsx',
    'src/components/dashboard/ProfileDashboard.tsx',
    'src/components/dashboard/DashboardEditProfile.tsx',
    'src/components/dashboard/CreateProject.tsx',
    'src/components/profile/ClientProfilePage.tsx',
    'src/components/profile/ClientProfileEditPage.tsx',
    'src/pages/ProfilePage.tsx',
    'src/pages/EditProfilePage.tsx',
];

components.forEach(component => {
    if (fs.existsSync(path.join(basePath, component))) {
        console.log(`Component found: ${component}`);
    } else {
        console.log(`Error: Component not found: ${component}`);
    }
});

// Vérifier que les services API existent
const services = [
    'src/services/profileService.ts',
    'src/services/dashboardService.ts',
];

services.forEach(service => {
    if (fs.existsSync(path.join(basePath, service))) {
        console.log(`Service found: ${service}`);
    } else {
        console.log(`Error: Service not found: ${service}`);
    }
});

console.log('\nFrontend check completed.');
